<template>
  <el-form
    status-icon
    :rules="rules"
    :model="form"
    ref="ruleForm"
    label-width="0"
    class="ruleForm"
  >
    <div class="step-two" v-if="actived == 2">
      <template v-if="concatList.length">
        <el-table
          :data="concatList"
          class="sqlIndicator-table"
          style="width: calc(100% - 48px); margin: 24px 24px 0"
        >
          <el-table-column prop="fieldName" label="字段">
            <template #default="{ row }">
              {{ row.fieldName }}
            </template>
          </el-table-column>
          <el-table-column prop="tagType" label="标记类型">
            <template #default="{ row }">
              <el-select
                v-model="row.tagType"
                placeholder="请选择"
                :disabled="row.groupName === 'old'"
                @change="changeDimType($event, row)"
              >
                <el-option
                  v-for="item in tagOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>

          <el-table-column prop="definitionCode" label="对应维度">
            <template #default="{ row }">
              <template
                v-if="
                  row.groupName === 'old' &&
                  ['维度', '度量和维度', '时间维度'].includes(row.tagType)
                "
              >
                {{ row.dimensionName }}({{ row.version }})
              </template>
              <template v-else>
                <el-popover
                  placement="right"
                  width="250"
                  trigger="hover"
                  @after-leave="resetNoCoverShowCount(row)"
                  v-if="
                    row.noCoverValues.length &&
                    ['维度', '度量和维度', '时间维度'].includes(row.tagType)
                  "
                >
                  <div>
                    <div class="popover-title">无法覆盖以下字段：</div>
                    <div class="popover-content">
                      <div
                        v-for="field in row.noCoverValues.slice(
                          0,
                          row.noCoverShowCount || 5
                        )"
                        :key="field"
                      >
                        {{ field }}
                      </div>
                      <el-button
                        v-if="
                          row.noCoverValues.length > (row.noCoverShowCount || 5)
                        "
                        type="text"
                        @click.stop="loadMoreNoCover(row)"
                      >
                        继续查看
                      </el-button>
                    </div>
                  </div>
                  <i
                    class="el-icon-warning"
                    style="
                      margin-right: 5px;
                      font-size: 20px;
                      color: #e6a23c;
                      cursor: pointer;
                    "
                    slot="reference"
                  ></i>
                </el-popover>
                <el-select-v2
                  v-if="
                    (row.tagType == '维度' || row.tagType == '度量和维度') &&
                    !row.newCreate
                  "
                  v-model="row.definitionCode"
                  :options="allDimTreeList"
                  :props="{
                    label: 'dimName',
                    value: 'definitionCode'
                  }"
                  filterable
                  clearable
                  placeholder="请选择"
                  @change="getDimLevels($event, row)"
                >
                  <template #default="{ item }">
                    <p
                      style="
                        padding: 0 17px;
                        display: flex;
                        justify-content: space-between;
                      "
                    >
                      <span>{{ item.dimName }}</span>
                      <span>{{ item.version }}</span>
                    </p>
                  </template>
                </el-select-v2>
                <span
                  v-if="
                    row.newCreate &&
                    (row.tagType == '维度' || row.tagType == '度量和维度')
                  "
                  style="display: flex; align-items: center"
                >
                  <span>{{ row.dimName }}({{ row.version }})</span>
                  <el-button type="text" @click="removeDim(row)">
                    移除
                  </el-button>
                  <el-button type="text" @click="editDim(row)">编辑</el-button>
                </span>
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="levelCode" label="维度层级">
            <template #default="{ row }">
              <span
                v-if="
                  row.groupName === 'old' &&
                  (row.tagType == '维度' || row.tagType == '度量和维度') &&
                  row.definitionCode != 'self'
                "
              >
                {{ row.levelName }}
              </span>
              <template v-else>
                <el-select
                  v-model="row.levelCode"
                  :disabled="row.groupName === 'old'"
                  placeholder="请选择"
                  @change="onLevelCodeChange($event, row)"
                  v-if="
                    (row.tagType == '维度' || row.tagType == '度量和维度') &&
                    row.definitionCode != 'self'
                  "
                >
                  <el-option
                    v-for="item in dimLevelMap[row.definitionCode]"
                    :key="item.levelCode"
                    :label="item.levelName"
                    :value="item.levelCode"
                  ></el-option>
                </el-select>
              </template>
            </template>
          </el-table-column>
        </el-table>
      </template>

      <div class="step-btn">
        <el-button @click="$router.push('/ddsBi/indicatorAnagement')">
          取消
        </el-button>
        <el-button size="small" @click="$emit('backStop')">上一步</el-button>
        <el-button
          size="small"
          type="primary"
          :disabled="false"
          @click="nextStep"
        >
          下一步
        </el-button>
      </div>
    </div>
    <div class="step-two" v-if="actived == 3">
      <template v-if="filteredOldList.length">
        <div class="old-data-title">老指标编辑</div>
        <div class="warm-reminder">
          <svg-icon icon-class="warning" style="margin-top: 3px" />
          <div class="warm-reminder-content">
            <p>
              SQL编辑的过程中，若别名没有变化的指标则为“老指标”，可直接在原指标上做信息编辑。
            </p>
          </div>
        </div>
        <el-table
          :data="filteredOldList"
          class="sqlIndicator-table"
          style="
            width: calc(100% - 48px);
            margin: 24px 24px 0;
            overflow-x: auto;
          "
          max-height="calc(100% - 300px)"
        >
          <el-table-column
            prop="fieldNameModified"
            label="指标名称"
            width="250"
          >
            <!-- <template #default="{ row }">
          <el-input placeholder="" v-model="row.fieldNameModified"></el-input>
        </template> -->

            <template slot-scope="scope">
              <el-form-item
                :ref="'fieldNameModified' + scope.row.index"
                class="is-required"
                :prop="'oldList.' + scope.$index + '.fieldNameModified'"
                :rules="rules.fieldNameModified"
                style="margin-bottom: 0"
              >
                <el-input
                  placeholder=""
                  v-model="scope.row.fieldNameModified"
                ></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="val1" label="类型">
            <template #default="{ row }">
              <el-select v-model="row.tagType" placeholder="请选择" disabled>
                <el-option label="派生维度" value="派生维度"></el-option>
                <el-option label="指标" value="指标"></el-option>
                <el-option label="时间维度" value="时间维度"></el-option>
              </el-select>
            </template>
          </el-table-column> -->
          <el-table-column prop="execType" label="计算方式" width="260">
            <template #default="{ row }">
              <div style="display: flex">
                <el-select
                  v-model="row.execType"
                  placeholder="请选择"
                  :style="{
                    width: row.execType === 'sort' ? '76px' : '240px'
                  }"
                >
                  <el-option
                    v-for="(item, index) in jsfsList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <template v-if="row.execType === 'sort'">
                  <el-select
                    style="margin-left: 10px"
                    v-model="row.sortType"
                    placeholder="排序方式"
                    :style="{ width: '76px' }"
                    class="myselect"
                  >
                    <el-option
                      v-for="(item, index) in sortTypeList"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                  <el-select
                    style="margin-left: 10px"
                    v-model="row.sortRange"
                    v-if="row.execType === 'sort'"
                    :style="{ width: '76px' }"
                    class="myselect"
                  >
                    <el-option
                      v-for="(item, index) in sortDefineList"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                  <el-input
                    :style="{ width: '76px' }"
                    style="margin-left: 10px"
                    placeholder="数值"
                    v-if="row.sortRange === 'top'"
                    v-model.number="row.sortLimit"
                    clearable
                  ></el-input>
                </template>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="period" label="计算周期" width="150">
            <template #default="{ row }">
              <el-select
                v-model="row.period"
                placeholder="请选择"
                v-if="row.tagType == '度量' || row.tagType == '度量和维度'"
                :key="isFull"
                :popper-append-to-body="!isFull"
              >
                <el-option
                  v-for="item in jszqList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="scopeId" label="所属指标域" width="150">
            <template #default="{ row }">
              <avue-input-tree
                v-if="row.tagType == '度量' || row.tagType == '度量和维度'"
                default-expand-all
                v-model="row.scopeId"
                :key="isFull"
                :popper-append-to-body="!isFull"
                :props="{
                  label: 'name',
                  value: 'id'
                }"
                placeholder="请选择归属域"
                :dic="viewGroup"
              ></avue-input-tree>
            </template>
          </el-table-column>
          <el-table-column label="归属部门" width="150">
            <template #default="{ row }">
              <el-cascader
                clearable
                v-model="row.deptAllCode"
                :props="cascaderProps"
              ></el-cascader>
            </template>
          </el-table-column>
          <el-table-column prop="dataFormat" label="数据类型" width="150">
            <template #default="{ row }">
              <el-select v-model="row.dataFormat" placeholder="请选择数据格式">
                <el-option
                  v-for="item in sjgs"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="unitName" label="单位" width="200">
            <template #header>
              单位
              <span style="color: red">*</span>
            </template>
            <template #default="{ row }">
              <div style="display: flex">
                <el-select
                  v-model="row.unitName"
                  placeholder="请选择单位"
                  :key="isFull"
                  :popper-append-to-body="!isFull"
                  :style="{
                    width: row.unitName === '其他' ? '122px' : '250px'
                  }"
                  class="myselect"
                  v-if="row.tagType == '度量' || row.tagType == '度量和维度'"
                >
                  <el-option
                    v-for="(item, index) in dwList"
                    :key="index"
                    :label="item.name"
                    :value="item.bm"
                  ></el-option>
                  <el-option label="无单位" :value="null"></el-option>
                </el-select>
                <el-input
                  v-if="row.unitName === '其他'"
                  v-model="row.diydw"
                  style="width: 122px; margin-left: 6px"
                  placeholder="请输入单位"
                ></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column width="150">
            <template #header>
              精度
              <el-tooltip
                class="item"
                effect="dark"
                content="精度的数值代表小数点的位数，此处仅支持输入整数"
                placement="top"
              >
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template #default="{ row }">
              <div
                style="display: flex; align-items: center"
                v-if="row.tagType == '度量' || row.tagType == '度量和维度'"
              >
                <el-input
                  placeholder=""
                  style="min-width: 50px"
                  v-model.number="row.precision"
                ></el-input>
                <el-checkbox
                  true-label="是"
                  false-label="否"
                  style="margin-left: 16px"
                  v-model="row.rounding"
                >
                  四舍五入
                </el-checkbox>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="阈值" width="150">
            <template #default="{ row }">
              <div
                style="display: flex; align-items: center"
                v-if="row.tagType == '度量' || row.tagType == '度量和维度'"
              >
                <el-input placeholder="" v-model="row.thresholdMin"></el-input>
                -
                <el-input placeholder="" v-model="row.thresholdMax"></el-input>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="description" label="描述" width="150">
            <template #default="{ row }">
              <el-input
                v-if="row.tagType == '度量' || row.tagType == '度量和维度'"
                placeholder="请输入描述"
                v-model="row.description"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="tagsName" label="标签" width="150">
            <template #default="{ row }">
              <el-select
                v-if="row.tagType == '度量' || row.tagType == '度量和维度'"
                v-model="row.tagsName"
                filterable
                multiple
                remote
                allow-create
                default-first-option
                @change="(val) => {
                  row.tagsName = val.filter(item => item.trim() !== '')
                  changeTag(val)
                }"
                @visible-change="handleSelectVisibleChange"
                @remove-tag="removeTag"
                :remote-method="remoteMethod"
                placeholder="请创建或者选择标签"
                :key="isFull"
                :popper-append-to-body="!isFull"
              >
                <el-option
                  v-for="item in formatLabels"
                  :key="item.bqmc"
                  :label="item.bqmc"
                  :value="item.bqmc"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </template>

      <template v-if="filteredNewList.length">
        <div class="new-data-title">新指标编辑</div>
        <div class="warm-reminder">
          <svg-icon icon-class="warning" style="margin-top: 3px" />
          <div class="warm-reminder-content">
            <p>SQL编辑的过程中，若系统识别到新的别名，则支持创建为新指标。</p>
          </div>
        </div>
        <el-table
          :data="filteredNewList"
          class="sqlIndicator-table"
          style="
            width: calc(100% - 48px);
            margin: 24px 24px 0;
            overflow-x: auto;
          "
          max-height="calc(100% - 300px)"
        >
          <el-table-column
            prop="fieldNameModified"
            label="指标名称"
            width="250"
          >
            <!-- <template #default="{ row }">
          <el-input placeholder="" v-model="row.fieldNameModified"></el-input>
        </template> -->

            <template slot-scope="scope">
              <el-form-item
                :ref="'fieldNameModified' + scope.row.index"
                class="is-required"
                :prop="'newList.' + scope.$index + '.fieldNameModified'"
                :rules="rules.fieldNameModified"
                style="margin-bottom: 0"
              >
                <el-input
                  placeholder=""
                  v-model="scope.row.fieldNameModified"
                ></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="val1" label="类型">
            <template #default="{ row }">
              <el-select v-model="row.tagType" placeholder="请选择" disabled>
                <el-option label="派生维度" value="派生维度"></el-option>
                <el-option label="指标" value="指标"></el-option>
                <el-option label="时间维度" value="时间维度"></el-option>
              </el-select>
            </template>
          </el-table-column> -->
          <el-table-column prop="val1" label="计算方式" width="260">
            <template #default="{ row }">
              <div style="display: flex">
                <el-select
                  v-model="row.execType"
                  placeholder="请选择"
                  :style="{
                    width: row.execType === 'sort' ? '76px' : '240px'
                  }"
                >
                  <el-option
                    v-for="(item, index) in jsfsList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <template v-if="row.execType === 'sort'">
                  <el-select
                    style="margin-left: 10px"
                    v-model="row.sortType"
                    placeholder="排序方式"
                    :style="{ width: '76px' }"
                    class="myselect"
                  >
                    <el-option
                      v-for="(item, index) in sortTypeList"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                  <el-select
                    style="margin-left: 10px"
                    v-model="row.sortRange"
                    v-if="row.execType === 'sort'"
                    :style="{ width: '76px' }"
                    class="myselect"
                  >
                    <el-option
                      v-for="(item, index) in sortDefineList"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                  <el-input
                    :style="{ width: '76px' }"
                    style="margin-left: 10px"
                    placeholder="数值"
                    v-if="row.sortRange === 'top'"
                    v-model.number="row.sortLimit"
                    clearable
                  ></el-input>
                </template>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="period" label="计算周期" width="150">
            <template #default="{ row }">
              <el-select
                v-model="row.period"
                placeholder="请选择"
                v-if="row.tagType == '度量' || row.tagType == '度量和维度'"
                :key="isFull"
                :popper-append-to-body="!isFull"
              >
                <el-option
                  v-for="item in jszqList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="scopeId" label="所属指标域" width="150">
            <template #default="{ row }">
              <avue-input-tree
                v-if="row.tagType == '度量' || row.tagType == '度量和维度'"
                default-expand-all
                v-model="row.scopeId"
                :key="isFull"
                :popper-append-to-body="!isFull"
                :props="{
                  label: 'name',
                  value: 'id'
                }"
                placeholder="请选择归属域"
                :dic="viewGroup"
              ></avue-input-tree>
            </template>
          </el-table-column>
          <el-table-column label="归属部门" width="150">
            <template #default="{ row }">
              <el-cascader
                v-model="row.deptAllCode"
                :props="cascaderProps"
              ></el-cascader>
            </template>
          </el-table-column>
          <el-table-column prop="dataFormat" label="数据类型" width="150">
            <template #default="{ row }">
              <el-select v-model="row.dataFormat" placeholder="请选择数据格式">
                <el-option
                  v-for="item in sjgs"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="unitName" label="单位" width="200">
            <template #header>
              单位
              <span style="color: red">*</span>
            </template>
            <template #default="{ row }">
              <div style="display: flex">
                <el-select
                  v-model="row.unitName"
                  placeholder="请选择单位"
                  :key="isFull"
                  :popper-append-to-body="!isFull"
                  :style="{
                    width: row.unitName === '其他' ? '122px' : '250px'
                  }"
                  class="myselect"
                  v-if="row.tagType == '度量' || row.tagType == '度量和维度'"
                >
                  <el-option
                    v-for="(item, index) in dwList"
                    :key="index"
                    :label="item.name"
                    :value="item.bm"
                  ></el-option>
                  <el-option label="无单位" :value="null"></el-option>
                </el-select>
                <el-input
                  v-if="row.unitName === '其他'"
                  v-model="row.diydw"
                  style="width: 122px; margin-left: 6px"
                  placeholder="请输入单位"
                ></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column width="150">
            <template #header>
              精度
              <el-tooltip
                class="item"
                effect="dark"
                content="精度的数值代表小数点的位数，此处仅支持输入整数"
                placement="top"
              >
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template #default="{ row }">
              <div
                style="display: flex; align-items: center"
                v-if="row.tagType == '度量' || row.tagType == '度量和维度'"
              >
                <el-input
                  placeholder=""
                  style="min-width: 50px"
                  v-model.number="row.precision"
                ></el-input>
                <el-checkbox
                  true-label="是"
                  false-label="否"
                  style="margin-left: 16px"
                  v-model="row.rounding"
                >
                  四舍五入
                </el-checkbox>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="阈值" width="150">
            <template #default="{ row }">
              <div
                style="display: flex; align-items: center"
                v-if="row.tagType == '度量' || row.tagType == '度量和维度'"
              >
                <el-input placeholder="" v-model="row.thresholdMin"></el-input>
                -
                <el-input placeholder="" v-model="row.thresholdMax"></el-input>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="description" label="描述" width="150">
            <template #default="{ row }">
              <el-input
                v-if="row.tagType == '度量' || row.tagType == '度量和维度'"
                placeholder="请输入描述"
                v-model="row.description"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="tagsName" label="标签" width="150">
            <template #default="{ row }">
              <el-select
                v-if="row.tagType == '度量' || row.tagType == '度量和维度'"
                v-model="row.tagsName"
                filterable
                multiple
                remote
                allow-create
                default-first-option
                @change="(val) => {
                  row.tagsName = val.filter(item => item.trim() !== '')
                  changeTag(val)
                }"
                @visible-change="handleSelectVisibleChange"
                @remove-tag="removeTag"
                :remote-method="remoteMethod"
                placeholder="请创建或者选择标签"
                :key="isFull"
                :popper-append-to-body="!isFull"
              >
                <el-option
                  v-for="item in formatLabels"
                  :key="item.bqmc"
                  :label="item.bqmc"
                  :value="item.bqmc"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </template>

      <div class="step-btn">
        <el-button @click="$router.push('/ddsBi/indicatorAnagement')">
          取消
        </el-button>
        <el-button size="small" @click="handleBack">上一步</el-button>
        <el-button
          size="small"
          type="primary"
          :disabled="false"
          @click="handleSave"
        >
          保存
        </el-button>
      </div>
    </div>
    <el-dialog
      title="编辑维度"
      :visible.sync="editDialogVisible"
      width="600px"
      label-width="100px"
      label-position="top"
    >
      <el-form :model="editDimForm" :rules="editDimRules" ref="editDimForm">
        <el-form-item label="维度名称" prop="dimName">
          <el-input v-model="editDimForm.dimName" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="维度类型">
          <el-select
            v-model="editDimForm.categoryCode"
            placeholder="请选择类型"
            style="width: 100%"
          >
            <el-option
              :label="item.label"
              :value="item.value"
              v-for="item in dimensionTypeList"
              :key="item.categoryCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="版本" prop="version">
          <el-input v-model="editDimForm.version" placeholder="请输入版本" />
        </el-form-item>
        <el-form-item label="层级名称" prop="levelName">
          <el-input
            v-model="editDimForm.levelName"
            placeholder="请输入层级名称"
          />
        </el-form-item>
        <el-form-item label="标签">
          <el-input v-model="editDimForm.tags" placeholder="请输入标签" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            type="textarea"
            :rows="4"
            placeholder="请输入描述"
            v-model="editDimForm.description"
          ></el-input>
        </el-form-item>
        <el-form-item label="更新频率">
          <el-select
            v-model="editDimForm.updateFrequency"
            placeholder="请选择更新频率"
          >
            <el-option
              v-for="item in updateFrequencys"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="更新方式">
          <el-radio-group v-model="editDimForm.updateType">
            <el-radio :label="0">增量同步</el-radio>
            <el-radio :label="1">全量同步</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveEditDim">确 定</el-button>
      </span>
    </el-dialog>
    <AffectScopeDialog ref="AffectScopeDialog" />
  </el-form>
</template>

<script>
import Request from '@/service'
import options from '../mixins/options'
// 变更影响弹窗
import AffectScopeDialog from '../components/AffectScopeDialog.vue'
export default {
  components: {
    AffectScopeDialog
  },
  mixins: [options],
  props: {
    sql: {
      type: String,
      default: ''
    },
    newSql: {
      type: String,
      default: ''
    },
    isFull: {
      type: Boolean,
      default: false
    },
    indCode: {
      type: Number,
      default: 0
    },
    tagOptions: {
      type: Array,
      default: () => []
    }
  },
  data () {
    let messageInstance = null
    const resetMessage = options => {
      if (messageInstance) messageInstance.close()
      messageInstance = this.$message(options)
    }
    var changeFieldNameModified = async (rule, value, callback) => {
      const arr = [...this.form.oldList, ...this.form.newList].filter(
        item => item.fieldNameModified === value
      )
      let errorMsg = ''
      if (arr.length > 1) {
        // callback(new Error("指标或维度名称已存在,请重新输入"))
        errorMsg = '指标或维度名称已存在,请重新输入'
      }

      const { data } = await Request.api.paramPost(
        'SqlIndicator/existByNameAndType',
        {
          name: value,
          type: arr[0].tagType,
          indCode: arr[0].indCode,
          dimensions: [...this.form.oldList, ...this.form.newList]
            .filter(e => e.tagType === '维度' || e.tagType === '度量和维度')
            .map(item => item.fieldNameModified)
        }
      )

      if (data) errorMsg = '指标或维度名称已存在,请重新输入'
      if (errorMsg) {
        resetMessage({ message: errorMsg, type: 'warning' })
        callback(new Error(errorMsg))
      } else {
        callback()
      }
    }

    return {
      form: {
        oldList: [],
        newList: []
      },
      actived: 2,
      dimLevelMap: {}, // 维度层级
      concatList: [],
      allDimTreeList: [], // 所有维度树
      editDialogVisible: false,
      editDimForm: {
        dimName: '',
        version: ''
      },
      editDimRules: {
        dimName: [
          { required: true, message: '请输入维度名称', trigger: 'blur' }
        ],
        version: [{ required: true, message: '请输入版本', trigger: 'blur' }],
        levelName: [
          { required: true, message: '请输入层级名称', trigger: 'blur' }
        ]
      },
      editRow: null,
      updateFrequencys: [
        { label: '按日', value: 1 },
        { label: '按周', value: 2 },
        { label: '按月', value: 3 },
        { label: '按学期', value: 4 },
        { label: '按学年', value: 5 },
        { label: '按年', value: 6 }
      ],
      dimensionTypeList: [],
      rules: {
        fieldNameModified: [
          {
            validator: changeFieldNameModified,
            trigger: 'blur',
            required: true
          }
        ]
      }
    }
  },
  computed: {
    filteredNewList () {
      return this.form.newList.filter(
        item => item.tagType === '度量' || item.tagType === '度量和维度'
      )
    },
    filteredOldList () {
      return this.form.oldList.filter(
        item => item.tagType === '度量' || item.tagType === '度量和维度'
      )
    }
  },
  created () {
    console.log('子页面+++++++++++++++++++++///////////////')
    this.getLabelSelectList()
    this.getBaseUnit()
    this.getAllViewGroup()
    this.getTableData()
    this.getDimTree()
  },
  mounted () {},
  watch: {},
  methods: {
    async getTableData () {
      const { data } = await Request.api.paramPost(
        '/SqlIndicator/editPartition',
        {
          sqlStatement: this.sql,
          newSqlStatement: this.newSql,
          indCode: this.indCode
        }
      )
      this.form.oldList = data.oldList.map(item => {
        return {
          ...item,
          fieldName: item.fieldNameModified,
          period: String(item.period),
          groupName: 'old',
          newSqlStatement: this.newSql,
          tagsName: item.tagsName ? item.tagsName.split(',') : [],
          deptName: item.deptName || null,
          sortType: item.sortType || null,
          sortRange: item.sortRange || null,
          levelCode: item.levelCode || null,
          sortLimit: item.sortLimit || null,
          dataFormat: item.dataFormat || 0,
          execType: item.execType || 'sum',
          noCoverValues: []
        }
      })

      this.form.newList = data.newList.map(item => ({
        fieldName: item,
        fieldNameModified: item,
        tagType: item.toUpperCase() === 'ID' ? '无' : '',
        period: '1',
        scopeId: Number(this.$route.query.sysjy) || 999,
        precision: null,
        rounding: '否',
        thresholdMin: null,
        thresholdMax: null,
        unitName: '',
        description: '',
        tagsName: [],
        createdById: null,
        indCode: null,
        sqlStatementOrigin: this.newSql,
        groupName: 'new',
        deptName: item.deptName || null,
        sortType: item.sortType || null,
        sortRange: item.sortRange || null,
        levelCode: item.levelCode || null,
        sortLimit: item.sortLimit || null,
        dataFormat: item.dataFormat || 0,
        execType: item.execType || 'sum',
        noCoverValues: []
      }))
      this.concatList = [...this.form.oldList, ...this.form.newList]
      console.log(this.concatList, 'this.concatList')
      this.initAllDimLevels()
    },
    async handleSave () {
      this.$refs.ruleForm.validate(async valid => {
        if (valid) {
          // 检查单位是否必填
          const hasEmptyUnit = [
            ...this.form.oldList,
            ...this.form.newList
          ].some(item => {
            return (
              (item.tagType === '度量' || item.tagType === '度量和维度') &&
              !item.unitName &&
              item.unitName !== null
            )
          })
          if (hasEmptyUnit) {
            this.$message.error('请为所有指标填写单位')
            return
          }

          let params = [...this.form.oldList, ...this.form.newList].map(
            item => {
              return {
                ...item,
                unitName: item.unitName === '其他' ? item.diydw : item.unitName
              }
            }
          )
          console.log('????')

          await this.$refs.AffectScopeDialog.isAffectSpace(
            {
              indCode: this.indCode,
              indType: 'sq'
            },
            async () => {
              const { data } = await Request.api.paramPost(
                '/SqlIndicator/saveEdit',
                {
                  ind: params,
                  optType: 'update'
                }
              )
              this.$message.success(data)
              this.$router.push({
                path: '/ddsBi/indicatorAnagement',
                query: {}
              })
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    async getDimTree () {
      const { data } = await Request.api.paramPost('/DimManage/getDimList', {
        pageSize: -1,
        dimName: '',
        pageNum: 1
      })
      this.allDimTreeList = [
        {
          dimName: '自身维度创建',
          definitionCode: 'self',
          value: ''
        },
        ...(data.list || [])
      ]
    },
    // 获取维度版本
    async getDimLevels (definitionCode, row) {
      if (definitionCode === 'self') {
        await this.createSelfDim(row)
      } else {
        const { data } = await Request.api.paramPostQuery(
          '/DimManage/getDimLevelByDefinitionCode',
          { definitionCode }
        )
        if (data.length) {
          row.levelCode = data[0].levelCode
          row.levelName = data[0].levelName
          this.onLevelCodeChange(row)
        } else {
          row.levelCode = ''
          row.levelName = ''
          this.$message.warning('该维度没有层级')
        }
        this.$set(this.dimLevelMap, definitionCode, data)
      }
    },
    async changeDimType (type, row) {
      if (type === '维度' || type === '维度和度量') {
        let params = [
          {
            tableSource: this.newSql, // 数据源ID
            fieldCode: '',
            fieldName: row.fieldName,
            lxbm: 'sq'
          }
        ]
        const [matchResult] = await this.matchDim(params)
        console.log(matchResult, 'matchResult')
        this.$set(row, 'definitionCode', matchResult.definitionCode)
        this.$set(row, 'noCoverValues', matchResult.noCoverValues)
        this.$set(row, 'levelCode', matchResult.levelCode)
        this.$set(row, 'levelName', matchResult.levelName)
        if (matchResult.definitionCode) {
          this.getDimLevels(matchResult.definitionCode, row)
        }
      }
      console.log(type, row)
    },
    // 匹配相似维度和层级
    async matchDim (params) {
      const { data } = await Request.api.paramPost(
        '/AtomIndicator/checkDimMatch',
        params
      )
      return data
    },
    async createSelfDim (row) {
      const { data } = await Request.api.paramPost('/DimManage/addDim', {
        dimDefinition: {
          categoryCode: 'wdlx_jcwd_1930198052817580032',
          dimName: row.fieldName || row.fieldCode,
          description: '',
          version: 'v1.0',
          tags: '基于自身创建维度',
          updateFrequency: 1,
          updateType: 0,
          categoryName: '基础维度'
        },
        createType: 0,
        dimLevels: [
          {
            level: 1,
            levelName: row.fieldName || row.fieldCode,
            sourceTable: this.newSql,
            sourceField: row.fieldName,
            fieldValueType: row.dataFormat
          }
        ],
        configs: []
      })
      await this.getDimTree()
      this.$set(row, 'definitionCode', data.definitionCode)
      this.$set(row, 'dimName', data.dimName)
      this.$set(row, 'newCreate', true)
      this.$set(row, 'version', data.version)
      this.$set(row, 'categoryCode', 'wdlx_jcwd_1930198052817580032')
      this.$set(row, 'tags', '基于自身创建维度')
      this.$set(row, 'description', '')
      this.$set(row, 'updateFrequency', 1)
      this.$set(row, 'updateType', 0)
      this.getDimLevels(data.definitionCode, row)
    },
    removeDim (row) {
      this.$confirm('是否删除当前维度树数据', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          Request.api
            .paramDel('/DimManage/deleteDimByCode', {
              code: row.definitionCode
            })
            .then(() => {
              this.$message.success('删除成功')
              this.$set(row, 'definitionCode', '')
              this.$set(row, 'dimName', '')
              this.$set(row, 'version', '')
              this.$set(row, 'newCreate', false)
              this.$set(row, 'levelCode', '')
              this.getDimTree()
            })
        })
        .catch(error => {
          console.error(error)
        })
    },
    editDim (row) {
      this.editRow = row
      this.editDimForm = {
        definitionCode: row.definitionCode || '',
        dimName: row.dimName || '',
        version: row.version || '',
        categoryCode: row.categoryCode || '',
        levelName: row.levelName || '',
        tags: row.tags || '',
        description: row.description || '',
        updateFrequency:
          typeof row.updateFrequency === 'undefined' ? 1 : row.updateFrequency,
        updateType: typeof row.updateType === 'undefined' ? 0 : row.updateType
      }
      this.editDialogVisible = true
    },
    saveEditDim () {
      this.$refs.editDimForm.validate(async valid => {
        if (valid) {
          await Request.api.paramPost('/DimManage/editVersion', {
            ...this.editDimForm
          })
          this.$message.success('编辑成功')
          this.editDialogVisible = false
          if (this.editRow) {
            Object.assign(this.editRow, this.editDimForm)
          }
        } else {
          return false
        }
      })
    },
    nextStep () {
      // concatList中编辑类型必须选择类型且如果是维度或者度量和维度必须有维度层级
      // 且必须至少有个为度量或者度量和维度
      let hasMeasure = false
      for (const row of this.concatList) {
        if (!row.tagType) {
          this.$message.warning('请选择类型')
          return
        }
        if (row.tagType === '维度' || row.tagType === '度量和维度') {
          if (!row.levelCode) {
            this.$message.warning('请选择维度层级')
            return
          }
        }
        if (row.tagType === '度量' || row.tagType === '度量和维度') {
          hasMeasure = true
        }
      }
      if (!hasMeasure) {
        this.$message.warning('请选择度量')
        return
      }
      this.actived = 3
    },
    handleBack () {
      this.actived = 2
    },
    async initAllDimLevels () {
      // for (const row of this.concatList) {
      //   if (row.definitionCode) {
      //     await this.getDimLevels(row.definitionCode, row)
      //     // getDimLevels 内部会自动设置 dimLevelMap[row.definitionCode]
      //     // 并且如果 row.levelCode 已有值，select 会自动回显
      //   }
      // }
      for (const row of this.form.oldList) {
        if (
          row.definitionCode &&
          row.levelCode &&
          this.dimLevelMap[row.definitionCode]
        ) {
          const match = this.dimLevelMap[row.definitionCode].find(
            item => item.levelCode === row.levelCode
          )
          if (match) {
            row.levelName = match.levelName
          }
        }
      }
    },
    loadMoreNoCover (row) {
      if (!row.noCoverShowCount) {
        this.$set(row, 'noCoverShowCount', 10)
      } else {
        this.$set(row, 'noCoverShowCount', row.noCoverShowCount + 5)
      }
    },
    resetNoCoverShowCount (row) {
      this.$set(row, 'noCoverShowCount', 5)
    },
    // 选择层级时
    async onLevelCodeChange (row) {
      await this.getNoCoverDimValues(row)
    },
    // 获取无覆盖维度
    async getNoCoverDimValues (row) {
      console.log(row.fieldName, 'row')
      const { data } = await Request.api.paramPost(
        '/DimManage/getNoCoverDimValues',
        {
          tableSource: this.newSql,
          levelCode: row.levelCode,
          fieldCode: row.fieldCode,
          lxbm: 'sq',
          fieldName: row.fieldName
        }
      )
      row.noCoverValues = data
    }
  }
}
</script>

<style scoped lang="scss">
.popover-content {
  max-height: 150px;
  overflow-y: auto;
  // 可选：自定义滚动条样式
  &::-webkit-scrollbar {
    width: 4px;
    background: #f5f5f5;
  }
  &::-webkit-scrollbar-thumb {
    background: #cbced1;
    border-radius: 2px;
  }
}
.warm-reminder {
  position: relative;
  width: calc(100% - 48px);
  height: 48px;
  background: #fffbe6;
  border-radius: 2px;
  border: 1px solid #fff1b8;
  display: flex;
  padding: 12px 17px 0;
  box-sizing: border-box;
  margin: 16px 24px 0;
  .warm-reminder-content {
    margin-left: 8px;
    p {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #323233;
      line-height: 22px;
      height: 22px;
    }
  }
  .el-icon-close {
    position: absolute;
    right: 12px;
    top: 12px;
  }
}
.old-data-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  padding: 20px 24px 0;
}
.new-data-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  padding: 20px 24px 0;
}

.step-two {
  min-height: calc(100% - 100px);
  display: flex;
  flex-direction: column;
  background-color: #fff;
}
.step-btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 52px;
  padding-right: 24px;
  box-sizing: border-box;
}
::v-deep .ruleForm {
  background-color: #fff;
}
/* 为了确保自定义样式能覆盖默认样式，你可能需要使用更具体的选择器 */
::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  width: 6px; /* 设置滚动条宽度 */
  height: 6px; /* 设置滚动条高度 */
}

::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
  border-radius: 3px; /* 滚动条圆角 */
  background: rgba(0, 0, 0, 0.2); /* 滚动条颜色 */
}

::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1); /* 滚动条轨道阴影 */
  background-color: #f0f0f0; /* 滚动条轨道颜色 */
  border-radius: 3px; /* 滚动条圆角 */
}
</style>
