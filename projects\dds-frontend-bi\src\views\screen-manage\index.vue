<template>
  <div>
    <list ref="list" v-show="'list' === com" @click="child_click" />
    <edit
      v-if="'edit' === com"
      @click="child_click"
      :id="editId"
      :form-data="formData"
    />
    <info v-if="'info' === com" @click="child_click" :id="editId" />
  </div>
</template>

<script>
import Request from "@/service"
import edit from "./component/edit"
import list from "./component/list"
import info from "./component/info"

export default {
  name: "index",
  components: { edit, list, info },
  data() {
    return {
      editId: "",
      com: "list",
      name: "",
      formData: {},
      typeList: [
        {
          label: "定制开发",
          value: 2
        },
        {
          label: "外链大屏",
          value: 3
        }
      ],
      themes: []
    }
  },
  provide() {
    return {
      typeList: this.typeList,
      parent: this
    }
  },
  created() {
    this.getAllThemes()
  },
  methods: {
    child_click(val) {
      // this.show = false;
      // {com:'组件',opt:'操作',data:数据}
      this.com = val.com
      if (val.opt === "list" || val.opt === "back") {
        this.$refs.list.getData()
      } else {
        this.editId = val.data.id
        this.formData = {
          ...val.data,
          roles: val.data.roles ? val.data.roles : []
        }
      }
    },
    getAllThemes() {
      Request.dashboard
        .getAllThemes()
        .then(res => {
          this.themes = res.data
        })
        .catch(() => {})
        .finally(() => {})
    }
  }
}
</script>

<style lang="scss" scoped></style>
