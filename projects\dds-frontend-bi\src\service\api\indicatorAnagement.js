import service from "../base"
import config from "../config"
// const config = {
//   VUE_MODULE_DDS_BI: "/dds-server-bi-zqz/"
// }
export default {
  // 数据源分组
  getAllViewGroup(
    data = {
      lxbm: "",
      zbmc: "",
      lxbms: [],
      sysjyid: ""
    }
  ) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/zbgl/getAllViewGroupByParam",
      method: "post",
      data: data
    })
  },
  // 获取数据域和指标
  getAllIndicatorList(data) {
    return service(
      {
        url:
          config.VUE_MODULE_DDS_BI + "/zbgl/getAllIndicatorListByViewGroupId",
        method: "post",
        data
      },
      "zbgl/getAllIndicatorListByViewGroupId"
    )
  },
  // 获取原子指标
  getZbList(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "zbgl/getAtomIndicatorList",
      method: "post",
      data
    })
  },

  // 添加主题域
  addZty(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "zbgl/addViewGroup",
      method: "post",
      data
    })
  },
  // 编辑主题域
  editZty(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "zbgl/editViewGroup",
      method: "post",
      data
    })
  },
  // 删除主题域
  deleteZty(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "zbgl/delViewGroup",
      method: "get",
      params: data
    })
  },
  // 批量打标签
  batchMakeTag(params,data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/zbgl/tagsIndicators",
      method: "post",
      params,
      data
    })
  },
  // 批量启用
  batchEnable(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/zbgl/enableIndicators",
      method: "post",
      data
    })
  },
  // 批量禁用
  batchDisable(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/zbgl/disEnableIndicators",
      method: "post",
      data
    })
  },
  //禁用
  disable(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/zbgl/disEnableIndicator",
      method: "post",
      data
    })
  },
  // 启用
  enable(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/zbgl/enableIndicator",
      method: "post",
      data
    })
  },
  // 批量修改指标域
  batchMoveDomain(params,data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/zbgl/domainIndicators",
      method: "post",
      params,
      data
    })
  },

  // 原子指标------------------------------------------------------------------------
  // 删除原子指标
  deleteYz(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/AtomIndicator/delAtomIndicator",
      method: "get",
      params: data
    })
  },
  // 获取原子指标详情信息
  getAtomIndicatorInfo(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/AtomIndicator/getAtomIndicator",
      method: "post",
      params: data
    })
  },

  // 查询数据源表信息
  getTableList(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/AtomIndicator/getSjyTable",
      method: "post",
      data
    })
  },
  // 获取数据源表字段信息
  getTableFields(data){
     return service({
      url: config.VUE_MODULE_DDS_BI + "/AtomIndicator/getTableInfo",
      method: "post",
      data
    })
  },


  
  
  // 原子指标
  getTableInfo(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/AtomIndicator/getTableInfo",
      method: "post",
      data
    })
  },
  // 编辑原子指标
  editYz(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/AtomIndicator/editAtomIndicator",
      method: "post",
      data
    })
  },
  // 查询维度表信息
  getWdTableList(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/AtomIndicator/getWdTable",
      method: "post",
      data
    })
  },
  // 原子指标预览后保存
  saveYzzb(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/AtomIndicator/addAtomIndicatorInfo",
      method: "post",
      data
    })
  },
  // 获取原子指标列表
  getYzList(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "DeriveIndicator/getYzPsList",
      method: "get",
      params: data
    })
  },

  // 派生指标------------------------------------------------------------------------

  // 获取派生指标详情信息
  getDeriveIndicatorInfo(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/DeriveIndicator/getDeriveIndicator",
      method: "post",
      params: data
    })
  },
  // 新增派生指标
  addPszb(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/DeriveIndicator/addDeriveIndicator",
      method: "post",
      data
    })
  },
  // 派生指标删除
  deletePs(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/DeriveIndicator/delDeriveIndicator",
      method: "get",
      params: data
    })
  },
  // 编辑派生指标
  editDeriveIndicator(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/DeriveIndicator/editDeriveIndicator",
      method: "post",
      data
    })
  },

  tryToCaluIndicator(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/DeriveIndicator/tryToCaluIndicator",
      method: "post",
      data
    })
  },
  // 获取单个维度值
  getDimensionValueById(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/DeriveIndicator/getDimValSelectList",
      method: "get",
      params: data
    })
  },
  // 是否包含时间维度
  isTimeDim(data) {
    return service({
      url:
        config.VUE_MODULE_DDS_BI +
        "/DeriveIndicator/checkWdlxTimeByDeriveIndCode",
      method: "get",
      params: data
    })
  },

  // 复合指标---------------------------------------------------------

  // --------------------------------------------------------------
  // 指标分析
  getZbfx(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "zbgl/getIndicatorSchema",
      method: "post",
      data
    })
  },
  // 获取相同维度
  getSameDimension(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "zbgl/getSameDimension",
      method: "post",
      data
    })
  },
  // 获取维度所选项
  getDimensionOptions(data) {
    return service(
      {
        url: config.VUE_MODULE_DDS_BI + "zbgl/getDimensionOptions",
        method: "post",
        data
      },
      "zbgl/getDimensionOptions"
    )
  },
  // 获取指标分析
  getIndicatorAnalysis(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "zbgl/getIndicatorAnalysis",
      method: "post",
      data
    })
  },

  // 获取公共维度
  getCommonDimension(data) {
    return service(
      {
        url: config.VUE_MODULE_DDS_BI + "zbgl/getCommonDimension",
        method: "post",
        data
      },
      "zbgl/getCommonDimension"
    )
  },
  // 获取单个维度
  getDimension(data) {
    return service(
      {
        url: config.VUE_MODULE_DDS_BI + "zbgl/getDimension",
        method: "post",
        data
      },
      "zbgl/getDimension"
    )
  },
  // 保存派生指标
  saveDerivedIndicators(data) {
    return service(
      {
        url: config.VUE_MODULE_DDS_BI + "zbgl/addCompositeIndicator",
        method: "post",
        data
      },
      "zbgl/addCompositeIndicator"
    )
  },
  // 是计算结果
  getCalculateResult(data) {
    return service(
      {
        url: config.VUE_MODULE_DDS_BI + "zbgl/getCalculateResult",
        method: "post",
        data
      },
      "zbgl/getCalculateResult"
    )
  },
  // /zbgl/checkIndicatorRepeat
  checkIndicatorRepeat(data) {
    return service(
      {
        url: config.VUE_MODULE_DDS_BI + "zbgl/checkIndicatorRepeat",
        method: "post",
        params: data
      },
      "zbgl/checkIndicatorRepeat"
    )
  },
  getIndicatorTags(tag) {
    return service(
      {
        url: config.VUE_MODULE_DDS_BI + "zbgl/getIndicatorTags?tag=" + tag,
        method: "post"
      },
      "zbgl/getIndicatorTags"
    )
  },
  addIndicatorTags(tags) {
    return service(
      {
        url: config.VUE_MODULE_DDS_BI + "zbgl/addIndicatorTags?tags=" + tags,
        method: "post"
      },
      "zbgl/addIndicatorTags"
    )
  },
  // 复合指标删除
  delCompositeIndicator(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "zbgl/delCompositeIndicator",
      method: "get",
      params: data
    })
  },

  getIndicatorInfo(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "zbgl/getIndicatorInfo",
      method: "post",
      params: data
    })
  },

  getBaseUnit() {
    return service({
      url: config.VUE_MODULE_DDS_BI + "zbgl/getBaseUnit",
      method: "post"
    })
  },

  // 演示保存复合指标

  saveTempCompositeIndicator() {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/zbgl/saveTempCompositeIndicator",
      method: "get"
    })
  },
  saveTempSqlIndicator(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/zbgl/saveTempSqlIndicator",
      method: "get",
      params: data
    })
  },
  // 复合指标删除
  deleteSql(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/SqlIndicator/delete",
      method: "delete",
      params: data
    })
  },

  // 获取所有指标常模类型
  getAllNormativeType() {
    return service({
      url: config.VUE_MODULE_DDS_BI + "zbgl/getAllNormativeType",
      method: "post"
    })
  },
  // 通过指标id获取指标类型常模
  getNormativeTypeByZb(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "zbgl/getNormativeTypeByZb",
      method: "get",
      params: data
    })
  },
  // 通过指标id和指标类型编码删除常模值
  delNormativeTypeByZb(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "zbgl/delNormativeTypeByZb",
      method: "get",
      params: data
    })
  },
  // 更新插入常模
  updateNormativeTypeByZb(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "zbgl/updateNormativeTypeByZb",
      method: "post",
      data
    })
  },
  // 常模列表
  getNormativeTypePageList(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "zbgl/getNormativeTypePageList",
      method: "post",
      data
    })
  },
  // 算法指标---------------------------------------------------------
  // 删除算法指标
  deleteSf(id) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "algorithmIndicator/" + id,
      method: "delete"
    })
  }
}
