<!-- 调用历史 -->
<template>
  <!-- <DT-View class="rolePage-mainView"> -->
  <div>
    <el-table
      :data="tableData"
      v-loading="loading"
      ref="table"
      height="600px"
      row-key="id"
    >
      <el-table-column prop="runTime" label="运行时间" min-width="170"></el-table-column>
      <el-table-column label="算法版本" min-width="100" prop="version"></el-table-column>
      <el-table-column prop="runType" label="运行类型" min-width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.runType === 1">试运行</el-tag>
          <el-tag v-if="scope.row.runType === 2">指标计算</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="callIndicatorCode" label="调用指标" min-width="100"></el-table-column>
      <el-table-column prop="inputParam" label="入参" min-width="100"></el-table-column>
      <el-table-column prop="output" label="输出" min-width="180"></el-table-column>
      <el-table-column prop="runTimeLength" label="运行时长" min-width="100">
        <template slot-scope="scope">{{ scope.row.runTimeLength }}s</template>
      </el-table-column>
    </el-table>

    <DT-Pagination
      :hidden="!pagination.total"
      :total="pagination.total"
      :page-size="pagination.pageSize"
      :current-page="pagination.currentPage"
      @sizeChange="handleSizeChange"
      @currentChange="handleCurrentChange"
    />
  </div>
</template>

<script>
import Request from "@/service"
export default {
  props: {
    sceneCode: {
      type: String,
      default: () => ""
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      pagination: {
        total: 0,
        pageSize: 10,
        currentPage: 1
      }
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    async getTableData() {
      this.loading = true
      try {
        const res = await Request.algorithm.getAlgorithmLog({
          algorithmCode: this.sceneCode || "",
          pageNum: this.pagination.currentPage,
          pageSize: this.pagination.pageSize
        })
        console.log(res, "getAlgorithmLog")
        if (res.code === 200) {
          this.tableData = res.data.list || []
          this.pagination.total = res.data.totalCount || 0
        }
      } catch (error) {
        console.error("获取算法日志失败:", error)
        this.tableData = []
        this.pagination.total = 0
      } finally {
        this.loading = false
      }
    },
    handleSizeChange(val) {
      console.log(121212, val)
      this.pagination.pageSize = val.pageSize
      this.pagination.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(val) {
      console.log(223232323, val)
      this.pagination.currentPage = val.currentPage
      this.getTableData()
    }
  }
}
</script>
