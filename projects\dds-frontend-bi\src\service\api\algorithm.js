// 复合指标
import service from "../base"
import config from "../config"

// const config = {
//   VUE_MODULE_DDS_BI: "/dds-server-bi-zqz/"
// }
export default {
  // 工具算法列表
  getAlgorithmList(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "tool/algorithm/page",
      method: "get",
      params: data
    })
  },
  // 工具算法试计算
  tryCalculate(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "tool/algorithm/tryExcute",
      method: "post",
      data
    })
  },
  // 工具算法日志查询
  getAlgorithmLog(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/tool/algorithm/log/page",
      method: "get",
      params: data
    })
  },
  // 算法指标试计算
  tryCalculateIndicator(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "algorithmIndicator/tryExcute",
      method: "post",
      data
    })
  },
  // 算法指标保存
  saveOrUpdateAlgorithmIndicator(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "algorithmIndicator/saveOrUpdate",
      method: "post",
      data
    })
  },
  // 算法指标查询指标名称
  checkIndNameExistAlgorithmIndicator(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "algorithmIndicator/checkIndNameExist",
      method: "get",
      params: data
    })
  },
  // 工具算法详情列表
  getAlgorithmDetailList(id) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "tool/algorithm/detail/list/" + id,
      method: "get",
    })
  },
  // 算法指标详情列表
  getAlgorithmlndicatorDetailList(id) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "algorithmIndicator/detail/list/" + id,
      method: "get",
    })
  },
  // 复制工具算法
  copyAlgorithm(id) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "tool/algorithm/copy/" + id,
      method: "get",
    })
  },
  // 删除工具算法
  deleteAlgorithm(id) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "tool/algorithm/" + id,
      method: "delete",
    })
  },
  // 工具算法详情
  getAlgorithmDetail(id) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "tool/algorithm/" + id,
      method: "get",
    })
  },
  // 工具算法保存
  saveAlgorithm(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "tool/algorithm/saveOrUpdate",
      method: "post",
      data
    })
  },

}
