<template>
  <DT-View
    :inner-style="{
      padding: 0,
      position: 'relative',
      height: '100%'
    }"
    :outer-style="{
      padding: isFull ? 0 : '20px'
    }"
    class="container-wrapper"
  >
    <div class="create-head">
      <div class="create-text">历史版本</div>
      <i class="el-icon-close" style="margin-left: auto" @click="$router.go(-1)"></i>
    </div>
    <div class="editor-content" v-if="activeId === 1">
      <div class="editor-pane">
        <MonacoEditor
          ref="MonacoEditor"
          :init-value.sync="form.pyScript"
          :hint-data="hintData"
          height="100%"
          language="python"
        />
      </div>
      <div class="config-pane">
        <el-table :data="tableData" style="width: 100%">
          <el-table-column prop="updateTime" min-width="150" label="编辑时间"></el-table-column>
          <el-table-column prop="creatorName" min-width="100" label="创建人"></el-table-column>
          <el-table-column prop="version" min-width="100" label="版本"></el-table-column>
          <el-table-column prop="address" min-width="100" label="操作">
            <template slot-scope="scope">
              <span v-if="scope.row.status === '0'">当前版本</span>
              <el-button
                v-else-if="scope.row.status === '2'"
                type="text"
                size="small"
                @click="handleReset(scope.row)"
              >
                还原
              </el-button>
              <el-button
                v-else-if="scope.row.status === '1'"
                type="text"
                size="small"
                @click="handlePreview(scope.row)"
              >
                预览
              </el-button>
              <span v-else>预览中</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="footer-btn">
        <div class="left-btn">
          <el-button type="success" @click="handleRun">试计算</el-button>
        </div>
        <div v-if="isOriginalState" class="right-btn1">
          <el-button @click="cancelEdit"> 取消 </el-button>
        </div>
        <div class="right-btn2" v-else>
          <el-button @click="handleExit"> 退出并恢复为进入前状态 </el-button>
          <el-button el-button type="primary" @click="saveCurAlgorithm">保存当前算法</el-button>
        </div>
      </div>
    </div>
    <AlgorithmIndicator ref="AlgorithmIndicator" />
  </DT-View>
</template>

<script>
import Request from "@/service"
import encryptInd from "@/utils/encryptInd.js"
import MonacoEditor from "@/components/MonacoEditor"
import cloneDeep from "lodash/cloneDeep"
import AlgorithmIndicator from "../Calculate.vue"

export default {
  components: { MonacoEditor, AlgorithmIndicator },

  props: {},
  data() {
    return {
      activeId: 1,
      form: {
        pyScript: ""
      },
      tableData: [],
      originalTableData: [],
      page: {
        total: 0,
        pageSize: 10,
        currentPage: 1
      },
      defaultProps: {
        label: "name",
        children: "zones",
        isLeaf: "leaf"
      },
      supportEnv: [
        {
          label: "基础环境",
          value: "Base"
        },
        {
          label: "数据分析环境",
          value: "DataScience"
        },
        {
          label: "AI环境",
          value: "AIEnv"
        }
      ],
      rowData: [],
      tables: [],
      columns: [],
      editAlgorithmId: null,
      isFull: false
    }
  },
  computed: {
    isOriginalState() {
      return this.tableData.some((item) => item.status === "0")
    },
    hintData() {
      return [...this.tables.map((item) => item.name), ...this.columns.map((item) => item.name)]
    }
  },
  created() {},
  mounted() {
    this.editAlgorithmId = this.$route.query.editAlgorithmId
    this.getDetail()
  },
  watch: {},
  methods: {
    handleReset(row) {
      this.rowData = row
      this.form.pyScript = encryptInd.decrypt(row.pyScript)
      this.tableData.forEach((item) => {
        if (item.id === row.id) {
          item.status = "0"
        } else {
          item.status = "1"
        }
      })
      this.$nextTick(() => {
        this.$refs.MonacoEditor.setInitValue(this.form.pyScript)
      })
    },
    handlePreview(row) {
      this.rowData = row
      this.form.pyScript = encryptInd.decrypt(row.pyScript)
      this.tableData.forEach((element) => {
        if (element.id === this.editAlgorithmId) {
          element.status = "2"
        } else {
          element.status = "1"
        }
      })
      row.status = "3"

      this.$nextTick(() => {
        if (this.$refs.MonacoEditor) {
          this.$refs.MonacoEditor.setInitValue(this.form.pyScript)
        }
      })
    },
    // 获取算法详情
    async getDetail() {
      const res = await Request.algorithm.getAlgorithmlndicatorDetailList(this.editAlgorithmId)
      if (res.code === 200) {
        this.tableData = res.data.map((item) => {
          return {
            ...item,
            status: item.id === Number(this.editAlgorithmId) ? "0" : "1"
          }
        })
        this.originalTableData = cloneDeep(this.tableData)
        const curEditItem = res.data.find((item) => item.id === this.editAlgorithmId)
        this.form.pyScript = encryptInd.decrypt(curEditItem.pyScript)
        // 将 rowData 赋值为当前版本的数据
        this.rowData = curEditItem
        // 更新编辑器内容
        if (curEditItem && curEditItem.pyScript) {
          this.$nextTick(() => {
            if (this.$refs.MonacoEditor) {
              this.$refs.MonacoEditor.setInitValue(this.form.pyScript)
            }
          })
        }
      } else {
        this.$message.error("获取失败")
      }
    },
    cancelEdit() {
      this.$router.go(-1)
    },
    handleExit() {
      // 恢复原始数据
      this.tableData = cloneDeep(this.originalTableData)
      // 恢复当前版本的代码到编辑器
      const currentVersion = this.tableData.find((item) => item.status === "0")
      if (currentVersion) {
        this.form.pyScript = encryptInd.decrypt(currentVersion.pyScript)
        this.$nextTick(() => {
          if (this.$refs.MonacoEditor) {
            this.$refs.MonacoEditor.setInitValue(this.form.pyScript)
          }
        })
      }

      this.$message.success("已恢复为进入前状态")
    },
    // 执行代码
    async handleRun() {
      if (this.$refs.AlgorithmIndicator) {
        this.$refs.AlgorithmIndicator.initData({
          ...this.rowData,
          pyScript: encryptInd.encrypt(this.form.pyScript)
        })
      }
    },
    // 保存当前算法
    async saveCurAlgorithm() {
      // const saveCurParams = this.tableData.find(
      //   item => item.id !== this.editAlgorithmId && item.status === '3'
      // )
      // if (!saveCurParams) {
      //   this.$message.warning('请先选择要保存的版本')
      //   return
      // }
      // this.$router.replace({
      //   name: 'CreateAlgorithmTool',
      //   query: {
      //     mode: 'edit',
      //     // id: this.editId,
      //     id: this.editAlgorithmId,
      //     script: encodeURIComponent(saveCurParams.pyScript),
      //     from: 'history'
      //   }
      // })
      // console.log('保存当前算法', saveCurParams)
    }
  }
}
</script>

<style scoped lang="scss">
.create-head {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  border-bottom: 1px solid #e5e5e5;
  height: 80px;
  background: #fff;
  padding: 20px;
  box-sizing: border-box;

  i {
    font-size: 24px;
    cursor: pointer;

    &:hover {
      color: #1563ff;
    }
  }

  .el-icon-back {
    font-size: 20px;
    padding-right: 16px;
    cursor: pointer;
  }

  .steps {
    width: 150px;
    height: 48px;
    background: #f5f7fa;
    border-radius: 6px;
    margin-left: 40px;
    display: flex;
    align-items: center;
    padding: 0 32px;

    .line {
      width: 168px;
      height: 1px;
      background: #cbced1;
      margin: 0 12px;

      &.active {
        background: #1563ff;
      }
    }

    .step-item {
      display: flex;
      align-items: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #5c646e;

      &.active {
        color: #1563ff;
      }

      i {
        font-size: 19px;
        margin-right: 6px;
      }
    }
  }
}

.step-two {
  height: calc(100% - 100px);
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.editor-content {
  width: 100%;
  display: flex;

  background-color: #f0f2f5;
  box-sizing: border-box;

  position: relative;
  padding: 20px;
  min-height: calc(100vh - 230px);

  background: #fff;
  padding-bottom: 52px;
  display: flex;

  .editor-pane {
    width: calc(100% - 540px);
    padding: 20px;
    background: #2d2d2d;
  }

  .config-pane {
    flex: 0 0 520px;
    overflow-y: auto;
    margin-left: 20px;
  }

  .footer-btn {
    width: 100%;
    display: flex;
    // align-items: center;
    // justify-content: flex-end;
    position: absolute;
    bottom: 0;
    left: 0;
    height: 52px;
    padding-right: 24px;
    box-sizing: border-box;
    border-top: 1px solid #f0f0f0;

    .left-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: calc(100% - 540px);
    }

    .right-btn1 {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 0 0 520px;
    }

    .right-btn2 {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      flex: 0 0 520px;
    }
  }
}

.step-btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 52px;
  padding-right: 24px;
  box-sizing: border-box;
  border-top: 1px solid #f0f0f0;
}

::v-deep .el-dialog__header {
  margin: 0 24px;
  padding: 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #edeff0;
}

::v-deep .el-dialog__body {
  padding: 20px 24px;
}

.inner-box {
  display: flex;
  align-items: center;
  padding: 24px;
  box-sizing: border-box;

  .icon {
    width: 56px;
    height: 56px;
    background: url("~@/assets/images/yjs.png") no-repeat;
    margin-right: 12px;
  }

  .text {
    text-align: left;
    height: 14px;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #323233;
    line-height: 14px;
    margin-bottom: 12px;
  }

  .right {
    width: 500px;
  }

  height: 104px;
  background: #ffffff;
  border-radius: 8px;
}

.step3-box {
  .label-text {
    margin: 20px 0;
  }
}
</style>
<style lang="scss">
.drag-element {
  /* 禁止文本选择 */
  user-select: none;
  /* 禁用默认拖拽效果 */
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

#project_frame
  .model-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .el-tree-node__expand-icon {
  background-color: transparent;
}

#project_frame
  .model-tree
  .el-tree-node.is-current
  > .el-tree-node__content:has(> span.item-style) {
  position: relative;
  background: #f4f7ff !important;
  box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1), 0px 6px 6px -4px rgba(0, 42, 128, 0.12);
  border-radius: 4px;
  border: 1px solid #1563ff;
  cursor: move;

  .el-tree-node__label {
    background: transparent !important;
  }
}

#project_frame .model-tree .el-tree-node {
  border: 1px solid transparent !important;
}

#project_frame {
  .el-tree-node:not(.is-expanded) > .el-tree-node__content:has(> span.item-style) {
    &:hover {
      position: relative;
      background: #f4f7ff !important;
      box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1), 0px 6px 6px -4px rgba(0, 42, 128, 0.12);
      border-radius: 4px;
      border: 1px solid #1563ff;
      cursor: move;

      .el-checkbox {
        background-color: transparent !important;
      }

      .el-tree-node__expand-icon {
        background-color: transparent !important;

        border-top-left-radius: 2px;
        border-bottom-left-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }

      .custom-tree-node,
      .el-tree-node__label {
        background-color: transparent !important;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }
    }
  }

  .el-tree-node.dragging > .el-tree-node__content {
    opacity: 0.2;
  }

  .el-tree-node > .el-tree-node__content {
    &:hover {
      background: #f5f7fa !important;

      > .el-checkbox {
        background-color: transparent !important;
      }

      .el-tree-node__expand-icon {
        background-color: transparent !important;

        border-top-left-radius: 2px;
        border-bottom-left-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }

      .custom-tree-node,
      .el-tree-node__label {
        background-color: transparent !important;

        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }
    }
  }

  .el-tree-node.is-current > .el-tree-node__content {
    background: #f5f7fa;

    > .el-checkbox {
      background-color: transparent !important;
    }

    .el-tree-node__expand-icon {
      background-color: transparent !important;

      border-top-left-radius: 2px;
      border-bottom-left-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }

    .custom-tree-node,
    .el-tree-node__label {
      background-color: transparent !important;

      border-top-right-radius: 2px;
      border-bottom-right-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }
  }
}

#project_frame .el-table.sqlIndicator-table td,
#project_frame .el-table.sqlIndicator-table th {
  padding: 0;
}
</style>
