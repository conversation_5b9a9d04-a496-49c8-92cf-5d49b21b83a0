<template>
  <dt-single-page-view
    class="cardList"
    :inner-style="{ textAlign: 'left' }"
    :show="show"
    v-loading="loading"
    element-loading-background="rgba(0, 0, 0, 0)"
  >
    <!-- <DT-Form
      v-model="data"
      type="search"
      :show-button="false"
      :render="render"
    /> -->
    <el-button type="primary" icon="el-icon-plus" @click="handleOnAdd"> 新建 </el-button>
    <el-table :data="filterTableData" style="width: 100%">
      <el-table-column prop="name" label="数据源名称" min-width="150"></el-table-column>
      <el-table-column
        prop="type"
        label="数据源类型"
        align="center"
        min-width="120"
      ></el-table-column>
      <el-table-column
        prop="updateTime"
        label="最近同步时间"
        align="center"
        min-width="120"
      ></el-table-column>
      <el-table-column prop="description" label="描述" min-width="150"></el-table-column>
      <!-- <el-table-column prop="description" label="描述" align="center">
      </el-table-column> -->
      <el-table-column prop="operate" label="操作">
        <template slot-scope="{ row }">
          <el-link :underline="false" type="primary" @click="tableConfig(row)">
            配置表归属
          </el-link>
          <!-- <el-link
            :underline="false"
            type="primary"
            @click="infoLine(row.id, row.name)"
          >
            查看
          </el-link>
          <div class="el-divider el-divider--vertical" /> -->

          <el-link
            :underline="false"
            type="primary"
            @click="uploadLine(row.id)"
            v-if="row.type === 'csv'"
          >
            上传
          </el-link>
          <div class="el-divider el-divider--vertical" v-if="row.type === 'csv'" />
          <el-link :underline="false" type="primary" @click="updLine(row.id)"> 修改 </el-link>
          <div class="el-divider el-divider--vertical" />
          <el-link :underline="false" type="primary" @click="syncMetaData(row)">
            同步数据源
          </el-link>
          <div class="el-divider el-divider--vertical" />
          <el-link :underline="false" type="primary" @click="delLine(row.id)"> 删除 </el-link>
        </template>
      </el-table-column>
    </el-table>
    <!-- 配置表归属的右侧弹框 -->
    <el-drawer :visible.sync="configTableVisible" direction="rtl" size="50%" class="config-drawer">
      <div slot="title" class="drawer-title">配置表归属</div>
      <div class="drawer-container">
        <div class="config-table">
          <CommonTable
            ref="tableOwnershipTable"
            :page.sync="page"
            id="xh"
            :table-data="configTableData"
            :table-columns.sync="tableColumns"
            @handleSortChange="sortChange"
            @handleSetting="handleSetting"
            :show-batch-setting="true"
          >
            <template #organization="{ row }">
              <el-cascader
                v-model="row.organization"
                :props="{
                  value: 'value',
                  label: 'label',
                  children: 'children',
                  multiple: false,
                  lazy: true,
                  lazyLoad: lazyLoadOrganization
                }"
                placeholder="请选择组织机构"
                size="mini"
                clearable
                style="width: 98%"
              >
              </el-cascader>
            </template>
          </CommonTable>
        </div>
        <!-- 按钮操作区域放到最底部 -->
        <div class="drawer-footer">
          <el-button @click="configTableVisible = false">取消</el-button>
          <el-button type="primary" @click="saveTableOwnership">确定</el-button>
        </div>
      </div>
    </el-drawer>
    <el-dialog title="上传 CSV 文件" :visible.sync="importVisible" width="576px">
      <el-form :model="formDialog" label-width="120px">
        <el-form-item label="表名" prop="tableName">
          <el-input v-model="formDialog.tableName" placeholder="请输入" class="my_with"></el-input>
        </el-form-item>
        <el-form-item label="主键" prop="primaryKeys">
          <el-input
            v-model="formDialog.primaryKeys"
            placeholder="请输入"
            class="my_with"
          ></el-input>
        </el-form-item>
        <el-form-item label="索引值" prop="indexKeys">
          <el-input v-model="formDialog.indexKeys" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="导入方式" prop="mode">
          <el-radio-group v-model="formDialog.mode">
            <el-radio :label="item.value" v-for="(item, key) in importList" :key="key">
              {{ item.label }}
            </el-radio>
          </el-radio-group>
          <el-popover placement="left" title="" width="360" trigger="hover">
            <div class="content">
              <div class="item">新增：首次上传文件到新表</div>
              <div class="item">替换：保持原有表结构不变，清空原有表数据后上传</div>
              <div class="item">追加：保持原有表结构不变，保持原有表数据并追加</div>
              <div class="item">覆盖：重建表结构并替换数据</div>
            </div>
            <el-link
              slot="reference"
              class="el-icon-warning-outline"
              style="font-size: 16px; margin-left: 12px"
            ></el-link>
          </el-popover>
        </el-form-item>

        <el-form-item label="上传" prop="file">
          <el-upload
            action="#"
            :show-file-list="false"
            style="display: contents"
            accept=".csv"
            :before-upload="beforeUpload"
          >
            <el-button
              type="
          primary"
              icon="el-icon-upload2"
            >
              {{ formDialog.file ? "重新长传" : "点击上传" }}
            </el-button>
            <el-popover placement="left" title="" width="260" trigger="hover">
              <div class="content">
                <div class="item">
                  csv文件第一行必须含有表头作为字段，第二行为表头字段的数据类型格式，目前只支持jdbc数据库导入
                </div>
              </div>
              <el-link
                slot="reference"
                class="el-icon-warning-outline"
                style="font-size: 16px; margin-left: 12px"
              ></el-link>
            </el-popover>
            <div class="fileInfo el-icon-document" v-if="formDialog.file">
              {{ formDialog.file.name }}
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="importVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitCSV()">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="批量配置"
      :visible.sync="batchConfigVisible"
      width="500px"
      :append-to-body="true"
    >
      <el-form label-width="120px">
        <el-form-item label="已选择表数量：">
          <span>{{ batchConfigSelection.length }}个</span>
        </el-form-item>
        <el-form-item label="选择组织机构：">
          <el-cascader
            v-model="batchOrganization"
            :props="{
              value: 'value',
              label: 'label',
              children: 'children',
              multiple: false,
              lazy: true,
              lazyLoad: lazyLoadBatchOrganization
            }"
            placeholder="请选择组织机构"
            clearable
            style="width: 100%"
          >
          </el-cascader>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="batchConfigVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmBatchConfig">确 定</el-button>
      </span>
    </el-dialog>
  </dt-single-page-view>
</template>
<script>
import Request from "@/service"
import Axios from "axios"
import CommonTable from "../component/CommonTable"
export default {
  name: "data-source-list",
  components: { CommonTable },
  data() {
    return {
      show: false,
      loading: true,
      tableData: [],
      data: {},
      render: [
        {
          type: "input",
          key: "search",
          label: "库名称",
          props: {
            placeholder: "请输入库名称"
          }
        }
      ],
      importVisible: false,
      configTableVisible: false,
      batchConfigVisible: false,
      batchOrganization: "", // 批量配置选择的组织机构
      batchConfigSelection: [], // 批量配置选中的表
      currentDataSource: null, // 当前数据源
      tableList: [], // 表列表数据
      page: {
        total: 0,
        pageSize: 10,
        currentPage: 1
      },
      configTableData: [],
      tableColumns: [
        {
          prop: "tableName",
          label: "表名",
          minWidth: 120,
          visible: true,
          sortable: false,
          fixed: false
        },
        {
          prop: "tableCnName",
          label: "表中文名",
          minWidth: 120,
          visible: true,
          sortable: false,
          fixed: false
        },
        {
          prop: "organization",
          label: "组织机构",
          minWidth: 150,
          visible: true,
          scopedSlots: { customRender: "organization" },
          sortable: false,
          fixed: false
        }
      ],
      formDialog: {
        sourceId: "",
        mode: 0,
        tableName: "",
        primaryKeys: "",
        indexKeys: "",
        file: ""
      },
      importList: [
        {
          label: "新增",
          value: 0
        },
        {
          label: "替换",
          value: 1
        },
        {
          label: "追加",
          value: 2
        },
        {
          label: "覆盖",
          value: 3
        }
      ]
    }
  },
  created() {
    setTimeout(() => {
      this.show = true
      this.loading = false
    }, 0.5 * 1000)
    this.initData()
  },
  computed: {
    filterTableData() {
      return this.tableData.filter(
        (data) =>
          !this.data.search || data.name.toLowerCase().includes(this.data.search.toLowerCase())
      )
    }
  },
  methods: {
    // 批量配置弹框中的级联选择器懒加载数据
    lazyLoadBatchOrganization(node, resolve) {
      const { level } = node

      // 模拟异步请求数据
      setTimeout(() => {
        let nodes = []

        if (level === 0) {
          // 第一级节点
          nodes = [
            {
              value: "org1",
              label: "组织机构1",
              leaf: false // false表示还有子节点
            },
            {
              value: "org2",
              label: "组织机构2",
              leaf: false
            },
            {
              value: "org3",
              label: "组织机构3",
              leaf: true // true表示没有子节点
            }
          ]
        } else if (level === 1) {
          // 第二级节点
          const parentNode = node.data
          if (parentNode.value === "org1") {
            nodes = [
              {
                value: "org1-1",
                label: "子机构1-1",
                leaf: true
              },
              {
                value: "org1-2",
                label: "子机构1-2",
                leaf: false
              }
            ]
          } else if (parentNode.value === "org2") {
            nodes = [
              {
                value: "org2-1",
                label: "子机构2-1",
                leaf: true
              }
            ]
          }
        } else if (level === 2) {
          // 第三级节点
          const parentNode = node.data
          if (parentNode.value === "org1-2") {
            nodes = [
              {
                value: "org1-2-1",
                label: "子机构1-2-1",
                leaf: true
              }
            ]
          }
        }

        // 调用resolve回调函数，传入子节点数据
        resolve(nodes)
      }, 300) // 模拟网络延迟
    },
    // 懒加载组织机构数据
    lazyLoadOrganization(node, resolve) {
      const { level } = node

      // 模拟异步请求数据
      setTimeout(() => {
        let nodes = []

        if (level === 0) {
          // 第一级节点
          nodes = [
            {
              value: "org1",
              label: "组织机构1",
              leaf: false // false表示还有子节点
            },
            {
              value: "org2",
              label: "组织机构2",
              leaf: false
            },
            {
              value: "org3",
              label: "组织机构3",
              leaf: true // true表示没有子节点
            }
          ]
        } else if (level === 1) {
          // 第二级节点
          const parentNode = node.data
          if (parentNode.value === "org1") {
            nodes = [
              {
                value: "org1-1",
                label: "子机构1-1",
                leaf: true
              },
              {
                value: "org1-2",
                label: "子机构1-2",
                leaf: false
              }
            ]
          } else if (parentNode.value === "org2") {
            nodes = [
              {
                value: "org2-1",
                label: "子机构2-1",
                leaf: true
              }
            ]
          }
        } else if (level === 2) {
          // 第三级节点
          const parentNode = node.data
          if (parentNode.value === "org1-2") {
            nodes = [
              {
                value: "org1-2-1",
                label: "子机构1-2-1",
                leaf: true
              }
            ]
          }
        }

        // 调用resolve回调函数，传入子节点数据
        resolve(nodes)
      }, 300) // 模拟网络延迟
    },
    sortChange() {},
    // 批量配置
    handleSetting(selection) {
      this.batchConfigSelection = selection
      if (this.batchConfigSelection.length === 0) {
        this.$message.error("请选择要配置的数据")
        return
      }
      console.log("handleSetting", this.batchConfigSelection)
      this.batchConfigVisible = true
    },
    confirmBatchConfig() {
      if (!this.batchOrganization) {
        this.$message.warning("请选择组织机构")
        return
      }

      // 构造要提交的数据
      const payload = {
        selectedCount: this.batchConfigSelection.length,
        organization: this.batchOrganization,
        tableIds: this.batchConfigSelection.map((item) => item.id) // 假设有 id 字段
      }

      console.log("批量配置数据:", payload)

      // 这里可以调用接口保存配置
      // this.$emit("handleBatchConfig", payload)

      // 关闭弹框
      this.batchConfigVisible = false
      // 重置选择
      this.batchOrganization = ""

      this.$message.success("配置成功")
    },
    handleOnAdd() {
      this.$emit("click", { com: "edit", opt: "add", data: { id: "" } })
    },
    infoLine(id, name) {
      // 修改
      this.$emit("click", {
        com: "info",
        opt: "info",
        data: { id: id, name: name }
      })
    },
    updLine(id) {
      // 修改
      this.$emit("click", { com: "edit", opt: "edit", data: { id: id } })
    },
    delLine(id) {
      this.$confirm("此操作将删除选中数据, 是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.loading = true
        Request.source
          .delete({ id: id })
          .then((res) => {
            this.$message.success(res.data)
          })
          .catch(() => {
            this.$message.error("操作失败")
          })
          .finally(() => {
            this.initData()
          })
      })
    },
    async syncMetaData(row) {
      this.$confirm("此操作将同步数据源, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(async () => {
          await Request.source.syncMetaData({
            id: row.id
          })
          this.$message.success("同步成功")
        })
        .catch(() => {})
    },
    tableConfig(row) {
      console.log(row, this.filterTableData, this.tableColumns, "tableConfig")
      this.currentDataSource = row
      this.configTableVisible = true
      // 这里应该调用接口获取该数据源下的表列表
      this.loadTableList(row.id)
    },
    // 保存表归属配置
    saveTableOwnership() {
      const selectedRows = this.$refs.tableOwnershipTable.selection
      console.log("选中的行:", selectedRows)
      console.log("表列表:", this.tableList)

      // 构造要提交的数据
      const payload = selectedRows.map((row) => ({
        tableName: row.tableName,
        organization: row.organization
      }))
      console.log(payload, "payload")

      // 调用接口保存配置
    },
    // 加载表列表
    loadTableList(sourceId) {
      console.log(sourceId)
      // 模拟数据，实际应该调用接口获取
      this.configTableData = [
        {
          tableName: "table1",
          tableCnName: "表1中文名",
          organization: ""
        },
        {
          tableName: "table2",
          tableCnName: "表2中文名",
          organization: ""
        },
        {
          tableName: "table3",
          tableCnName: "表3中文名",
          organization: ""
        }
      ]
    },
    uploadLine(id) {
      this.formDialog.sourceId = id
      this.formDialog.mode = 0
      this.formDialog.tableName = ""
      this.formDialog.primaryKeys = ""
      this.formDialog.indexKeys = ""
      this.formDialog.file = ""
      this.importVisible = true
    },
    initData() {
      this.loading = true
      Request.source
        .getAll()
        .then((res) => {
          this.tableData = res.data
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    beforeUpload(file) {
      console.log("file:", file)
      this.formDialog.file = file
    },
    submitCSV() {
      let fd = new FormData()
      fd.append("file", this.formDialog.file)
      fd.append("sourceId", this.formDialog.sourceId)
      fd.append("mode", this.formDialog.mode)
      fd.append("tableName", this.formDialog.tableName)
      fd.append("primaryKeys", this.formDialog.primaryKeys)
      fd.append("indexKeys", this.formDialog.indexKeys)

      Axios({
        method: "POST",
        url: "/api/dds-server-bi/bi/source/" + this.formDialog.sourceId + "/uploadcsv",
        headers: { ...this.$utils.auth.getAdminheader() },
        data: fd
      }).then((res) => {
        if (res.data.code === 200) {
          this.$message.success(res.data.data ? res.data.data : "上传成功")
          setTimeout(() => {
            this.importVisible = false
          }, 1500)
        } else {
          this.$message.error(res.data.message ? res.data.message : "上传失败，请稍后重试")
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.cardList {
  .title {
    margin-top: 100px;
  }

  .buttons {
    margin-top: 50px;
  }
  .fileInfo {
    display: block;
    margin-top: 10px;
    height: 26px;
    line-height: 26px;
  }

  .config-drawer {
    .drawer-title {
      font-size: 18px;
      font-weight: 700;
      color: #333333;
      padding-bottom: 12px;
      border-bottom: 1px solid #ebebeb;
    }

    ::v-deep .el-drawer__body {
      padding: 0;
    }

    .drawer-container {
      display: flex;
      flex-direction: column;
      height: 100%;

      .config-table {
        flex: 1;
        padding: 20px;
        overflow: auto;
      }

      .drawer-footer {
        padding: 12px 20px;
        text-align: right;
        border-top: 1px solid #e8e8e8;
        background: #fff;
      }
    }

    ::v-deep .el-drawer__title {
      font-size: 16px;
      font-weight: bold;
    }
  }
}
</style>
