vue
<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="600px"
    custom-class="rule-rollback-dialog"
    :show-close="false"
    append-to-body
  >
    <!-- 顶部标题栏 -->
    <template #title>
      <div class="dialog-header">
        <span class="title">变更影响</span>
        <i class="el-icon-close close-btn" @click="dialogVisible = false"></i>
      </div>
    </template>

    <!-- 主要内容区域 -->
    <div class="dialog-content">
      <section class="scope-section">
        <h3 class="section-title">
          如果对本指标进行修改，将影响以下场景，保存后请检查结果是否正确
        </h3>
        <div class="info-group">
          <div class="info-item">
            <span class="label">指标：</span>
            <div
              class="value-scrollable"
              :class="{
                scrollable:
                  scopeList.filter(
                    item =>
                      item.currentType !== 'chart' &&
                      item.currentType !== 'display'
                  ).length > 0
              }"
            >
              <span class="value">
                {{
                  scopeList
                    .filter(
                      item =>
                        item.currentType !== 'chart' &&
                        item.currentType !== 'display'
                    )
                    .map(item => item.currentName)
                    .join('; ') || '-'
                }}
              </span>
            </div>
          </div>
          <div class="info-item">
            <span class="label">图表：</span>
            <div
              class="value-scrollable"
              :class="{
                scrollable:
                  scopeList.filter(item => item.currentType === 'chart')
                    .length > 0
              }"
            >
              <span class="value">
                {{
                  scopeList
                    .filter(item => item.currentType === 'chart')
                    .map(item => item.currentName)
                    .join('; ') || '-'
                }}
              </span>
            </div>
          </div>
          <div class="info-item">
            <span class="label">驾驶舱：</span>
            <div
              class="value-scrollable"
              :class="{
                scrollable:
                  scopeList.filter(item => item.currentType === 'display')
                    .length > 0
              }"
            >
              <span class="value">
                {{
                  scopeList
                    .filter(item => item.currentType === 'display')
                    .map(item => item.currentName)
                    .join('; ') || '-'
                }}
              </span>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button class="action-btn" @click="dialogVisible = false">
          取消
        </el-button>
        <el-button
          type="primary"
          class="action-btn"
          @click="handleRollbackSubmit"
        >
          确认变更
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      dialogVisible: false,
      scopeList: [],
      tempCallBack: null
    }
  },
  methods: {
    // 获取影响范围
    async isAffectSpace({ indCode, indType }, callBack) {
      const { data } = await this.$httpBi.api.paramGet(
        '/IndUpdRec/getIndRollBackScope',
        {
          indCode,
          indType
        }
      )
      return new Promise(resolve => {
        this.scopeList = data || []
        if (this.scopeList.length) {
          this.dialogVisible = true
          this.tempCallBack = callBack
          resolve(false)
        } else {
          resolve(true)
          callBack()
        }
      })
    },
    handleRollbackSubmit() {
      this.tempCallBack()
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-dialog__header {
  padding: 0;
  border-bottom: none !important ;
}
::v-deep .el-dialog__body {
  padding: 20px;
}
.rule-rollback-dialog {
  background: #f5f5f5;

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid #e8e8e8;

    .title {
      color: #333;
      font-size: 16px;
      font-weight: 600;
    }

    .close-btn {
      color: #999;
      cursor: pointer;
      font-size: 18px;

      &:hover {
        color: #666;
      }
    }
  }

  .dialog-content {
    .section-title {
      height: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #222222;
      line-height: 14px;
      text-align: left;
      font-style: normal;
      margin-bottom: 12px;
    }

    .scope-section {
      margin-bottom: 24px;
      .info-group {
        width: 100%;
        // 移除固定高度，让内容自适应
        background: #f5f7fa;
        border-radius: 8px;
        padding: 16px;
        box-sizing: border-box;
      }
      .info-item {
        display: flex;
        margin-bottom: 24px; // 增大行间距
        font-size: 13px;

        .label {
          color: #666;
          width: 65px;
          flex-shrink: 0;
          padding: 4px;
        }

        .value-scrollable {
          flex: 1;
          max-height: 80px; // 设置固定高度
          overflow-y: auto; // 超出高度显示滚动条
          box-sizing: border-box;

          // 滚动条样式
          &::-webkit-scrollbar {
            width: 6px;
          }
          &::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
          }
          &::-webkit-scrollbar-thumb {
            background: #ccc;
            border-radius: 3px;
          }
          &::-webkit-scrollbar-thumb:hover {
            background: #999;
          }
        }

        .value {
          color: #333;
          white-space: pre-line; // 保留换行符
          line-height: 1.6; // 增加行高
        }
      }
    }

    .rule-section {
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
      }

      .compare-table {
        // border: 1px solid #e8e8e8;
        border-radius: 8px;
        background: #f5f7fa;
        padding: 16px;
        box-sizing: border-box;

        // &::-webkit-scrollbar {
        //   width: 8px;
        //   height: 8px;
        // }
        // &::-webkit-scrollbar-track {
        //   background: #f5f7fa;
        // }
        // &::-webkit-scrollbar-thumb {
        //   background: #e8e8e8;
        //   border-radius: 4px;
        // }

        .table-header,
        .table-row {
          display: flex;
          padding: 12px;

          .col {
            flex: 1;
            color: #333;
            font-size: 13px;
            line-height: 16px;
            margin-right: 10px;

            // &:not(:last-child) {
            //   border-right: 1px solid #e8e8e8;
            // }
          }
        }
        .table-content {
          overflow: auto;
          height: 300px;
        }

        // .diff {
        //   color: #f56c6c;
        //   background: #fef0f0;
        // }
      }
    }
  }

  .dialog-footer {
    padding: 16px 0 0;
    border-top: 1px solid #e8e8e8;
    text-align: right;

    .action-btn {
      margin-left: 12px;
      padding: 8px 20px;
      border-radius: 4px;
    }
  }
}
.indicator-attr-dialog {
  background: #fff;
  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid #e8e8e8;
    .title {
      color: #333;
      font-size: 16px;
      font-weight: 600;
    }
    .close-btn {
      color: #999;
      cursor: pointer;
      font-size: 18px;
      &:hover {
        color: #666;
      }
    }
  }
  .attr-content {
    padding: 20px 24px 0 24px;
    .attr-row {
      margin-bottom: 14px;
      font-size: 14px;
      line-height: 18px;
      .attr-label {
        color: #666;
        font-weight: 500;
        text-align: right;
        padding-right: 8px;
      }
    }
  }
  .dialog-footer {
    padding: 16px 0 0;
    border-top: 1px solid #e8e8e8;
    text-align: right;
    .action-btn {
      margin-left: 12px;
      padding: 8px 20px;
      border-radius: 4px;
    }
  }
}
</style>
