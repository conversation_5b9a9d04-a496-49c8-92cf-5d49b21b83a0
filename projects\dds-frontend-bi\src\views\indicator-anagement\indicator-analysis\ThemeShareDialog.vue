<template>
  <el-dialog
    title="主题分享"
    :visible.sync="visibleInner"
    width="700px"
    append-to-body
    :close-on-click-modal="false"
    @close="$emit('update:visible', false)"
  >
    <div>
      <el-form :model="form" label-width="150px">
        <el-form-item label="分享方式">
          <el-radio-group v-model="form.shareType">
            <el-radio :label="1">按角色分享</el-radio>
            <el-radio :label="2">按管理员分享</el-radio>
          </el-radio-group>
        </el-form-item>
        <template v-if="form.shareType === 1">
          <el-form-item label="选择角色">
            <el-select
              v-model="form.shareToIds"
              multiple
              collapse-tags
              style="width: 100%"
              placeholder="请选择角色"
              @change="handleRoleChange"
            >
              <el-option
                v-for="r in roleList"
                :key="r.roleId"
                :label="r.roleName"
                :value="r.roleId"
              />
            </el-select>
          </el-form-item>
          <!-- 当前符合条件管理员 -->

          <el-form-item label="当前符合条件管理员" label-width="150px">
            <el-table :data="tableData" style="width: 100%" border>
              <el-table-column prop="username" label="管理员姓名" width="180">
              </el-table-column>
              <el-table-column prop="realName" label="管理员账号" width="180">
              </el-table-column>
              <el-table-column prop="username" label="角色"> </el-table-column>
            </el-table>
          </el-form-item>
        </template>

        <el-form-item v-else label="选择管理员"> </el-form-item>
      </el-form>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="$emit('update:visible', false)">取 消</el-button>
      <el-button type="primary" :loading="saving" @click="handleSave"
        >保 存</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import Request from '@/service'

export default {
  name: 'ThemeShareDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    themeId: {
      type: [Number, String],
      default: null
    }
  },
  data () {
    return {
      visibleInner: this.visible,
      saving: false,
      roleList: [],
      form: {
        shareType: 1,
        shareToIds: []
      },
      treeProps: {
        label: 'title',
        children: 'children',
        isLeaf: data => data.type !== 1
      },
      defaultExpandedKeys: []
    }
  },
  computed: {
    selectedUsers () {
      const tree = this.$refs.userTree
      if (!tree) return []
      // 仅取叶子节点（用户）
      const checked = tree.getCheckedNodes(true)
      return checked.filter(item => item.type !== 1)
    }
  },
  watch: {
    visible: {
      immediate: true,
      handler (val) {
        this.visibleInner = val
        if (val) {
          this.init()
        }
      }
    }
  },
  methods: {
    async init () {
      // 角色列表
      const { data } = await Request.upms.paramGet('adm/security/role/select')
      this.roleList = data
      // 初始化组织根节点
      if (this.form.shareType === 2) {
        this.$nextTick(() => {
          this.ensureRootLoaded()
        })
      }
    },
    // 根据角色id查询管理员
    async handleRoleChange () {
      const { data } = await Request.upms.paramGet(
        'adm/security/user/queryUserList',
        {
          username: ''
        }
      )
      this.tableData = data
    },
    async ensureRootLoaded () {
      // 触发加载根
      const tree = this.$refs.userTree
      if (!tree) return
      if (!tree.root.childNodes.length) {
        tree.load(null, () => {})
      }
    },
    async loadUsers (node, resolve) {
      // node.level === 0 -> root
      try {
        const parentId = node && node.data ? node.data.id : ''
        const { data } = await Request.api.orgMember({ id: parentId })
        const list = (data || []).map(item => ({
          ...item,
          id: item.id,
          title: item.name,
          // type: 1 组织/部门, 其他为人员
          leaf: item.type !== 1
        }))
        resolve(list)
      } catch (e) {
        resolve([])
      }
    },
    async handleSave () {
      const payload = {
        themeId: this.themeId,
        shareType: this.form.shareType,
        shareToIds:
          this.form.shareType === 1
            ? this.form.shareToIds
            : this.selectedUsers.map(u => u.id)
      }
      if (!payload.themeId) {
        this.$message.error('缺少主题ID')
        return
      }
      if (!payload.shareToIds || payload.shareToIds.length === 0) {
        this.$message.warning('请选择分享对象')
        return
      }
      this.saving = true
      try {
        await this.$httpBi.analyzeTheme.shareTheme(payload)
        this.$message.success('分享成功')
        this.$emit('success')
        this.$emit('update:visible', false)
      } finally {
        this.saving = false
      }
    }
  }
}
</script>

<style scoped lang="scss">
.user-tree {
  width: 360px;
  height: 300px;
  padding: 8px 12px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: auto;
}
.selected-box {
  flex: 1;
  height: 300px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 8px 12px;
  overflow: auto;
}
.selected-title {
  font-weight: 500;
  margin-bottom: 6px;
}
.selected-list {
  padding-left: 12px;
}
</style>
