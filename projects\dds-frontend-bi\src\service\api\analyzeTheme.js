// 复合指标
import service from "../base"
import config from "../config"

// const config = {
//   VUE_MODULE_DDS_BI: "/dds-server-bi-zqz/"
// }
export default {
  // 获取指标域展示列表
  getIndicatorGroup(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/analyzeTheme/groups",
      method: "get",
      params: data
    })
  },
  // 获取指标列表
  getIndicatorList(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/analyzeTheme/inds",
      method: "post",
      data
    })
  },
  // 获取公共维度
  getPublicDim(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/analyzeTheme/selectPubDim",
      method: "post",
      data
    })
  },
  // 获取汇总表
  getSummaryTable(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/analyzeTheme/calculate",
      method: "post",
      data
    })
  },
  checkAnalyzeTheme(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/analyzeTheme/checkName",
      method: "post",
      data
    })
  },

  // 获取分析主题
  getAnalyzeTheme(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/analyzeTheme/page",
      method: "get",
      params
    })
  },
  // 删除主题
  deleteAnalyzeTheme(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/analyzeTheme/delete",
      method: "post",
      data
    })
  },
  // 保存主题
  saveAnalyzeTheme(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/analyzeTheme/save",
      method: "post",
      data
    })
  },
  // 加载主题
  loadAnalyzeTheme(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/analyzeTheme/detail",
      method: "get",
      params
    })
  },
  // 主题数据导出
  exportAnalyzeTheme(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/analyzeTheme/export",
      method: "get",
      params
    })
  },
  // 主题分享
  shareTheme(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/analyzeTheme/share",
      method: "post",
      data
    })
  }
}
