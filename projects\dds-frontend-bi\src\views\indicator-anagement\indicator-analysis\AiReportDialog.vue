<template>
  <el-dialog
    title="生成AI报表"
    :visible.sync="visible"
    width="1000px"
    :before-close="handleClose"
    class="ai-report-dialog"
  >
    <div class="dialog-content">
      <!-- 左侧：选择上报模板 -->
      <div class="left-section">
        <div class="section-title">选择上报模板</div>

        <!-- 搜索框 -->
        <div class="search-box">
          <el-input
            v-model="fileName"
            placeholder="请输入指标名称"
            prefix-icon="el-icon-search"
            clearable
            @input="handleSearch"
          />
        </div>

        <!-- 模板分类标签 -->
        <div class="template-tabs">
          <div class="template-list">
            <div class="template-grid">
              <div
                v-for="(template, index) in templates"
                :key="index"
                class="template-item"
                :class="{ selected: selectedTemplateId === template.id }"
                @click="toggleTemplateSelection(template)"
              >
                <div class="template-icon">
                  <img src="@/assets/images/muban.png" alt="模板图标" />
                </div>
                <div class="template-content">
                  <div class="template-title">{{ template.fileName }}</div>
                  <span
                    class="template-preview"
                    @click.stop="previewTemplate(template)"
                  >
                    预览
                  </span>
                  <!-- <div class="template-description">
                    {{ template.desc }}
                  </div> -->
                </div>
                <div
                  class="selection-indicator"
                  v-if="selectedTemplateId === template.id"
                >
                  <i class="el-icon-check"></i>
                </div>
              </div>
            </div>
            <!-- 分页器 -->
          </div>
          <div
            class="template-pagination"
            v-if="templatePagination.total > templatePagination.pageSize"
          >
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="templatePagination.currentPage"
              :page-sizes="[20, 20, 50, 100]"
              :page-size="templatePagination.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="templatePagination.total"
              small
            ></el-pagination>
          </div>
        </div>
      </div>

      <!-- 右侧：上传新模板 -->
      <div class="right-section">
        <div class="section-title">上传新模板</div>

        <div class="upload-area">
          <div v-if="excelName" class="custom-file-tag">
            <div class="file-info">
              <i class="el-icon-document"></i>
              <span class="file-name">{{ excelName }}</span>
            </div>
            <i class="el-icon-close close-btn" @click="handleColseTag"></i>
          </div>
          <el-upload
            action="/api/dds-server-main/ftp/upload"
            :show-file-list="false"
            v-else
            style="display: contents"
            accept=".xls,.XLS,.xlsx,.XLSX"
            :headers="$utils.auth.getAdminheader()"
            :on-success="handleSuccess"
            :on-error="handleError"
            :http-request="customUpload"
            drag
          >
            <!-- 点击下载导入模板 -->
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">文件上传或拖拽上传</div>
            <div class="el-upload__text">本地文件 (.csv、.xlsx、.xls)</div>
          </el-upload>
        </div>
      </div>
    </div>

    <el-dialog
      :title="previewTemplateItem.desc"
      :visible.sync="previewExcel"
      width="80%"
      append-to-body
      @close="previewExcel = false"
    >
      <vue-office-excel
        :src="previewTemplateItem.url + previewTemplateItem.filePath"
        style="height: 100vh"
        @rendered="renderedHandler"
        @error="errorHandler"
      />
    </el-dialog>

    <el-dialog
      title="生成中"
      class="generate-dialog"
      :visible.sync="showProgressDialog"
      width="80%"
      append-to-body
      @close="handleCloseProgress"
    >
      <!-- 思考过程 -->
      <div class="think-process">
        <svg-icon class="icon" @click="drawer = true" icon-class="think" />
        思考过程
      </div>
      <div id="messageCompBox">
        <div
          class="markdown-body dark"
          v-html="
            markDownText ? markdownRender.render(markDownText) : '思考中...'
          "
        ></div>
        <!-- 思考动画 -->
        <div v-if="isThinking" class="thinking-animation">
          报表数据生成中,请耐心等待
          <div></div>
          <div></div>
          <div></div>
        </div>
      </div>
    </el-dialog>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button
        type="primary"
        @click="handleConfirm"
        :loading="confirmLoading"
      >
        生成
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import axios from 'axios'
import VueOfficeExcel from '@vue-office/excel'
import MarkdownIt from 'markdown-it'
import '@vue-office/excel/lib/index.css'
import debounce from 'lodash/debounce' // 防抖函数
export default {
  name: 'AiReportDialog',
  components: {
    VueOfficeExcel
  },
  props: {
    rowData: {
      type: Array,
      default: () => []
    },
    themeName: {
      type: String,
      default: '汇总表'
    },
    headKeys: {
      type: Array,
      default: () => []
    },
    useDims: {
      type: Array,
      default: () => []
    },
    analyzeDims: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      visible: false,
      fileName: '',
      activeTab: 'statistical',
      // 初始化
      markdownRender: new MarkdownIt({
        html: true,
        linkify: true,
        typographer: true
      }),

      fileList: [],
      confirmLoading: false,
      uploadAction: '#', // 实际使用时替换为真实的上传地址
      acceptedFileTypes: '.csv,.xlsx,.xls',
      selectedTemplateId: null,
      selectedTemplateItem: null,
      // 模板数据
      templates: [],
      // 分页相关数据
      templatePagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      previewExcel: true,
      previewTemplateItem: {
        desc: '',
        excelUrl: ''
      },
      excelPreviewData: [], // 新增：本地 Excel 预览数据
      uploadPercentage: 0, // 上传进度百分比
      excelName: '',
      // 进度弹窗相关
      showProgressDialog: false,
      progressPercentage: 0,
      isGenerating: false,
      controller: new AbortController(),
      markDownText: '',
      isThinking: false,
      thinkingDots: ''
    }
  },
  computed: {
    filteredTemplates () {
      if (!this.searchKeyword) {
        return this.templates.filter(
          template => template.category === this.activeTab
        )
      }
      return this.templates.filter(
        template =>
          template.category === this.activeTab &&
          template.title
            .toLowerCase()
            .includes(this.searchKeyword.toLowerCase())
      )
    }
  },
  watch: {
    useDims: {
      handler (newVal) {
        console.log(newVal, '444')
      }
    },
    headKeys: {
      handler (newVal) {
        console.log(newVal, '555')
      }
    },
    rowData: {
      handler (newVal) {
        console.log(newVal, '666')
      }
    }
  },
  methods: {
    customUpload (file) {
      const formData = new FormData()
      formData.append('file', file.file)
      formData.append('type', '2') // 添加额外参数

      axios
        .post('/api/dds-server-main/ftp/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            ...this.$utils.auth.getAdminheader()
          }
        })
        .then(response => {
          this.handleSuccess(response.data)
        })
        .catch(error => {
          console.error('上传失败:', error)
        })
    },
    // 打开弹窗
    open () {
      this.visible = true
      this.resetData()
    },

    // 获取模板列表
    async getTemplates () {
      const { data } = await this.$httpBi.gpt.getTemplates({
        currentPage: this.templatePagination.currentPage,
        pageSize: this.templatePagination.pageSize,
        fileName: this.fileName
      })
      this.templates = data.list
      this.templatePagination.total = data.totalCount
    },
    // 关闭弹窗
    handleClose () {
      this.visible = false
      this.resetData()
    },

    // 重置数据
    resetData () {
      this.searchKeyword = ''
      this.selectedTemplateId = null
      this.selectedTemplateItem = null
      this.fileList = []
      this.confirmLoading = false
      this.excelUrl = ''
      this.excelName = ''
      this.previewExcel = false
      this.excelPreviewData = [] // 重置预览数据
      this.isGenerating = false
      // 重置分页数据
      this.templatePagination.currentPage = 1
      this.templatePagination.total = 0
      this.getTemplates()
    },
    previewTemplate (template) {
      this.previewTemplateItem = template
      this.previewExcel = true
    },
    // 搜索处理
    handleSearch: debounce(function () {
      this.templatePagination.currentPage = 1
      this.getTemplates()
      // 搜索逻辑已在computed中处理
    }, 300),
    // 标签页切换
    handleTabClick (tab) {
      this.activeTab = tab.name
    },

    // 切换模板选择状态 - 单选模式
    toggleTemplateSelection (template) {
      this.selectedTemplateId = template.id
      this.selectedTemplateItem = template
    },
    handleColseTag () {
      this.excelName = ''
      this.selectedTemplateId = null
      this.selectedTemplateItem = null
    },
    // 分页大小改变
    handleSizeChange (val) {
      this.templatePagination.pageSize = val
      this.templatePagination.currentPage = 1
      this.getTemplates()
    },

    // 当前页改变
    handleCurrentChange (val) {
      this.getTemplates(val)
    },

    // 上传成功回调
    handleSuccess (response) {
      console.log(response, 'response')
      if (response.code !== 200) {
        this.$message.error(response.message)
        return
      }
      this.excelName = response.data.fileName
      this.selectedTemplateId = response.data.id
      this.selectedTemplateItem = response.data
      // this.getTemplates()
    },
    handleError (err) {
      console.log(err)
      this.$message.error('上传失败，请重试')
    },

    // 文件移除处理
    handleFileRemove (file, fileList) {
      this.fileList = fileList
    },

    // 确认操作
    async handleConfirm () {
      this.markDownText = ''
      this.isThinking = false

      if (!this.selectedTemplateId && this.fileList.length === 0) {
        this.$message.warning('请选择模板或上传文件')
        return
      }

      this.confirmLoading = true
      this.isGenerating = true
      const { signal } = this.controller
      // 显示进度弹窗并开始模拟进度
      this.showProgressDialog = true

      try {
        // 构建AI报表数据格式
        const aiReportData = this.buildAiReportData()

        const response = await fetch('/api/dds-server-ai/report/', {
          signal: signal,
          method: 'POST',
          async: false,
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(aiReportData)
        })
        const reader = response.body.getReader()
        const decoder = new TextDecoder()
        let extractedJson = null

        for (;;) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value, { stream: true })
          console.log(chunk, 'chunk')

          if (chunk.includes('</think>')) {
            this.isThinking = true
            // return // 不显示</think>标记本身
          }
          // 检查chunk是否包含JSON标记
          const jsonStartIndex = chunk.indexOf('<JSON_START>')
          const jsonEndIndex = chunk.indexOf('<JSON_END>')

          if (
            jsonStartIndex !== -1 &&
            jsonEndIndex !== -1 &&
            jsonEndIndex > jsonStartIndex
          ) {
            try {
              const jsonString = chunk.substring(
                jsonStartIndex + '<JSON_START>'.length,
                jsonEndIndex
              )
              extractedJson = JSON.parse(jsonString)
              console.log('提取的JSON对象:', extractedJson)
            } catch (error) {
              console.error('JSON解析失败:', error)
            }
          } else {
            this.markDownText += chunk

            // chunk
            // 放慢0.8倍速度显示markdown内容
            // await this.slowlyAddMarkdownText(chunk)

            // 在markdown内容添加完成后，再执行标记检查逻辑
            // 检查chunk是否包含</think>标记
          }

          this.$nextTick(() => {
            const div = document.getElementById('messageCompBox')
            div.scrollTop = div.scrollHeight - div.clientHeight
          })
        }

        if (Number(extractedJson.code) === 200) {
          // 解决浏览器拦截新窗口的问题
          this.openPreviewWindow(extractedJson.file_name, extractedJson)
        } else {
          this.$message.error(extractedJson.message)
        }
        // 完成进度并关闭弹窗
        this.isGenerating = false
        this.showProgressDialog = false
      } catch (error) {
        console.error('AI报表生成失败:', error)
      } finally {
        this.confirmLoading = false
        this.isGenerating = false
      }
    },
    cancelFetch () {
      this.controller.abort() // 取消请求
      this.controller = new AbortController() // 重置控制器以便下次使用新的信号
    },

    // 构建AI报表数据
    buildAiReportData () {
      console.log('开始构建AI报表数据...')
      console.log('原始headKeys:', this.headKeys)
      console.log('analyzeDims:', this.analyzeDims)

      // 构建head数组
      const head = []

      this.headKeys.forEach(item => {
        if (item.children) {
          // 如果有子列，处理子列
          item.children.forEach(child => {
            const dimNameDimValues = this.buildDimNameDimValuesForChild(
              item.label,
              child.label
            )
            head.push({
              label: `${item.label}:${child.label}`,
              dimNameDimValues: dimNameDimValues,
              prop: child.prop
            })
            console.log(
              `处理子列: ${item.label}:${child.label} -> ${dimNameDimValues}`
            )
          })
        } else {
          // 单列
          const dimNameDimValues = this.buildDimNameDimValuesForSingle(
            item.prop
          )
          if (item.label === '合计') {
            head.push({
              label: dimNameDimValues,
              dimNameDimValues: dimNameDimValues,
              prop: item.prop
            })
          } else {
            head.push({
              label: item.label,
              dimNameDimValues: dimNameDimValues,
              prop: item.prop
            })
          }
          console.log('单利')

          console.log(`处理单列: ${item.label} -> ${dimNameDimValues}`)
        }
      })

      // 构建rowData数组
      const rowData = this.rowData.map(row => {
        const newRow = {}
        // 处理每一行数据
        Object.keys(row).forEach(key => {
          // 跳过包含_origin的key
          if (key.includes('_origin')) {
            return
          }

          if (key === 'indicator') {
            newRow[key] = row[key]
          } else {
            // 处理包含"合计"的key
            if (key.includes('合计') && key !== '合计') {
              // 找到"合计"前面的部分
              const beforeTotal = key.substring(0, key.indexOf('合计'))
              const dimValList = this.analyzeDims.find(
                dim => dim.alias === beforeTotal
              ).dimList[0].dimValList
              const newKey = `${dimValList
                .map(item => item.value)
                .join('、')}合计`
              newRow[newKey] = row[key]
            } else {
              // 对于其他列，保持原值
              newRow[key] = row[key]
            }
          }
        })
        return newRow
      })

      // 构建完整的AI报表数据
      const aiReportData = {
        themeName: this.themeName || '汇总表',
        modelName: this.selectedTemplateItem.fileSuffix, // 这里可以根据实际需求设置
        modelId: this.selectedTemplateItem.id, // 这里可以根据实际需求设置
        head: head,
        rowData: rowData
      }

      console.log('构建完成的AI报表数据:', aiReportData)
      return aiReportData
    },

    // 构建维度名称和维度值的组合 - 处理有子级的情况
    buildDimNameDimValuesForChild (parentLabel, childLabel) {
      // 在analyzeDims中查找对应的数据
      const matchedAnalyzeDim = this.analyzeDims.find(
        dim => dim.alias === parentLabel
      )

      if (!matchedAnalyzeDim) {
        console.warn(`未找到alias为${parentLabel}的analyzeDim数据`)
        return `${parentLabel}:${childLabel}`
      }

      // 如果dimList长度大于1
      if (matchedAnalyzeDim.dimList && matchedAnalyzeDim.dimList.length > 1) {
        const dimNameDimValues = []

        matchedAnalyzeDim.dimList.forEach(dim => {
          if (dim.dimValList && dim.dimValList.length > 0) {
            if (dim.dimValList.length > 1) {
              // 如果dimValList长度大于1，用、连接
              const values = dim.dimValList.map(item => item.value).join('、')
              dimNameDimValues.push(`${dim.dimName}:${values}`)
            } else {
              // 如果dimValList长度等于1
              dimNameDimValues.push(`${dim.dimName}:${dim.dimValList[0].value}`)
            }
          }
        })

        return dimNameDimValues.join(',')
      } else if (
        matchedAnalyzeDim.dimList &&
        matchedAnalyzeDim.dimList.length === 1
      ) {
        // 如果dimList长度等于1
        return `${parentLabel}:${childLabel}`
      } else {
        // 如果没有dimList或为空
        return `${parentLabel}:${childLabel}`
      }
    },

    // 构建维度名称和维度值的组合 - 处理单列的情况
    buildDimNameDimValuesForSingle (label) {
      // 在analyzeDims中查找对应的数据
      const matchedAnalyzeDim = this.analyzeDims.find(
        dim => dim.alias === label
      )

      if (label.includes('合计') && label !== '合计') {
        console.log('??????????')
        // 找到"合计"前面的部分
        const beforeTotal = label.substring(0, label.indexOf('合计'))
        const dimValList = this.analyzeDims.find(
          dim => dim.alias === beforeTotal
        ).dimList[0].dimValList
        return `${dimValList.map(item => item.value).join('、')}合计`
      }
      if (!matchedAnalyzeDim) {
        console.warn(`未找到alias为${label}的analyzeDim数据`)
        return label
      }

      // 如果dimList长度大于1
      if (matchedAnalyzeDim.dimList && matchedAnalyzeDim.dimList.length > 1) {
        const dimNameDimValues = []

        matchedAnalyzeDim.dimList.forEach(dim => {
          if (dim.dimValList && dim.dimValList.length > 0) {
            if (dim.dimValList.length > 1) {
              // 如果dimValList长度大于1，用、连接
              const values = dim.dimValList.map(item => item.value).join('、')
              dimNameDimValues.push(`${dim.dimName}:${values}`)
            } else {
              // 如果dimValList长度等于1
              dimNameDimValues.push(`${dim.dimName}:${dim.dimValList[0].value}`)
            }
          }
        })

        return dimNameDimValues.join(',')
      } else if (
        matchedAnalyzeDim.dimList &&
        matchedAnalyzeDim.dimList.length === 1
      ) {
        // 如果dimList长度等于1
        return label
      } else {
        // 如果没有dimList或为空
        return label
      }
    },
    downloadExcel () {
      if (this.excelUrl) {
        window.open(this.excelUrl, '_blank')
      } else {
        this.$message.warning('暂无可下载的Excel文件')
      }
    },

    // 解决浏览器拦截新窗口问题的预览方法
    openPreviewWindow (fileName, extractedJson) {
      const routerUrl = this.$router.resolve({
        path: '/ddsBi/PreviewAiExcel',
        query: {
          file: fileName,
          emptyCell: extractedJson.emptyCell,
          shapeError: extractedJson.shapeError
        }
      })
      // 方案1: 使用确认对话框，让用户主动点击打开
      this.$confirm('AI报表生成成功！是否立即预览？', '提示', {
        confirmButtonText: '立即预览',
        cancelButtonText: '稍后查看',
        type: 'success'
      })
        .then(() => {
          // 用户点击确认后打开新窗口，此时有用户手势，不会被拦截
          window.open(routerUrl.href, '_blank')
        })
        .catch(() => {})

      // 方案2备选: 如果方案1不满足需求，可以使用以下代码替换上面的确认对话框
      /*
      // 创建一个临时的a标签来触发下载或打开
      const link = document.createElement('a')
      link.href = routerUrl.href
      link.target = '_blank'
      link.rel = 'noopener noreferrer'

      // 添加到DOM并触发点击
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 同时显示成功消息
      this.$message.success('AI报表生成成功！新窗口已打开预览')
      */
    },

    // 关闭进度弹窗（用户点击关闭按钮）
    handleCloseProgress () {
      if (this.isGenerating) {
        // 中断生成任务
        this.cancelFetch()
        this.confirmLoading = false
        this.isGenerating = false
        this.$message.info('生成任务已中断')
      }
    },

    // 慢速添加markdown文本内容（0.8倍速度）
    async slowlyAddMarkdownText (chunk) {
      return new Promise(resolve => {
        // 将chunk按字符分割
        const chars = chunk.split('')
        let index = 0

        const addChar = () => {
          if (index < chars.length) {
            this.markDownText += chars[index]
            index++
            // 0.8倍速度意味着延迟时间增加1.25倍（1/0.8 = 1.25）
            // 假设正常速度为每个字符10ms，那么0.8倍速度为12.5ms
            setTimeout(addChar, 12.5)
          } else {
            resolve()
          }
        }

        addChar()
      })
    }
  }
}
</script>

<style scoped lang="scss">
.ai-report-dialog {
  ::v-deep .el-dialog__body {
    padding: 20px;
  }
}
.generate-dialog {
  ::v-deep .el-dialog__body {
    padding: 10px 20px 20px;
  }
  .think-process {
    display: flex;
    padding: 0 0 10px;
    margin-bottom: 5px;
    font-size: 16px;
    font-weight: bold;
  }
}

.dialog-content {
  display: flex;
  gap: 20px;
  min-height: 400px;
}

.left-section {
  flex: 1;
  border-right: 1px solid #e4e7ed;
  padding-right: 20px;

  .section-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 16px;
  }

  .search-box {
    margin-bottom: 16px;
  }

  .template-tabs {
    ::v-deep .el-tabs__header {
      margin-bottom: 16px;
    }

    ::v-deep .el-tabs__item {
      padding: 0 16px;
      height: 32px;
      line-height: 32px;
      font-size: 14px;
    }
  }

  .template-list {
    max-height: 300px;
    overflow-y: auto;
    padding-right: 10px;

    .template-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;

      .template-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        cursor: pointer;
        position: relative;
        transition: all 0.3s;
        min-height: 80px;

        &:hover {
          border-color: #409eff;
          background-color: #f5f7fa;
        }

        &.selected {
          border-color: #409eff;
          background-color: #ecf5ff;

          .selection-indicator {
            display: flex;
          }
        }

        .template-icon {
          margin-right: 12px;
          flex-shrink: 0;

          img {
            width: 43px;
            height: 43px;
            object-fit: cover;
            border-radius: 5px;
          }
        }

        .template-content {
          flex: 1;
          min-width: 0;

          .template-title {
            font-size: 14px;
            color: #303133;
            line-height: 1.4;
            margin-bottom: 8px;
            word-break: break-all;
          }
          .template-preview {
            font-size: 12px;
            color: #3774f3;
            line-height: 1.4;
            &:hover {
              text-decoration: underline;
            }
          }
          .template-description {
            font-size: 12px;
            color: #909399;
            line-height: 1.4;
            margin-bottom: 8px;
            word-break: break-all;
          }
        }

        .selection-indicator {
          display: none;
          align-items: center;
          justify-content: center;
          width: 20px;
          height: 20px;
          background-color: #409eff;
          border-radius: 50%;
          color: white;
          font-size: 12px;
          position: absolute;
          top: 8px;
          right: 8px;
        }
      }
    }
  }

  .template-pagination {
    margin-top: 16px;
    display: flex;
    justify-content: center;

    ::v-deep .el-pagination {
      .el-pagination__total,
      .el-pagination__sizes,
      .el-pagination__jump {
        color: #606266;
        font-size: 13px;
      }

      .el-pager li {
        font-size: 13px;
        min-width: 28px;
        height: 28px;
        line-height: 28px;
      }

      .btn-prev,
      .btn-next {
        font-size: 13px;
        min-width: 28px;
        height: 28px;
        line-height: 28px;
      }
    }
  }
}

.right-section {
  flex: 1;

  .section-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 16px;
  }

  .upload-area {
    .custom-file-tag {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: linear-gradient(135deg, #667eea 0%, #48aee3 100%);
      border-radius: 12px;
      padding: 16px 20px;
      margin-bottom: 16px;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
      }

      .file-info {
        display: flex;
        align-items: center;
        flex: 1;

        .el-icon-document {
          font-size: 24px;
          color: #ffffff;
          margin-right: 12px;
        }

        .file-name {
          color: #ffffff;
          font-size: 16px;
          font-weight: 500;
          word-break: break-all;
        }
      }

      .close-btn {
        font-size: 18px;
        color: #ffffff;
        cursor: pointer;
        padding: 4px;
        border-radius: 50%;
        transition: all 0.2s ease;

        &:hover {
          background-color: rgba(255, 255, 255, 0.2);
          transform: scale(1.1);
        }
      }
    }

    ::v-deep .el-upload-dragger {
      // width: 100%;
      // height: 200px;
      border: 2px dashed #d9d9d9;
      border-radius: 6px;
      background-color: #fafafa;
      text-align: center;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: border-color 0.3s;

      &:hover {
        border-color: #409eff;
      }

      .el-icon-upload {
        font-size: 48px;
        color: #c0c4cc;
        margin-bottom: 16px;
        margin-top: 20px;
      }

      .el-upload__text {
        color: #606266;
        font-size: 14px;
        text-align: center;
        margin-bottom: 8px;
      }

      .el-upload__tip {
        color: #909399;
        font-size: 12px;
        text-align: center;
      }
    }

    ::v-deep .el-upload-list {
      margin-top: 16px;
    }
  }
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

// 滚动条样式
.template-list::-webkit-scrollbar {
  width: 6px;
}

.template-list::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: #c0c4cc;
}

.template-list::-webkit-scrollbar-track {
  border-radius: 3px;
  background: #f5f7fa;
}

// 进度弹窗样式
.progress-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  justify-content: center;
  align-items: center;

  &.fadein {
    animation: fadeIn 0.3s ease-in-out;
  }

  &.fadeout {
    animation: fadeOut 0.3s ease-in-out;
  }

  .progress-inner-box {
    width: 400px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    overflow: hidden;

    .progress-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid #e4e7ed;
      background: #f5f7fa;

      .progress-title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }

      .progress-close {
        cursor: pointer;
        color: #909399;
        font-size: 16px;
        transition: color 0.3s;

        &:hover {
          color: #409eff;
        }
      }
    }

    .progress-content {
      padding: 24px 20px;
      display: flex;
      align-items: center;

      .progress-icon {
        width: 56px;
        height: 56px;
        background: url('~@/assets/images/yjs.png') no-repeat center;
        background-size: contain;
        margin-right: 16px;
        flex-shrink: 0;
      }

      .progress-right {
        flex: 1;

        .progress-text {
          font-size: 14px;
          color: #303133;
          margin-bottom: 12px;
          font-weight: 500;
        }

        ::v-deep .el-progress {
          .el-progress-bar__outer {
            background-color: #e4e7ed;
            border-radius: 3px;
          }

          .el-progress-bar__inner {
            background: linear-gradient(90deg, #409eff 0%, #66b1ff 100%);
            border-radius: 3px;
            transition: width 0.3s ease;
          }
        }
      }
    }
  }
}

.thinking-animation {
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 4px;
}

.thinking-animation > div {
  width: 8px;
  height: 8px;
  background-color: #666;
  border-radius: 50%;
  animation: thinking-animation 1.4s infinite ease-in-out;
}

.thinking-animation > div:nth-child(1) {
  animation-delay: 0s;
}

.thinking-animation > div:nth-child(2) {
  animation-delay: 0.2s;
}

.thinking-animation > div:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes thinking-animation {
  0%,
  80%,
  100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  40% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.9);
  }
}

// messageCompBox 样式
#messageCompBox {
  max-height: 500px;
  overflow-y: auto;
  padding: 20px;
  background-color: #ffffff;
  border-radius: 8px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei',
    sans-serif;

  .markdown-body {
    font-size: 14px;
    line-height: 1.8;
    color: #333333;

    &.dark {
      background-color: transparent;
      color: #333333;
    }

    // 段落样式
    p {
      margin-bottom: 16px;
      text-align: justify;
      text-indent: 0;
      word-wrap: break-word;
      word-break: break-all;
    }

    // 标题样式
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      margin-top: 20px;
      margin-bottom: 12px;
      font-weight: 600;
      color: #2c3e50;

      &:first-child {
        margin-top: 0;
      }
    }

    // 列表样式
    ul,
    ol {
      margin-bottom: 16px;
      padding-left: 24px;

      li {
        margin-bottom: 8px;
        line-height: 1.6;
      }
    }

    // 强调文本
    strong,
    b {
      font-weight: 600;
      color: #2c3e50;
    }

    // 代码块样式
    pre {
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      padding: 16px;
      overflow-x: auto;
      margin-bottom: 16px;
      font-size: 13px;
    }

    // 行内代码样式
    code {
      background-color: #f1f3f4;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 13px;
      color: #e83e8c;
    }

    // 表格样式
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 16px;
      border: 1px solid #dee2e6;

      th,
      td {
        border: 1px solid #dee2e6;
        padding: 12px;
        text-align: left;
        vertical-align: top;
      }

      th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #495057;
      }

      tr:nth-child(even) {
        background-color: #f8f9fa;
      }
    }

    // 引用样式
    blockquote {
      border-left: 4px solid #409eff;
      padding: 12px 16px;
      margin: 16px 0;
      background-color: #f8f9fa;
      color: #6c757d;
      font-style: italic;

      p:last-child {
        margin-bottom: 0;
      }
    }

    // 分割线
    hr {
      border: none;
      border-top: 1px solid #dee2e6;
      margin: 24px 0;
    }

    // 链接样式
    a {
      color: #409eff;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  // 滚动条样式
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #c0c4cc;
    border-radius: 4px;

    &:hover {
      background-color: #a8abb2;
    }
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f7fa;
    border-radius: 4px;
  }
}

// messageCompBox 样式
#messageCompBox {
  max-height: 400px;
  overflow-y: auto;
  padding: 16px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  .markdown-body {
    font-size: 14px;
    line-height: 1.6;
    color: #333;

    &.dark {
      background-color: transparent;
      color: #333;
    }

    // 标题样式
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      margin-top: 16px;
      margin-bottom: 8px;
      font-weight: 600;
      color: #2c3e50;
    }

    // 段落样式
    p {
      margin-bottom: 12px;
      text-align: justify;
    }

    // 列表样式
    ul,
    ol {
      margin-bottom: 12px;
      padding-left: 20px;

      li {
        margin-bottom: 4px;
      }
    }

    // 代码块样式
    pre {
      background-color: #f1f3f4;
      border: 1px solid #e1e4e8;
      border-radius: 4px;
      padding: 12px;
      overflow-x: auto;
      margin-bottom: 12px;
    }

    // 行内代码样式
    code {
      background-color: #f1f3f4;
      padding: 2px 4px;
      border-radius: 3px;
      font-size: 13px;
    }

    // 表格样式
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 12px;

      th,
      td {
        border: 1px solid #e1e4e8;
        padding: 8px 12px;
        text-align: left;
      }

      th {
        background-color: #f6f8fa;
        font-weight: 600;
      }
    }

    // 引用样式
    blockquote {
      border-left: 4px solid #409eff;
      padding-left: 12px;
      margin: 12px 0;
      color: #666;
      font-style: italic;
    }
  }

  // 滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #c0c4cc;
    border-radius: 3px;

    &:hover {
      background-color: #a8abb2;
    }
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f7fa;
    border-radius: 3px;
  }
}
</style>
