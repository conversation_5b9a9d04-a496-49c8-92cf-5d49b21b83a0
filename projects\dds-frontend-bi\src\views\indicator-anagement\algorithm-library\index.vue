<template>
  <DT-View class="rolePage-mainView">
    <!-- 按钮 -->
    <el-button type="primary" @click="handleAdd">新增</el-button>
    <el-button @click="handleExport">导入</el-button>
    <!-- 表格 -->
    <DT-Table :data="tableData" :column="tableColumn"></DT-Table>
    <!-- 分页 -->
    <DT-Pagination
      :hidden="pagination.total == 0"
      :total="pagination.total"
      :page-size="pagination.pageSize"
      :current-page="pagination.currentPage"
      @sizeChange="handlePageSizeChange"
      @currentChange="handlePageCurrentChange"
    />
    <el-dialog
      title="调用历史"
      :visible.sync="dialog.useLogVisible"
      width="80%"
      :before-close="closeUseLogDialog"
    >
      <useLogTable v-if="dialog.useLogVisible" :scene-code="dialog.currentSceneCode" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeUseLogDialog">关闭</el-button>
      </span>
    </el-dialog>
  </DT-View>
</template>

<script>
import Request from "@/service"
import useLogTable from "./components/useLogTable.vue"
export default {
  name: "AlgorithmLibrary",
  components: {
    useLogTable
  },
  props: {
    showDetail: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 表格渲染配置
      tableColumn: [
        {
          label: "算法名称",
          prop: "algorithmName"
        },
        {
          label: "算法介绍",
          prop: "introduction"
        },
        {
          label: "版本",
          prop: "version"
        },
        {
          label: "引用次数",
          prop: "referenceNumber"
        },
        {
          label: "修改时间",
          prop: "updateTime"
        },
        // 操作
        {
          label: "操作",
          width: 150,
          button: [
            {
              label: "复制",
              onClick: ({ row }) => this.handleCopy(row)
            },
            {
              label: "编辑",
              permission: ["algorithm:edit"],
              onClick: ({ row }) => this.handleEdit(row)
            },
            {
              label: "查看",
              onClick: ({ row }) => this.handleView(row)
            },
            {
              label: "删除",
              onClick: ({ row }) => this.handleDel(row)
            },
            {
              label: "调用日志",
              onClick: ({ row }) => this.handleUseLog(row)
            }
          ]
        }
      ],
      dialog: {
        useLogVisible: false,
        currentSceneCode: null // 当前选中的信息
      },
      tableData: [],
      pagination: {
        total: 0,
        pageSize: 10,
        currentPage: 1
      }
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    // ------------------------ 主视图 ------------------------
    // 拉取表格数据
    getTableData() {
      let param = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.currentPage,
        query: ""
      }
      this.$dt_loading.show()
      Request.algorithm
        .getAlgorithmList(param)
        .then((res) => {
          if (res.code === 200) {
            console.log(1212, res.data)
            this.pagination.total = res.data.totalCount || 0
            this.tableData = res.data.list || []
            this.$dt_loading.hide()
          }
        })
        .catch(() => this.$dt_loading.hide())
    },
    // 分页 - 每页条数改变
    handlePageSizeChange(event) {
      this.pagination.pageSize = event.pageSize
      this.pagination.currentPage = 1
      this.getTableData()
    },
    // 分页 - 当前页码改变
    handlePageCurrentChange(event) {
      this.pagination.currentPage = event.currentPage
      this.getTableData()
    },
    // 新增
    handleAdd() {
      this.$router.push({
        name: "CreateAlgorithmTool",
        query: { mode: "add" }
      })
    },
    // 复制
    handleCopy(row) {
      this.$dt_loading.show()
      Request.algorithm
        .copyAlgorithm(row.id)
        .then((res) => {
          console.log(343554, res)
          if (res.code === 200) {
            this.$message.success("复制成功")
            this.getTableData()
          } else {
            this.$message.error("复制失败")
          }
        })
        .catch(() => this.$dt_loading.hide())
        .finally(() => {
          this.$dt_loading.hide()
        })
    },
    // 编辑
    handleEdit(row) {
      // this.createAlgorithm.show = true
      // this.mainView.show = false
      // this.$emit("update:showDetail", true)
      this.$router.push({
        name: "CreateAlgorithmTool",
        query: { mode: "edit", id: row.id }
      })
      // this.$router.push("/ddsBi/createAlgorithmTool")
    },
    // 查看
    handleView(row) {
      this.$router.push({
        name: "CreateAlgorithmTool",
        query: { mode: "view", id: row.id }
      })
    },
    // 调用日志
    handleUseLog(item) {
      this.dialog.currentSceneCode = item.algorithmDetailCode
      this.dialog.useLogVisible = true
    },
    closeUseLogDialog() {
      this.dialog.useLogVisible = false
      this.dialog.currentSceneCode = null
    },

    lookVersion() {
      this.createAlgorithm.show = false
      this.mainView.show = false
      this.historyVersion.show = true
    },
    handleLstPage() {
      this.handleBack()
      this.createAlgorithm.show = true
    },
    // 删除
    handleDel(row) {
      console.log("del", row)
      this.$confirm("是否删除选中数据", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$dt_loading.show()
          Request.algorithm
            .deleteAlgorithm(row.id)
            .then((res) => {
              if (res.code === 200) {
                // 若当前并非首页且本页仅有一条数据，则页码减一后拉取数据
                if (this.pagination.currentPage > 1 && this.data.length === 1) {
                  this.pagination.currentPage -= 1
                }
                this.$message.success("删除成功")
                this.getTableData()
              } else {
                this.$message.error("删除失败")
              }
            })
            .catch(() => this.$dt_loading.hide())
            .finally(() => {
              this.$dt_loading.hide()
            })
        })
        .catch((error) => {
          console.error(error)
        })
    },
    // 开启
    handleOpen(row) {
      this.$confirm("该场景一旦开启后，推送该场景", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$dt_loading.show()

          Request.api
            .paramPost("/pushSetting/scene/changeState", {
              id: row.id,
              isEnable: 1
            })
            .then(() => {
              this.$message.success("启用成功")
              this.getTableData()
            })
            .catch(() => this.$dt_loading.hide())
        })
        .catch((error) => {
          console.error(error)
        })
    },
    // 关闭
    handleClose(row) {
      this.$confirm("该场景一旦禁用后，无法推送该场景，直到再次开启", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$dt_loading.show()
          Request.api
            .paramPost("/pushSetting/scene/changeState", {
              id: row.id,
              isEnable: 0
            })
            .then(() => {
              this.$message.success("禁用成功")
              this.getTableData()
            })
            .catch(() => this.$dt_loading.hide())
        })
        .catch((error) => {
          console.error(error)
        })
    },
    // 返回
    handleBack() {
      this.createAlgorithm.show = false
      this.mainView.show = false
      this.historyVersion.show = false
    },
    backMain() {
      this.handleBack()
      this.mainView.show = true
      this.$emit("update:showDetail", false)
    }
  }
}
</script>
<style scoped lang="scss">
.btns {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}
</style>
