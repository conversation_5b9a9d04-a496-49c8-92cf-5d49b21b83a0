<template>
  <div class="chart-bar" :style="{ width, height }">
    <div ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'ChartBar',
  props: {
    // 基础属性
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    // 图表数据
    chartData: {
      type: Array,
      default: () => []
    },
    // 新格式数据支持
    categories: {
      type: Array,
      default: () => []
    },
    series: {
      type: Array,
      default: () => []
    },
    // 字段配置
    xField: {
      type: String,
      default: 'value'
    },
    yField: {
      type: String,
      default: 'name'
    },
    seriesName: {
      type: String,
      default: '数据'
    },
    // 样式配置
    color: {
      type: [String, Array],
      default: '#52c41a'
    },
    colors: {
      type: Array,
      default: () => [
        '#2361DB',
        '#0EACCC',
        '#1DB35B',
        '#FFC508',
        '#FF742E',
        '#F5427E',
        '#AA51D6',
        '#77D2E5'
      ]
    },
    // 显示配置
    showLabel: {
      type: Boolean,
      default: false
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    showGrid: {
      type: Boolean,
      default: true
    },
    showTooltip: {
      type: Boolean,
      default: true
    },
    // 动画配置
    animation: {
      type: Boolean,
      default: true
    },
    animationDuration: {
      type: Number,
      default: 1000
    },
    // 自定义配置
    customOption: {
      type: Object,
      default: () => ({})
    },
    // 展示数量
    showNum: {
      type: Number || String,
      default: 5
    },
    // 滚动轴
    showDataZoom: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      chart: null
    }
  },
  computed: {
    chartOption () {
      // 判断使用哪种数据格式
      const useNewFormat = this.categories.length > 0 && this.series.length > 0

      let yData, seriesData, legendData

      if (useNewFormat) {
        // 新格式：categories + series
        yData = this.categories
        seriesData = this.series
        legendData = this.series.map(item => item.name)
      } else {
        // 旧格式：chartData + xField + yField
        yData = this.chartData.map(item => item[this.yField])
        const xData = this.chartData.map(item => item[this.xField])
        seriesData = [
          {
            name: this.seriesName,
            data: xData
          }
        ]
        legendData = [this.seriesName]
      }

      const option = {
        title: {
          show: false
        },
        tooltip: {
          // trigger: 'axis',
          trigger: 'axis',
          confine: true,

          axisPointer: {
            type: 'shadow',
            label: { show: false, backgroundColor: 'transparent' },
            shadowStyle: {
              color: 'rgba(35,97,219,0.05)'
            }
          },
          className: 'echarts-tooltip-diy'
        },
        dataZoom: this.isShowScroll
          ? [
              {
                type: 'slider',
                yAxisIndex: 0,
                zoomLock: true,
                width: 6,

                showDetail: false,
                start: 0,
                endValue: this.showNum - 1,
                showDataShadow: false,
                fillerColor: '#DBDBDB', // 滑块的颜色

                backgroundColor: 'transparent', // 滑块轨道的颜色
                borderColor: 'transparent', // 滑块轨道边框的颜色
                moveHandleIcon: 'none',

                zoomOnMouseWheel: false,
                brushSelect: false,

                handleIcon:
                  'M-292,322.2c-3.2,0-6.4-0.6-9.3-1.9c-2.9-1.2-5.4-2.9-7.6-5.1s-3.9-4.8-5.1-7.6c-1.3-3-1.9-6.1-1.9-9.3c0-3.2,0.6-6.4,1.9-9.3c1.2-2.9,2.9-5.4,5.1-7.6s4.8-3.9,7.6-5.1c3-1.3,6.1-1.9,9.3-1.9c3.2,0,6.4,0.6,9.3,1.9c2.9,1.2,5.4,2.9,7.6,5.1s3.9,4.8,5.1,7.6c1.3,3,1.9,6.1,1.9,9.3c0,3.2-0.6,6.4-1.9,9.3c-1.2,2.9-2.9,5.4-5.1,7.6s-4.8,3.9-7.6,5.1C-285.6,321.5-288.8,322.2-292,322.2z',
                handleSize: '100%',
                handleStyle: {
                  color: '#DBDBDB',
                  borderColor: 'transparent'
                }
              },
              {
                type: 'inside',
                id: 'insideY',
                yAxisIndex: 0,
                zoomOnMouseWheel: false,
                moveOnMouseMove: true,
                moveOnMouseWheel: true
              }
            ]
          : [
              {
                show: false
              }
            ],
        legend: {
          show: this.showLegend && legendData.length > 1,
          data: legendData,
          top: 10,
          right: 20
        },
        grid: {
          top: 40,
          bottom: 0,
          left: 0,
          right: '4%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          splitLine: {
            lineStyle: {
              color: '#EBEDF0',
              type: 'dashed'
            }
          },
          axisLabel: {
            fontSize: 12,
            color: '#646566',
            fontWeight: '400',
            lineHeight: 20

            // formatter: metricAxisLabelFormatter,
          }
        },
        yAxis: [
          {
            type: 'category',
            data: yData,
            inverse: true,
            splitLine: {
              lineStyle: {
                color: '#8B9FB3',
                type: 'dashed'
              }
            },
            axisLabel: {
              fontSize: 12,
              color: '323233',
              lineHeight: 14,
              fontWeight: '400',
              formatter: value => {
                let startName = value.substring(0, 5)
                let endName = value.substring(5)
                if (endName.length > 5) {
                  return `${startName}\n${value.substring(5, 9)}...`
                }
                return `${startName}\n${endName}`
              }
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: seriesData.map((seriesItem, seriesIndex) => ({
          name: seriesItem.name,
          type: 'bar',
          data: seriesItem.data,
          barWidth: 16,
          itemStyle: {
            color: useNewFormat
              ? this.colors[seriesIndex % this.colors.length]
              : Array.isArray(this.color)
              ? params => this.colors[params.dataIndex % this.colors.length]
              : this.color
          },
          label: {
            show: this.showLabel,
            position: 'right',
            color: '#666666',
            fontSize: 12
          }
        }))
      }

      // 合并自定义配置
      return this.mergeOption(option, this.customOption)
    },
    // 是否数据大于显示长度才显示滚动
    isShowScroll () {
      return this.showDataZoom && this.categories.length > this.showNum
    }
  },
  watch: {
    chartData: {
      handler () {
        this.updateChart()
      },
      deep: true
    },
    chartOption: {
      handler () {
        this.updateChart()
      },
      deep: true
    }
  },
  mounted () {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeUnmount () {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initChart () {
      if (!this.$refs.chartContainer) return

      this.chart = echarts.init(this.$refs.chartContainer)
      this.updateChart()

      // 绑定点击事件
      this.chart.on('click', params => {
        this.$emit('chart-click', params)
      })

      // 绑定双击事件
      this.chart.on('dblclick', params => {
        this.$emit('chart-dblclick', params)
      })

      // 绑定鼠标悬停事件
      this.chart.on('mouseover', params => {
        this.$emit('chart-mouseover', params)
      })

      // 绑定鼠标离开事件
      this.chart.on('mouseout', params => {
        this.$emit('chart-mouseout', params)
      })
    },
    updateChart () {
      if (!this.chart) return

      this.chart.setOption(this.chartOption, true)
    },
    handleResize () {
      if (this.chart) {
        this.chart.resize()
      }
    },
    mergeOption (target, source) {
      if (!source || typeof source !== 'object') return target

      const result = { ...target }

      Object.keys(source).forEach(key => {
        if (
          source[key] &&
          typeof source[key] === 'object' &&
          !Array.isArray(source[key])
        ) {
          result[key] = this.mergeOption(result[key] || {}, source[key])
        } else {
          result[key] = source[key]
        }
      })

      return result
    },
    // 公共方法
    getChart () {
      return this.chart
    },
    getOption () {
      return this.chart ? this.chart.getOption() : null
    },
    getDataURL (opts) {
      return this.chart ? this.chart.getDataURL(opts) : null
    },
    resize () {
      if (this.chart) {
        this.chart.resize()
      }
    },
    clear () {
      if (this.chart) {
        this.chart.clear()
      }
    },
    dispose () {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    }
  }
}
</script>

<style scoped>
.chart-bar {
  position: relative;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
