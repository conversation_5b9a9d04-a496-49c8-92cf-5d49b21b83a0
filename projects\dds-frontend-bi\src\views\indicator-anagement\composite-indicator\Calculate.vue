<template>
  <el-dialog
    title="试计算"
    :visible.sync="dialogVisible"
    width="950px"
    top="10vh"
    :before-close="handleClose"
  >
    <div class="sub-title" style="margin-top: 15px">
      <div class="sub-text">维度</div>
      <div class="sub-line"></div>
    </div>
    <div style="display: flex; align-items: center">
      <div class="label" style="margin-right: 10px; flex: 0 0 60px">
        选择维度
      </div>

      <div class="right" style="flex: 1">
        <el-row :gutter="15">
          <el-col :span="10" v-for="(item, index) in filterDims" :key="index">
            <div style="display: flex; align-items: center; margin: 8px 0">
              <span class="dimName" :title="item.dimAndLevelName">
                {{ item.dimAndLevelName }}
              </span>

              <LevelMultipleSelect
                v-model="item.wdzval"
                v-if="item.levelCode && !item.enableClustering"
                :is-selected-all="item.isSelectedAll"
                :is-selected-all-name="item.isSelectedAllName"
                :props="{
                  label: 'value',
                  value: 'value'
                }"
                style="width: 205px; margin-left: 6px"
                :level-code="item.levelCode"
              />
              <ClusterMultipleSelect
                v-if="item.levelCode && item.enableClustering"
                :dim-values.sync="item.wdzval"
                v-model="item.clusterCodes"
                :is-selected-all="item.isSelectedAll"
                :is-selected-all-name="item.isSelectedAllName"
                value-code="value"
                :level-code="item.levelCode"
                style="width: 205px; margin-left: 6px"
              />
            </div>
          </el-col>
          <el-col :span="2" style="margin: 8px 0">
            <el-button @click="openDimensionDialog">选择过滤维度</el-button>
          </el-col>
        </el-row>
      </div>
    </div>
    <div
      style="display: flex; align-items: center; margin-top: 15px"
      v-if="isHasDateDim"
    >
      <div class="label" style="margin-right: 10px; flex: 0 0 60px">
        时间范围
      </div>
      <el-date-picker
        v-model="date"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd HH:mm:ss"
      ></el-date-picker>
      <el-button @click="calculation" style="margin-left: 70px">计算</el-button>
    </div>

    <el-button
      v-else
      @click="calculation"
      style="margin-left: 70px; margin-top: 15px"
    >
      计算
    </el-button>
    <div class="sub-title" style="margin-top: 24px">
      <div class="sub-text">试计算结果</div>
      <div class="sub-line"></div>
    </div>
    <el-table :data="tableData" v-loading="loading" :max-height="300">
      <el-table-column prop="indName" label="指标" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.indName }}
          <p style="color: #909399;font-size: 12px;" >
            <span
            v-for="(item, index) in row.dimVos" :key="index"
            > {{ item.colName }}：{{ item.val }}; </span>
          </p>
        </template>
      </el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="indValShow"
        label="值"
        width="220"
      >
        <template #default="{ row }">
          {{ row.indValShow | formatValue }}
        </template>
      </el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="unit"
        label="单位"
        width="100"
      >
        <template #default="{ row }">
          {{ row.unit || '-' }}
        </template>
      </el-table-column>
    </el-table>
    <FilterDimDialog
      :filter-dims.sync="filterDims"
      ref="FilterDimDialog"
      :props="{
        label: 'dimAndLevelName',
        value: 'dimCol',
        valueList: 'wdzval'
      }"
      @getDimsValue="getDimsValue"
    />
  </el-dialog>
</template>

<script>
import options from '../mixins/options'
import FilterDimDialog from '../components/FilterDimDialog.vue'
import LevelMultipleSelect from '../components/LevelMultipleSelect.vue'
import ClusterMultipleSelect from '../components/ClusterMultipleSelect.vue'

export default {
  components: { LevelMultipleSelect, FilterDimDialog, ClusterMultipleSelect },
  mixins: [options],
  props: {},
  data () {
    return {
      isHasDateDim: false,
      isCollapsed: true,
      dialogVisible: false,
      tableData: [],
      loading: false,
      dimensionList: [],
      currentIndicatorInfo: {},
      pswdOptionsMap: {},
      date: [],
      filterDims: []
    }
  },
  computed: {
    // 常驻显示的条件数量
    visibleCount () {
      return 2 // 可根据需要调整
    },

    // 常驻显示的条件
    visibleConditions () {
      return this.dimensionList.slice(0, this.visibleCount)
    },

    // 可折叠的条件
    collapsibleConditions () {
      return this.dimensionList.slice(this.visibleCount)
    },

    // 是否需要显示折叠功能
    hasCollapsible () {
      return this.dimensionList.length > this.visibleCount
    }
  },
  methods: {
    openDimensionDialog () {
      this.$refs.FilterDimDialog.open(this.dimensionList)
    },
    open () {
      this.dialogVisible = true
      this.tableData = []
      this.filterDims = []
      this.dimensionList = []
    },
    async initData (indCode) {
      this.open()
      const { data: detail } =
        await this.$httpBi.compositeIndicator.getCompositeIndicatorInfo({
          indCode
        })

      const { data } = await this.$httpBi.api.paramGet(
        '/DimManage/getDimLevelByIndCode',
        {
          indCode: indCode
        }
      )
      this.dimensionList = data
        .map(item => ({
          ...item,
          col: item.dimCol,
          wdzval: []
        }))
        .filter(item => item.fieldType !== 'time')
      this.currentIndicatorInfo = detail
      data.forEach(item => {
        if (item.fieldType === 'time') {
          this.isHasDateDim = true
        }
      })

      this.calculation()
    },
    async calculation () {
      this.loading = true
      const { data: tableData } =
        await this.$httpBi.compositeIndicator.tryToCaluCompositeIndicator({
          ...this.currentIndicatorInfo,
          whereParams: this.filterDims
            .filter(item => item.dimName !== '无')
            .map(item => ({
              col: item.dimCol,
              dateStart: this.date[0],
              dateEnd: this.date[1],
              dimType: item.dimType,
              val: item.wdzval.filter(e => e !== '全部').join(',')
            }))
            .concat(
              this.currentIndicatorInfo.expandDims
                .filter(item => item.dimType === 'time' && this.date.length)
                .map(item => ({
                  col: item.dimCol,
                  dimType: item.dimType,
                  val: '',
                  dateStart: this.date[0],
                  dateEnd: this.date[1]
                }))
            )
        })
      this.tableData = tableData
      this.loading = false
    },
    initDimensionList (IndicatorInfo) {
      console.log(IndicatorInfo, 'IndicatorInfo')
      this.dimensionList = IndicatorInfo.expandDims
        .map(item => ({
          ...item,
          col: item.dimCol,
          wdzval: ['全部']
        }))
        .filter(item => item.fieldType !== 'time' || item.dimType !== 'time')

      this.currentIndicatorInfo = {
        ...IndicatorInfo,
        indCode: IndicatorInfo.steps.find(item => item.ctype === 1).stepIndCode,
        lxbm: IndicatorInfo.steps.find(item => item.ctype === 1).lxbm
      }
      IndicatorInfo.expandDims.forEach(item => {
        if (item.fieldType === 'time' || item.dimType === 'time') {
          this.isHasDateDim = true
        }
      })

      this.calculation()
    },
    toggleCollapse () {
      this.isCollapsed = !this.isCollapsed
    }
  }
}
</script>

<style scoped lang="scss">
.sub-title {
  display: flex;
  align-items: center;
  margin-bottom: 24px;

  .sub-text {
    font-size: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #1d2129;
    margin-right: 5px;
  }

  .sub-line {
    height: 1px;
    flex: 1;
    background: #e5e6eb;
  }
}
.dimName {
  flex: 0 0 50%;

  border: 1px solid #dcdfe6;
  padding-left: 15px;
  height: 32px;
  line-height: 32px;
  border-radius: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
::v-deep .el-row {
  margin-bottom: 0;
}
</style>
