<template>
  <div class="rolling-table-container">
    <!-- 表格头部 -->
    <div class="table-header">
      <div v-for="(column, index) in Object.keys(data[0])" :key="index" class="header-cell">
        {{ column }}
      </div>
    </div>

    <!-- 滚动表格内容 -->
    <div class="table-body">
      <vue-seamless-scroll
        :data="data"
        :class-option="scrollOptions"
        class="seamless-warp"
      >
        <div class="table-content">
          <div
            v-for="(row, rowIndex) in data"
            :key="rowIndex"
            class="table-row"
          >
            <div
              v-for="(column, colIndex) in Object.keys(data[0])"
              :key="colIndex"
              class="table-cell"
            >
              <!-- 普通文本 -->
              {{ row[column] || "-" }}
            </div>
          </div>
        </div>
      </vue-seamless-scroll>
    </div>
  </div>
</template>

<script>
import vueSeamlessScroll from "vue-seamless-scroll"

export default {
  name: "RollingTable",
  components: {
    vueSeamlessScroll
  },
  props: {
    // 表格数据
    data: {
      type: Array,
      default: () => []
    },
    // 滚动配置
    scrollConfig: {
      type: Object,
      default: () => ({
        step: 0.5, // 滚动速度
        limitMoveNum: 2, // 开始滚动的数据量
        hoverStop: true, // 鼠标悬停停止滚动
        direction: 1, // 滚动方向 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000 // 单步运动停止的时间(默认值1000ms)
      })
    },
    // 行高
    rowHeight: {
      type: [String, Number],
      default: "40px"
    }
  },
  computed: {
    // 表格数据
    tableData() {
      return this.data || []
    },

    computed: {
      // 提取数据中的值列表
      columns() {
        if (this.data.length === 0) return []
        // 获取第一个对象的所有值
        return Object.keys(this.data[0])
      }
    },
    // 容器高度
    containerHeight() {
      return typeof this.height === "number" ? `${this.height}px` : this.height
    },
    // 表格主体高度
    bodyHeight() {
      const titleHeight = this.showTitle ? 40 : 0
      const headerHeight = 40
      const totalHeight =
        typeof this.height === "number" ? this.height : parseInt(this.height)
      return `${totalHeight - titleHeight - headerHeight}px`
    },
    // 滚动配置
    scrollOptions() {
      return {
        ...this.scrollConfig,
        singleHeight: this.getSingleHeight()
      }
    }
  },
  methods: {
    // 计算单行高度
    getSingleHeight() {
      return typeof this.rowHeight === "number"
        ? this.rowHeight
        : parseInt(this.rowHeight)
    }
  }
}
</script>

<style scoped lang="scss">
.rolling-table-container {
  width: 100%;
  background: #fff;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .table-title {
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    color: #fff;
  }

  .table-header {
    display: flex;
    background: #f5f7fa;
    border-bottom: 1px solid #ebeef5;

    .header-cell {
      flex: 1;
      height: 40px;
      line-height: 40px;
      padding: 0 10px;
      font-weight: bold;
      color: #606266;
      border-right: 1px solid #ebeef5;

      &:last-child {
        border-right: none;
      }
    }
  }

  .table-body {
    overflow: hidden;

    .seamless-warp {
      height: 100%;
      overflow: hidden;
    }

    .table-content {
      .table-row {
        display: flex;
        min-height: 40px;
        border-bottom: 1px solid #ebeef5;
        transition: background-color 0.3s;

        &.even-row {
          background: #fafafa;
        }

        &.odd-row {
          background: #fff;
        }

        &:hover {
          background: #f0f9ff;
        }

        .table-cell {
          flex: 1;
          display: flex;
          align-items: center;
          padding: 8px 10px;
          border-right: 1px solid #ebeef5;
          color: #606266;
          font-size: 14px;
          word-break: break-all;

          &:last-child {
            border-right: none;
          }

          // 支持HTML内容的样式
          ::v-deep div {
            width: 100%;
          }
        }
      }
    }
  }
}

// 深色主题
.rolling-table-container.dark-theme {
  background: #2d3748;
  color: #e2e8f0;

  .table-title {
    background: linear-gradient(90deg, #4a5568 0%, #2d3748 100%);
  }

  .table-header {
    background: #4a5568;
    border-bottom-color: #718096;

    .header-cell {
      color: #e2e8f0;
      border-right-color: #718096;
    }
  }

  .table-content {
    .table-row {
      border-bottom-color: #718096;

      &.even-row {
        background: #4a5568;
      }

      &.odd-row {
        background: #2d3748;
      }

      &:hover {
        background: #553c9a;
      }

      .table-cell {
        color: #e2e8f0;
        border-right-color: #718096;
      }
    }
  }
}
</style>
