<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="780px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      v-show="step == 1"
      ref="form"
      :model="form"
      :rules="rules"
      label-width="80px"
    >
      <el-form-item label="名称" prop="dimDefinition.dimName">
        <el-input
          v-model="form.dimDefinition.dimName"
          placeholder="请输入名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="标签" prop="tags">
        <el-input
          v-model="form.dimDefinition.tags"
          placeholder="请输入标签"
        ></el-input>
      </el-form-item>
      <el-form-item label="版本" prop="dimDefinition.version">
        <el-input
          v-model="form.dimDefinition.version"
          placeholder="请输入版本"
        ></el-input>
      </el-form-item>
      <el-form-item label="描述">
        <el-input
          type="textarea"
          :rows="4"
          placeholder="请输入描述"
          v-model="form.dimDefinition.description"
        ></el-input>
      </el-form-item>
      <el-form-item label="维度类型" prop="dimDefinition.categoryCode">
        <el-select
          v-model="form.dimDefinition.categoryCode"
          placeholder="请选择维度类型"
        >
          <el-option
            v-for="item in parent.dimensionTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="维度值">
        <el-radio-group v-model="form.createType">
          <el-radio :label="0">基于数据源创建</el-radio>
          <el-radio :label="1">手动添加</el-radio>
        </el-radio-group>
      </el-form-item>
      <template v-if="form.createType == 0">
        <el-form-item label="更新频率">
          <el-select
            v-model="form.dimDefinition.updateFrequency"
            placeholder="请选择维度类型"
          >
            <el-option
              v-for="item in updateFrequencys"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="更新方式">
          <el-radio-group v-model="form.dimDefinition.updateType">
            <el-radio :label="0">增量同步</el-radio>
            <el-radio :label="1">全量同步</el-radio>
          </el-radio-group>
        </el-form-item> -->
      </template>
    </el-form>

    <div class="step2" v-show="step == 2">
      <div class="table-list">
        <el-input
          placeholder="输入关键字搜索"
          prefix-icon="el-icon-search"
          v-model="key"
          class="search-input"
          @input="getSearchTable"
        ></el-input>
        <div
          class="checkbox-warp"
          v-loading="loading"
          v-load-more="handleScroll"
        >
          <el-checkbox-group v-model="tableNames">
            <el-checkbox
              v-for="item in dataDomainList"
              :label="item.id"
              :key="item.id"
            >
              <svg-icon icon-class="sjy" />
              {{ item.a }}/{{ item.c }}/{{ item.id }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
    </div>

    <div class="step3" v-show="step == 3">
      <!-- 建立该维度使用的表格   -->
      <div class="dimension-table">
        <div class="dimension-head">
          <div class="dimension-head-text">建立该维度使用的表格</div>
          <el-button type="text" @click="innerDialog.dialogVisible = true">
            添加
          </el-button>
        </div>
        <div class="dimension-body">
          <el-tree
            ref="tree"
            :data="treeData"
            :props="{
              children: 'children',
              label: 'label',
              isLeaf: 'leaf'
            }"
            node-key="id"
            :expand-on-click-node="false"
            class="tree"
            draggable
            lazy
            :load="loadNode"
            :allow-drop="allowDrop"
            :allow-drag="allowDrag"
            @node-drag-start="handleTreeDragStart"
            @node-drag-end="handleTreeDragEnd"
          >
            <span
              slot-scope="{ node }"
              class="custom-node"
              :style="{
                cursor: node.level === 2 ? 'move' : 'default'
              }"
              @click="handleNodeClick(node)"
            >
              {{ node.label }}

              <span style="margin-left: auto">
                {{ node.level === 2 ? node.data.comment : '' }}
              </span>
            </span>
          </el-tree>
          <div class="dimension-value-preview" v-if="previewList.length">
            {{ currentNode.label }}下字段值预览：
            <span v-for="(item, index) in previewList" :key="index">
              {{ item }}
            </span>
          </div>
        </div>
      </div>
      <div class="drop-zone">
        <h3>维度层级</h3>
        <div
          @dragover="handleDragOver"
          @drop="handleTargetDrop"
          class="target-container"
          ref="targetContainer"
        >
          <div v-if="!form.dimLevels.length" class="empty-prompt">
            请从上方已选择的表中，拖拽字段到此处
          </div>
          <draggable
            v-model="form.dimLevels"
            group="dimension"
            animation="500"
            handle=".mover"
          >
            <transition-group>
              <div
                v-for="item in form.dimLevels"
                class="dim-item"
                :key="item.id"
              >
                <span class="mover"></span>
                {{ item.label }}
                <span class="dim-cname">
                  {{ item.comment }}

                  <i
                    class="el-icon-delete"
                    @click.stop="handleDeleteDimLevel(item)"
                    style="color: red; cursor: pointer"
                  ></i>
                </span>
              </div>
            </transition-group>
          </draggable>
        </div>
      </div>
      <div class="table-relation" v-if="relations.length">
        <h3>表关联关系（多个表时出现）</h3>
        <div class="relations">
          <div
            class="relation-item"
            v-for="(item, index) in relations"
            :key="index"
          >
            <div class="left-table" v-if="index == 0">
              <div class="inner-text">{{ item.leftTable }}</div>
            </div>
            <div class="left-icon" v-else></div>

            <el-popover
              placement="right"
              width="400"
              trigger="click"
              content="xxx"
            >
              <div class="popover-content">
                <el-radio-group v-model="item.joinType">
                  <el-radio-button label="LEFT">左关联</el-radio-button>
                  <el-radio-button label="INNER">内关联</el-radio-button>
                  <el-radio-button label="RIGHT">右关联</el-radio-button>
                </el-radio-group>
                <div class="fields" style="display: flex; align-items: center">
                  <div class="right-field">
                    <p>左键</p>

                    <el-select v-model="item.leftField" placeholder="请选择">
                      <el-option
                        v-for="item in talbeMap[item.leftTable]"
                        :key="item.name"
                        :label="item.comment"
                        :value="item.name"
                      ></el-option>
                    </el-select>
                  </div>
                  <p style="min-width: 60px; text-align: center">等于</p>
                  <div class="right-field">
                    <p>右键</p>
                    <el-select v-model="item.rightField" placeholder="请选择">
                      <el-option
                        v-for="item in talbeMap[item.rightTable]"
                        :key="item.name"
                        :label="item.comment"
                        :value="item.name"
                      ></el-option>
                    </el-select>
                  </div>
                </div>
              </div>

              <div
                class="relation-icon"
                :class="item.joinType"
                slot="reference"
              ></div>
            </el-popover>
            <div class="right-table">
              <div class="inner-text">{{ item.rightTable }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>

      <el-button type="primary" v-if="step > 1" @click="step--">
        上一步
      </el-button>
      <el-button
        type="primary"
        v-if="form.createType === 1 || step == 3"
        @click="createDimension"
        :disabled="form.dimLevels.length === 0 && step == 3"
        :loading="createLoading"
      >
        创建维度
      </el-button>
      <el-button
        type="primary"
        v-else
        :disabled="step == 2 && !this.tableNames.length"
        @click="handleNext"
      >
        下一步
      </el-button>
    </span>

    <el-dialog
      title="选择数据表"
      :visible.sync="innerDialog.dialogVisible"
      width="780px"
      append-to-body
    >
      <div class="table-list">
        <el-input
          placeholder="输入关键字搜索"
          prefix-icon="el-icon-search"
          v-model="key"
          class="search-input"
          @input="getSearchTable"
        ></el-input>
        <div
          class="checkbox-warp"
          v-loading="loading"
          v-load-more="handleScroll"
        >
          <el-checkbox-group v-model="tableNames">
            <el-checkbox
              v-for="item in dataDomainList"
              :label="item.id"
              :key="item.id"
            >
              <svg-icon icon-class="sjy" />
              {{ item.a }}/{{ item.c }}/{{ item.id }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="innerDialog.dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="selectTable">确 定</el-button>
      </span>
    </el-dialog>
  </el-dialog>
</template>

<script>
import _ from 'lodash'
import Request from '@/service'
import draggable from 'vuedraggable'
export default {
  components: { draggable },
  // 自定义指定
  directives: {
    loadMore: {
      bind: function (el, binding) {
        el.addEventListener('scroll', function () {
          const threshold = 2 // 容差像素
          const isBottom =
            el.scrollHeight - el.scrollTop - el.clientHeight <= threshold
          if (isBottom) {
            binding.value()
          }
        })
      }
    }
  },
  props: {},
  data () {
    return {
      createLoading: false,
      currentNode: {},
      step: 1,
      dialogTitle: '新建图表',
      dialogVisible: false,
      treeData: [],

      form: {
        dimDefinition: {
          categoryCode: '',
          dimName: '',
          description: '',
          version: 'v1.0',
          tags: '',
          updateFrequency: 1,
          updateType: 0
        },
        createType: 1,
        dimLevels: []
      },
      rules: {
        'dimDefinition.dimName': [
          { required: true, message: '请输入维度名称', trigger: 'blur' }
        ],
        'dimDefinition.version': [
          { required: true, message: '请输入维度版本', trigger: 'blur' },
          {
            validator: async (rule, value, callback) => {
              if (!value) return callback()
              try {
                // 检查版本是否存在
                const { data } = await Request.api.paramPost(
                  '/DimManage/dimVerify',
                  {
                    dimName: this.form.dimDefinition.dimName,
                    version: value
                  }
                )
                if (data) {
                  callback(new Error('该版本已存在，请输入其他版本号'))
                } else {
                  callback()
                }
              } catch (e) {
                callback() // 网络错误等不阻断
              }
            },
            trigger: 'blur'
          }
        ],
        'dimDefinition.categoryCode': [
          { required: true, message: '请选择维度类型', trigger: 'change' }
        ]
      },
      optionsMap: {},
      sameDimension: [],
      innerDialog: {
        title: '提示',
        dialogVisible: false,
        content: ''
      },
      dataDomainList: [],
      tableNames: [],
      loading: false,
      key: '',

      previewList: [],

      updateFrequencys: [
        {
          label: '按日',
          value: 1
        },
        {
          label: '按周',
          value: 2
        },
        {
          label: '按月',
          value: 3
        },
        {
          label: '按学期',
          value: 4
        },
        {
          label: '按学年',
          value: 5
        },
        {
          label: '按年',
          value: 6
        }
      ],
      relations: [],
      talbeMap: {},
      page: {
        currentPage: 1,
        pageSize: 30,
        total: 0
      }
    }
  },
  inject: ['parent'],
  computed: {},
  created () {},
  mounted () {},
  watch: {
    'form.dimLevels': {
      handler () {
        console.log('监听到了')
        const sourceTables = this.form.dimLevels.map(item => item.sourceTable)
        const uniqueSourceTables = [...new Set(sourceTables)]

        console.log(uniqueSourceTables, 'sourceTables')
        if (uniqueSourceTables.length > 1) {
          // 初始化维度表关联关系
          this.relations = uniqueSourceTables
            .slice(0, uniqueSourceTables.length - 1)
            .map((item, index) => ({
              definitionCode: '',
              leftTable: item,
              leftField: '',
              rightTable: uniqueSourceTables[index + 1],
              rightField: '',
              joinType: 'LEFT'
            }))
        } else {
          this.relations = []
        }
      },
      deep: true
    }
  },
  methods: {
    openDialog () {
      this.dialogTitle = '维度属性'
      this.dialogVisible = true
      this.key = ''
      this.previewList = []
      this.tableNames = []
      this.currentNode = {}
      this.step = 1
      this.form = {
        dimDefinition: {
          categoryCode: '',
          dimName: '',
          description: '',
          version: 'v1.0',
          tags: '',
          updateFrequency: 1,
          updateType: 0
        },
        createType: 1,
        dimLevels: []
      }
      this.getTableList()
    },
    allowDrop () {
      return false
    },
    // tree节点能否被拖拽
    allowDrag (draggingNode) {
      console.log(draggingNode, '拖拽节点')
      if (draggingNode.level === 2) {
        return true
      }
    },
    // tree拖拽开始
    handleTreeDragStart (node, event) {
      console.log(node)
      if (node.level === 2) {
        this.isDragging = true
        // 在拖拽开始时设置拖拽节点的数据
        event.dataTransfer.setData('draggingItem', JSON.stringify(node.data))
      } else {
        return false
      }
    },
    handleDragOver (e) {
      e.preventDefault()
    },
    handleTargetDrop (e) {
      if (e.target === this.$refs.targetContainer) {
        // 阻止默认行为
        e.preventDefault()
        const data = JSON.parse(e.dataTransfer.getData('draggingItem'))
        console.log(data, '拖拽节点')

        // 判断是否重复
        const isDuplicate = this.form.dimLevels.some(
          item =>
            item.sourceTable === data.sourceTable && item.name === data.name
        )

        if (isDuplicate) {
          this.$message.warning('来源表和字段重复，请重新选择！')
          return
        }

        // 添加到维度层级
        this.form.dimLevels.push(data)
        console.log(this.relations, 'this.relations')
      }
    },
    // 创建维度
    createDimension () {
      this.createLoading = true
      this.$refs.form.validate(async valid => {
        if (valid) {
          // this.relations中rightField和leftField必填项
          let flag = false
          this.relations.forEach(item => {
            if (!item.rightField || !item.leftField) {
              flag = true
            }
          })
          if (flag) {
            this.$message.warning('请选择表关联键值')

            this.createLoading = false

            return
          }
          console.log(this.form, 'this.form')
          try {
            const { data } = await Request.api.paramPost('/DimManage/addDim', {
              ...this.form,
              dimDefinition: {
                ...this.form.dimDefinition,
                categoryName: this.parent.dimensionTypeList.find(
                  item => item.value === this.form.dimDefinition.categoryCode
                ).label
              },
              dimLevels: this.form.dimLevels.map((item, index) => {
                return {
                  level: index + 1,
                  levelName:
                    item.comment || '层级' + this.numberToChinese(index + 1),
                  sourceTable: item.sourceTable,
                  sourceField: item.name,
                  fieldValueType: item.type
                }
              }),
              configs: this.relations
            })
            this.$message.success('创建成功')
            this.dialogVisible = false
            this.createLoading = false

            // this.$emit("refresh")
            this.$emit('createDimension', data)
          } catch (error) {
            this.$message.error('创建失败')
            this.createLoading = false
            return
          }
        } else {
          this.createLoading = false
          return false
        }
      })
    },
    getSearchTable () {
      this.page.currentPage = 1
      this.dataDomainList = []

      this.getTableList()
    },
    handleScroll () {
      console.log('ffff')
      if (!this.loading && this.dataDomainList.length < this.page.total) {
        this.page.currentPage++
        this.getTableList()
      }
    },
    getTableList: _.debounce(
      function () {
        this.loading = true
        this.$httpBi.indicatorAnagement
          .getTableList({
            key: this.key,
            currentPage: this.page.currentPage,
            pageSize: this.page.pageSize
          })
          .then(res => {
            this.dataDomainList.push(...res.data.list)
            this.page.total = res.data.totalCount
            this.loading = false
          })
      },
      300,
      {
        leading: true
      }
    ),
    handleNext () {
      if (this.step === 1) {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.step++
          }
        })
      } else if (this.step === 2) {
        this.selectTable()
        this.step++
      }
    },
    // 选择表
    async selectTable () {
      this.treeData = this.tableNames.map(item => {
        return {
          id: item,
          label: item
        }
      })
      console.log(this.treeData, 'this.treeData')
      // 获取选择表的字段
      await Promise.all(
        this.treeData.map(async item => {
          const { data } = await Request.api.paramGet(
            '/bi/source/getTableAndColumn',
            {
              dbName: 'ods',
              tableName: item.label
            }
          )
          this.talbeMap[item.label] = data.columns
        })
      )
      //  const { data } =await Request.api.paramPost("/DimManage/autoJoinPath",
      //   this.treeData.map(item => {
      //     return {
      //       tableName: item.label,
      //       fieldNames:this.talbeMap[item.label].map(item => item.name),
      //     }
      //   })
      //  )
      // console.log(this.talbeMap, " this.talbeMap")
      // // 初始化维度表关联关系
      // this.relations = data.map((item, ) => ({
      //   definitionCode: "",
      //   ...item,
      //   joinType: "LEFT"
      // }))
      // console.log(this.relations, " this.this.relations")

      this.innerDialog.dialogVisible = false
    },
    async loadNode (node, resolve) {
      console.log(node, 'node')
      if (node.level === 0) {
        return resolve([])
      }

      const { data } = await Request.api.paramGet(
        '/bi/source/getTableAndColumn',
        {
          dbName: 'ods',
          tableName: node.data.label
        }
      )
      resolve(
        data.columns.map(item => ({
          ...item,
          id: item.name,
          label: item.name,
          sourceTable: data.name,
          leaf: true
        }))
      )
    },
    async handleNodeClick (node) {
      console.log(node, 'node')
      if (node.level === 2) {
        this.currentNode = node.data
        const { data } = await Request.api.paramGet(
          '/bi/source/getColumnValues',
          {
            dbName: 'ods',
            tableName: node.data.sourceTable,
            columnName: node.data.label,
            currentPage: 1,
            pageSize: 3
          }
        )
        console.log(data, 'data')
        this.previewList = data.list
        // this.$refs.tree.setCurrentKey(node.id)
        // this.$refs.tree.setCheckedKeys([node.id])
      }
    },
    nextStep () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.getTableList()
          this.step = 2
        } else {
          return false
        }
      })
    },
    numberToChinese (num) {
      const chineseNumbers = [
        '零',
        '一',
        '二',
        '三',
        '四',
        '五',
        '六',
        '七',
        '八',
        '九'
      ]
      return chineseNumbers[num]
    },
    handleDeleteDimLevel (item) {
      const index = this.form.dimLevels.findIndex(level => level.id === item.id)
      if (index > -1) {
        this.form.dimLevels.splice(index, 1)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.step3 {
  .dimension-table {
    .dimension-head {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .dimension-head-text {
        height: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #222222;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        margin-bottom: 16px;
      }
    }

    .dimension-body {
      display: flex;
      height: 200px;
      background: #f5f7fa;
      border-radius: 8px;
      overflow: auto;
      .el-tree {
        width: 50%;
        background: #f5f7fa;
      }

      .dimension-value-preview {
        border-left: 1px solid #edeff0;
        width: 50%;
        display: flex;
        flex-direction: column;
        padding-top: 10px;
        padding-left: 10px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #222222;
        line-height: 14px;
        text-align: left;
        font-style: normal;
        span {
          margin-top: 10px;
        }
      }
    }
  }

  .drop-zone {
    width: 100%;

    margin: 20px 0;
    h3 {
      height: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #222222;
      line-height: 16px;
      text-align: left;
      font-style: normal;
      margin-bottom: 16px;
    }
    .target-container {
      background: #f5f7fa;
      border-radius: 8px;
      height: 200px;
      padding: 20px;
      box-sizing: border-box;
      position: relative;

      .empty-prompt {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #909399;
        font-size: 14px;
        text-align: center;
      }

      .dim-item {
        height: 20px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        line-height: 12px;
        text-align: left;
        font-style: normal;
        margin-bottom: 10px;
        display: flex;
        .mover {
          width: 12px;
          height: 12px;
          background: url('~@/assets/images/drag.png') no-repeat;
          cursor: move;
        }
        .dim-cname {
          margin-left: auto;
        }
      }
    }
  }
  .table-relation {
    h3 {
      height: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #222222;
      line-height: 16px;
      text-align: left;
      font-style: normal;
      margin-bottom: 16px;
    }
    .relations {
      width: 100%;
      height: 72px;
      background: #f5f7fa;
      border-radius: 8px;
      padding: 20px;
      box-sizing: border-box;
      display: flex;
      overflow-x: auto;
      .relation-item {
        display: flex;
        align-items: center;
        .left-table,
        .right-table {
          position: relative;
          width: 166px;
          height: 32px;
          background: #ffffff;
          border-radius: 6px;
          border: 1px solid #1563ff;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #222222;
          line-height: 32px;
          text-align: center;
          font-style: normal;
          white-space: nowrap;
          .inner-text {
            width: 100%;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }
        .left-table {
          position: relative;
          z-index: 99;
          &::after {
            position: absolute;
            top: 15px;
            right: -24px;
            content: '';
            width: 24px;
            height: 1px;
            border: 1px solid #1563ff;
          }
          &::before {
            content: '';
            width: 4px;
            height: 8px;
            background: #1563ff;
            position: absolute;
            border-radius: 0 4px 4px 0;
            top: 12px;
            right: -4px;
            // transform: translate(100%);
          }
        }
        .left-icon {
          position: relative;
          z-index: 99;
          height: 32px;
          &::after {
            position: absolute;
            top: 15px;
            right: -24px;
            content: '';
            width: 24px;
            height: 1px;
            border: 1px solid #1563ff;
          }
          &::before {
            content: '';
            width: 4px;
            height: 8px;
            background: #1563ff;
            position: absolute;
            border-radius: 0 4px 4px 0;
            top: 12px;
            right: -4px;
            // transform: translate(100%);
          }
        }
        .right-table {
          position: relative;
          z-index: 99;
          &::after {
            position: absolute;
            top: 15px;
            left: -24px;
            content: '';
            width: 24px;
            height: 1px;
            border: 1px solid #1563ff;
          }
          &::before {
            content: '';
            width: 4px;
            height: 8px;
            background: #1563ff;
            position: absolute;
            border-radius: 4px 0 0 4px; /* 左上 + 左下为圆角 */
            top: 12px;
            left: -4px;
            // transform: translate(100%);
          }
        }
        .relation-icon {
          width: 40px;
          height: 24px;
          margin: 0 24px;
          cursor: pointer;
          &.LEFT {
            background: url('~@/assets/images/LEFT.png') no-repeat;
          }
          &.RIGHT {
            background: url('~@/assets/images/RIGHT.png') no-repeat;
          }
          &.INNER {
            background: url('~@/assets/images/INNER.png') no-repeat;
          }
        }
      }
    }
  }

  .drop-zone.drag-over {
    border-color: #409eff;
    background: #f0f7ff;
  }
}
.custom-node {
  display: flex;
  width: calc(100% - 80px);
}
// .dialog-footer {
// }

::v-deep .el-dialog__header {
  margin: 0 24px;
  padding: 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #edeff0;
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #222222;
}

::v-deep .el-dialog__body {
  padding: 20px 24px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}

.table-list {
  width: 640px;
  margin: 20px auto;

  .checkbox-warp {
    height: calc(100vh - 420px);
    overflow: auto;
    margin-top: 24px;
  }

  ::v-deep .el-checkbox-group {
    display: flex;
    flex-direction: column;
  }

  ::v-deep .el-checkbox {
    margin-bottom: 24px;
  }

  //滚动条样式
  ::-webkit-scrollbar {
    width: 2px;
    height: 2px;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #cbced1;
  }

  ::-webkit-scrollbar-track {
    border-radius: 3px;
    background-color: transparent;
  }
}
</style>
<style lang="scss">
.drag-element {
  /* 禁止文本选择 */
  user-select: none;
  /* 禁用默认拖拽效果 */
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

#project_frame
  .el-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .el-tree-node__expand-icon {
  background-color: transparent !important;
}

#project_frame
  .model-tree
  .el-tree-node.is-current
  > .el-tree-node__content:has(> span.item-style) {
  position: relative;
  background: #f4f7ff !important;
  box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
    0px 6px 6px -4px rgba(0, 42, 128, 0.12);
  border-radius: 4px;
  border: 1px solid #1563ff;
  cursor: move;

  .el-tree-node__label {
    background: transparent !important;
  }
}

.el-tree-node.dragging > .el-tree-node__content {
  opacity: 0.2;
}

.el-tree-node > .el-tree-node__content {
  &.is-current {
    background: #f5f7fa !important;
  }

  &.is-focusable {
    background: #f5f7fa !important;
  }

  &:hover {
    background: #f5f7fa !important;

    > .el-checkbox {
      background-color: transparent !important;
    }

    .el-tree-node__expand-icon {
      background-color: transparent !important;

      border-top-left-radius: 2px;
      border-bottom-left-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }

    .custom-tree-node,
    .el-tree-node__label {
      background-color: transparent !important;

      border-top-right-radius: 2px;
      border-bottom-right-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }

    .custom-tree-btns {
      opacity: 1;
    }
  }
}

#project_frame .el-tree .el-tree-node.is-current > .el-tree-node__content {
  background-color: #f5f7fa !important;

  .el-tree-node__label {
    background-color: transparent;
  }

  .custom-tree-btns {
    opacity: 1;
  }
}

#project_frame
  .el-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .custom-tree-node,
#project_frame
  .el-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .el-tree-node__label {
  background-color: transparent !important;
}

.custom-tree-node {
  padding: 0 10px;
  width: 100%;
  display: flex;
  justify-content: space-between;

  .custom-tree-btns {
    opacity: 0;
  }
}
</style>
