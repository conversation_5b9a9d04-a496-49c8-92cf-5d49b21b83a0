<template>
  <dt-single-page-view
    class="editPage"
    :inner-style="{ textAlign: 'left' }"
    :show="true"
    v-loading="loading"
    element-loading-background="rgba(0, 0, 0, 0)"
  >
    <dt-header>
      <el-page-header
        @back="goBack"
        :content="id == '' ? '新增' : '修改'"
      ></el-page-header>
    </dt-header>
    <el-form
      :model="form"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      label-position="right"
    >
      <el-form-item label="大屏名称" prop="name">
        <el-input v-model="form.name" class="my_with"></el-input>
      </el-form-item>
      <el-form-item label="主题场景" prop="theme">
        <el-select
          v-model="form.theme"
          placeholder="请选择主题"
          class="my_with"
        >
          <el-option
            v-for="item in parent.themes"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="大屏类型" prop="type">
        <el-radio-group v-model="form.type" :disabled="form.id ? true : false">
          <el-radio
            :label="item.value"
            v-for="(item, key) in typeList"
            :key="key"
          >
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <template v-if="form.type === 2">
        <el-form-item label="学校LOGO：" prop="schoolLogo">
          <div class="pic-box">
            <el-button
              v-if="form.schoolLogo"
              class="el-icon-close"
              style="
                position: absolute;
                left: 284px;
                top: -10px;
                font-size: 16px;
                padding: 4px;
              "
              circle
              type="danger"
              @click="form.schoolLogo = ''"
            ></el-button>
            <el-upload
              class="avatar-uploader"
              action="#"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUploadSchoolLogo"
            >
              <img
                v-if="form.schoolLogo"
                :src="form.schoolLogo"
                class="pic_w_h"
              />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </div>
        </el-form-item>

        <el-form-item label="缩略图：" prop="icon">
          <div class="pic-box">
            <el-button
              v-if="form.icon"
              class="el-icon-close"
              style="
                position: absolute;
                left: 284px;
                top: -10px;
                font-size: 16px;
                padding: 4px;
              "
              circle
              type="danger"
              @click="form.icon = ''"
            ></el-button>
            <el-upload
              class="avatar-uploader"
              action=""
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <img v-if="form.icon" :src="form.icon" class="pic_w_h" />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </div>
        </el-form-item>

        <el-form-item label="大屏路由">
          <el-input
            v-model="form.router"
            placeholder="请输入链接"
            class="my_with"
          ></el-input>
        </el-form-item>
        <el-form-item label="关联图表模型" v-if="form.id">
          <el-select-v2
            multiple
            v-model="form.charts"
            placeholder="请选择图表模型"
            class="my_with"
            filterable
            :options="chartCodeList"
            :props="{
              label: 'chartName',
              value: 'chartCode'
            }"
          ></el-select-v2>
        </el-form-item>
        <el-form-item label="启用静态数据">
          <el-switch
            v-model="form.isStaticData"
            :active-value="1"
            :inactive-value="0"
          ></el-switch>
        </el-form-item>
        <el-form-item label="静态数据" v-if="form.isStaticData === 1">
          <div class="content" style="height: 500px; width: 100%">
            <AceEditor v-model="form.staticData" />
          </div>
        </el-form-item>
      </template>
      <template v-if="form.type === 3">
        <el-form-item label="缩略图：" prop="icon">
          <div class="pic-box">
            <el-button
              v-if="form.icon"
              class="el-icon-close"
              style="
                position: absolute;
                left: 284px;
                top: -10px;
                font-size: 16px;
                padding: 4px;
              "
              circle
              type="danger"
              @click="form.icon = ''"
            ></el-button>
            <el-upload
              class="avatar-uploader"
              action=""
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <img v-if="form.icon" :src="form.icon" class="pic_w_h" />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </div>
        </el-form-item>
        <el-form-item label="链接地址">
          <el-input
            v-model="form.router"
            placeholder="请输入"
            class="my_with"
          ></el-input>
        </el-form-item>
      </template>

      <!-- <el-form-item label="显示水印" prop="url">
        <el-switch
          v-model="value"
          active-color="#13ce66"
          inactive-color="#ff4949"
        >
        </el-switch>
      </el-form-item> -->
      <el-form-item label="大屏描述" prop="info">
        <el-input
          v-model="form.info"
          type="textarea"
          :rows="2"
          class="my_with"
        ></el-input>
      </el-form-item>
      <el-form-item label="权限设置" prop="permission">
        <el-radio-group v-model="form.permission">
          <el-radio
            :label="item.value"
            v-for="(item, key) in permissionList"
            :disabled="item.value === 2 && id == ''"
            :key="key"
          >
            {{ item.label }}
          </el-radio>
        </el-radio-group>
        <el-tooltip
          class="item"
          effect="dark"
          content="角色权限设置,需要新增保存后可编辑"
          placement="top"
        >
          <span style="margin-left: 5px" v-if="id == ''">
            <i class="el-icon-question"></i>
          </span>
        </el-tooltip>
      </el-form-item>
      <el-form-item
        ref="role"
        class="is-required"
        :label="'角色'"
        prop="roles"
        v-if="form.permission === 2"
        :rules="[{ validator: rules.rolesRule }]"
      >
        <el-scrollbar class="block-view" style="height: 220px">
          <el-checkbox-group
            v-model="form.roles"
            @change="validateFormSlot('role')"
          >
            <div
              class="checkbox-item-block"
              v-for="item in roleList"
              :key="item.roleId"
            >
              <el-checkbox :label="item.roleId">
                <div class="checkbox-item">
                  <div>{{ item.roleTitle }}</div>
                  <div class="item-content">{{ item.remark }}</div>
                </div>
              </el-checkbox>
            </div>
          </el-checkbox-group>
        </el-scrollbar>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="addSubmit()">确 定</el-button>
        <el-button @click="reset_from()">重置</el-button>
      </el-form-item>
    </el-form>
  </dt-single-page-view>
</template>

<script>
import AceEditor from '@/components/AceEditor'

import Request from '@/service'
import Axios from 'axios'
export default {
  name: 'edit-page',
  components: { AceEditor },
  directives: {
    loadMore: {
      bind: (el, binding) => {
        const SELECTWRAP_DOM = el.querySelector(
          '.el-select-dropdown .el-select-dropdown__wrap'
        )
        SELECTWRAP_DOM.addEventListener('scroll', function () {
          const CONDITION =
            this.scrollHeight - this.scrollTop <= this.clientHeight
          if (CONDITION) {
            binding.value()
          }
        })
      }
    },
    // 自定义指令
    suffixClick: {
      bind: (el, binding) => {
        const suffixElement = el.querySelector('.el-input__suffix')
        if (suffixElement) {
          suffixElement.addEventListener('click', binding.value)
        }
      },
      unbind: (el, binding) => {
        const suffixElement = el.querySelector('.el-input__suffix')
        if (suffixElement) {
          suffixElement.removeEventListener('click', binding.value)
        }
      }
    }
  },
  props: {
    id: String,
    formData: Object
  },
  data () {
    return {
      loading: false,
      title: '新增',
      form: {
        name: '',
        icon: '',
        schoolLogo: '',
        info: '',
        type: 1,
        router: '',
        permission: 0,
        davinciDisplayId: '',
        roles: [],
        theme: '',
        chartModels: [], // 新增：关联的图表模型
        config: {
          displayParams: {
            autoPlay: true,
            autoSlide: 10,
            transitionStyle: 'none',
            transitionSpeed: 'default'
          }
        }
      },
      rules: {
        name: [{ required: true, message: '请输入大屏名称', trigger: 'blur' }],
        theme: [{ required: true, message: '请选择主题场景', trigger: 'blur' }],
        rolesRule: (rule, value, callback) => {
          if (this.form.roles.length === 0) callback(new Error('请选择角色'))
          else callback()
        }
      },
      permissionList: [
        {
          label: '全不可见',
          value: 0
        },
        {
          label: '全部可见',
          value: 1
        },
        {
          label: '角色可见',
          value: 2
        }
      ],
      options: [], // 存储下拉框选项
      chartModelsLoading: false, // 是否正在加载
      current: 1, // 当前分页页码,
      query: '',
      total: 0,
      isInit: false,
      isDropdownOpen: false, // 下拉框是否打开
      roleList: [],
      chartCodeList: []
    }
  },
  inject: ['typeList', 'parent'],
  mounted () {},
  // 修改created生命周期
  async created () {
    if (this.id) {
      this.form = { ...this.form, ...this.formData }
      // 初始化已选中的图表模型数据，确保回显正常
      // await this.initSelectedChartModels()
    }
    this.getAllRole()
    // if (this.form.charts.length){
    // }
      this.getChartCodeList()
  },
  methods: {
    // 加载分页数据
    async getChartCodeList () {
      const { data } = await Request.api.paramPost('/indicator/chart/getPage', {
        pageSize: -1,
        chartName: '',
        currentPage: 1,
        chartCodeList: this.form.charts
      })

      if (data.list) {
        this.chartCodeList = data.list
      }
    },
    async getAllRole () {
      const { data } = await Request.getAllRole()
      this.roleList = data
    },
    goBack () {
      this.$emit('click', { com: 'list', opt: 'back', data: { id: '' } })
    },
    addSubmit () {
      this.validateFormSlot('all')
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          let api = ''
          let p = {}
          if (this.form.type === 1) {
            // BI大屏【敏捷配置】
            api = 'display'
            p = {
              avatar: '',
              config: JSON.stringify(this.form.config),
              description: this.form.info,
              publish: true
            }
          } else if (this.form.type === 2) {
            // 定制大屏
            api = 'screenManage'
          } else {
            // 外链大屏
            api = 'screenManage'
          }
          if (this.form.id) {
            Request[api]
              .updDisplay({ ...this.form, ...p })
              .then(() => {
                this.$emit('click', { com: 'list', opt: 'back', data: {} })
                Request.display
                  .paramPost('/manage/addCharts', {
                    displayId: this.form.id,
                    chartCodes: this.form.charts
                  })
                  .then(() => {
                    this.$message.success('更新成功')
                  })
              })
              .catch(() => {
                this.$message.error('操作失败')
              })
          } else {
            Request[api]
              .addDisplay({ ...this.form, ...p })
              .then(() => {
                this.$emit('click', { com: 'list', opt: 'back', data: {} })
                this.$message.success('新增成功')
              })
              .catch(() => {
                this.$message.error('操作失败')
              })
          }
        } else {
          return false
        }
      })
    },
    reset_from () {
      this.$refs['ruleForm'].resetFields()
    },
    handleAvatarSuccess () {},
    beforeAvatarUploadSchoolLogo (file) {
      let size = file.size / 1024 / 1024
      if (file.type.indexOf('image') < 0) {
        this.$message.warning('只能上传图片文件')
        return false
      }
      if (size > 10) {
        this.$message.warning('文件大小不得超过10M')
        return false
      }

      let fd = new FormData()
      fd.append('file', file)
      Axios({
        method: 'POST',
        url: '/file-api/upms-service/upload',
        headers: { ...this.$utils.auth.getAdminheader() },
        data: fd
      }).then(res => {
        this.form['schoolLogo'] = res.data.data.path
        this.$message.success('图片上传成功')
      })
      return true
    },
    beforeAvatarUpload (file) {
      let size = file.size / 1024 / 1024
      if (file.type.indexOf('image') < 0) {
        this.$message.warning('只能上传图片文件')
        return false
      }
      if (size > 10) {
        this.$message.warning('文件大小不得超过10M')
        return false
      }

      let fd = new FormData()
      fd.append('file', file)
      Axios({
        method: 'POST',
        url: '/file-api/upms-service/upload',
        headers: { ...this.$utils.auth.getAdminheader() },
        data: fd
      }).then(res => {
        this.form['icon'] = res.data.data.path
        return true
      })
      return false
    },
    // 表单插槽校验
    validateFormSlot (ref_key) {
      if (ref_key === 'all') {
        Object.keys(this.$refs).map(
          item =>
            this.$refs[item] &&
            this.$refs[item].validate &&
            this.$refs[item].validate()
        )
      } else {
        this.$refs[ref_key].validate && this.$refs[ref_key].validate()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.my_with {
  width: 300px;
}

.pic_w_h {
  max-width: 300px;
  max-height: 178px;
  border-radius: 5px;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #409eff;
  width: 300px;
  line-height: 178px;
  text-align: center;
  border: 1px dashed #409eff;
  border-radius: 5px;
}

.pic-box {
  position: relative;
}
/deep/.el-form {
  .realInfo-mask {
    position: relative;
    width: 100%;
    height: 32px;
    margin-top: -32px;
    background: transparent;
    cursor: pointer;
    &.disabled {
      cursor: not-allowed;
    }
  }
  .block-view {
    background: #f8f8f8;
    padding: 15px 30px;
    width: 90%;
    max-width: 900px;
    .title {
      font-weight: bolder;
      margin-bottom: 10px;
    }
    .el-radio-group {
      width: 100%;
      .el-radio {
        width: 33%;
        margin-right: 0;
        vertical-align: top;
        .el-radio__input {
          vertical-align: top;
        }
        .radio-item {
          width: 85%;
          font-size: 13px;
          display: inline-block;
          .item-content {
            color: #cccccc;
            margin: 10px 0;
          }
        }
      }
    }
    .el-checkbox-group {
      display: flex;
      flex-wrap: wrap;
      .checkbox-item-block {
        width: 33%;
        height: 100px;
        .el-checkbox {
          background: white;
          width: 90%;
          height: 80px;
          padding: 20px;
          margin-left: 5%;
          margin-top: 10px;
          box-shadow: 0px 0px 10px 2px #f3eeee;
          .el-checkbox__input {
            vertical-align: top;
            margin-top: 3px;
          }
          .el-checkbox__label {
            width: 90%;
            vertical-align: top;
            .checkbox-item {
              font-size: 13px;
              .item-content {
                margin: 10px 0;
                color: #cccccc;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
            }
          }
        }
      }
    }
    &.el-scrollbar {
      padding: 0;
      .el-scrollbar__view {
        padding: 15px;
      }
      .el-scrollbar__bar.is-horizontal {
        display: none;
      }
    }
  }
  .is-error {
    .block-view {
      border: 1px solid #ff4949;
    }
  }
}
.load-more-option {
  padding: 8px 20px;
  text-align: center;
  color: #409eff;
  cursor: pointer;
  border-top: 1px solid #e4e7ed;

  &:hover {
    background-color: #f5f7fa;
  }

  i {
    margin-right: 5px;
  }
}

/deep/.el-select-dropdown__item.load-more-option {
  height: auto;
  line-height: normal;
}
</style>
