<template>
  <div class="rolePage-mainView">
    <!-- 查询 -->
    <DT-Form
      type="search"
      v-model="searchForm"
      :render="searchRender"
      @confirm="handleSearch"
    />
    <!-- 按钮 -->
    <el-button type="primary" icon="el-icon-plus" @click="$emit('handleAdd')">
      新建
    </el-button>
    <!-- 表格 -->
    <DT-Table :data="data" :column="tableColumn">
      <template slot="action" slot-scope="scope">
        <el-button
          type="text"
          size="mini"
          @click="$emit('handleEdit', scope.row)"
        >
          编辑
        </el-button>
        <el-divider direction="vertical"></el-divider>
        <el-button
          type="text"
          size="mini"
          @click="$emit('handlePreview', scope.row)"
        >
          预览
        </el-button>
        <el-divider direction="vertical"></el-divider>
        <el-button
          type="text"
          size="mini"
          @click="$emit('handleShare', scope.row)"
        >
          分享
        </el-button>

        <el-divider direction="vertical"></el-divider>
        <el-button
          type="text"
          size="mini"
          @click="$emit('handleDelete', scope.row)"
        >
          删除
        </el-button>
      </template>
    </DT-Table>
    <!-- 分页 -->
    <DT-Pagination
      :hidden="pagination.total == 0"
      :total="pagination.total"
      :page-size="pagination.pageSize"
      :current-page="pagination.currentPage"
      @sizeChange="handlePageSizeChange"
      @currentChange="handlePageCurrentChange"
    />
  </div>
</template>

<script>
export default {
  name: 'main-view',
  props: {
    // 数据
    data: {
      type: Array,
      default: () => []
    },
    // 搜索（与index同步）
    search: {
      type: Object,
      default: () => {
        return {
          chartName: ''
        }
      }
    },
    // 分页配置
    pagination: {
      type: Object,
      default: () => {
        return {
          total: 0,
          pageSize: 10,
          currentPage: 1
        }
      }
    }
  },
  data () {
    return {
      // 搜索表单内容
      searchForm: {},
      // 搜索渲染配置
      searchRender: [
        {
          label: '图表模型名称',
          type: 'input',
          key: 'chartName',
          props: {
            placeholder: '请输入图表模型名称'
          }
        }
      ],
      chartTypeList: [
        {
          label: '表格',
          value: 'table'
        },
        {
          label: '柱状图',
          value: 'bar'
        },
        {
          label: '水平柱状图',
          value: 'horizontalBar'
        },
        {
          label: '折线图',
          value: 'line'
        },
        {
          label: '折柱混合图',
          value: 'lineBar'
        },
        {
          label: '饼图',
          value: 'pie'
        },
        {
          label: '雷达图',
          value: 'radar'
        },
        {
          label: '翻牌器',
          value: 'flop'
        },
        {
          label: '环形图',
          value: 'circleProgress'
        },
        {
          label: '仪表盘',
          value: 'gauge'
        },
        {
          label: '指标',
          value: 'statistic'
        },
        {
          label: '进度条',
          value: 'lineProgress'
        },
        {
          label: '滚动表格',
          value: 'rollingTable'
        },
        {
          label: '颜色块',
          value: 'colorBlock'
        },
        {
          label: '文本',
          value: 'text'
        },
        {
          label: '通用性',
          value: 'public'
        }
      ],
      // 表格渲染配置
      tableColumn: [
        {
          label: '图表模型名称',
          prop: 'chartName'
        },
        {
          label: '图表模型ID',
          prop: 'chartCode'
        },
        {
          label: '关联指标数',
          formatter: ({ row }) => row.inds + '个',
          width: 120
        },
        {
          label: '图表类型',
          formatter: ({ row }) => this.getChartTypeLabel(row.chartType),
          width: 120
        },

        {
          label: '分享次数',
          width: 100,
          formatter: ({ row }) => row.shares + '次'
        },
        // 操作
        {
          label: '操作',
          width: 180,
          slot: true,
          slotName: 'action',
          button: [
            {
              label: '编辑',
              onClick: ({ row }) => this.$emit('handleEdit', row)
            },
            {
              label: '预览',
              onClick: ({ row }) => this.$emit('handlePreview', row)
            },
            {
              label: '分享',
              onClick: ({ row }) => this.$emit('handleShare', row)
            },
            {
              label: '删除',
              onClick: ({ row }) => this.$emit('handleDelete', row)
            }
          ]
        }
      ]
    }
  },
  mounted () {
    // 页面初始化时配置搜索双向绑定数据，使表单页返回时搜索框数据保持与之前一致
    this.searchForm = { ...this.search }
  },
  methods: {
    // 筛选表单 - 查询
    handleSearch (form) {
      this.pagination.currentPage = 1
      Object.keys(form).forEach(key => (this.search[key] = form[key]))
      this.$emit('search')
    },
    // 分页 - 每页条数改变
    handlePageSizeChange (event) {
      this.pagination.pageSize = event.pageSize
      this.pagination.currentPage = 1
      this.$emit('paginationChange')
    },
    // 分页 - 当前页码改变
    handlePageCurrentChange (event) {
      this.pagination.currentPage = event.currentPage
      this.$emit('paginationChange')
    },
    // 获取图表类型中文
    getChartTypeLabel (chartType) {
      const item = this.chartTypeList.find(item => item.value === chartType)
      return item ? item.label : chartType
    }
  }
}
</script>
