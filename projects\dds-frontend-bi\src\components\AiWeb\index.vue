<template>
  <div class="container flex flex-col">
    <main class="flex-1 overflow-hidden">
      <div
        id="scrollRef"
        ref="scrollRef"
        class="h-full overflow-hidden overflow-y-auto"
      >
        <div
          id="image-wrapper"
          class="w-full max-w-screen-xl m-auto dark:bg-[#101014] p-4"
        >
          <template v-if="!dataSources.length">
            <div
              class="flex items-center flex-col justify-center mt-4 text-center"
            >
              <SvgIcon icon="ri:bubble-chart-fill" class="mr-2 text-3xl" />
              <div style="line-height: 26px; color: #2da2ff">
                你好，我是你的数据助手，可以帮助你快速完成
                <span style="font-weight: bold">问数、数据可视化</span>
                等目标
                <br />
                可以试着问
                <span style="font-weight: bold">“学校有多少学生？”</span>
              </div>
            </div>
          </template>
          <template v-else>
            <Message
              v-for="(item, index) of dataSources"
              :key="index"
              :date-time="item.dateTime"
              :text="item.text"
              :json="item.json"
              :thinkingEnd="item.thinkingEnd"
              :pre-text="dataSources[index - 1] && dataSources[index - 1].text"
              :inversion="item.inversion"
              :error="item.error"
              :duration="item.duration"
              :loading="item.loading"
              @regenerate="onRegenerate(index)"
              @delete="handleDelete(index)"
            />
            <div class="sticky bottom-0 left-0 flex justify-center">
              <el-button v-if="loading" type="warning" @click="handleStop">
                <template #icon>
                  <SvgIcon icon="ri:stop-circle-line" />
                </template>
                停止回答
              </el-button>
            </div>
          </template>
        </div>
      </div>
    </main>

    <!-- <div class="sticky bottom-0 w-full p-6 pb-8">
      <div class="flex">
        <el-input
          class="input"
          type="text"
          placeholder="请输入"
          v-model="prompt"
          @keyup.enter.native="handleSubmit"
        />
        <el-button
          @click="handleSubmit"
          type="primary"
          icon="el-icon-s-promotion"
          class="btn"
        ></el-button>
      </div>
    </div> -->
    <div class="input-area">
      <el-input
        v-model="prompt"
        type="textarea"
        resize="none"
        id="keyInput"
        placeholder="请输入内容"
        @keydown.native="handleKeyDown"
      />
      <div class="send" @click="handleSubmit"></div>
    </div>
  </div>
</template>

<script>
// import { md } from "@/utils/markdown"
// import { chat } from "@/service/api/gpt"
import { fetchChatAPIProcess } from "@/service/api/gpt"

import { Message } from "./components"
import useChat from "./mixin/useChat"
import useScroll from "./mixin/useScroll"

export default {
  components: { Message },
  mixins: [useChat, useScroll],

  props: {},
  data() {
    return {
      decoder: new TextDecoder("utf-8"),
      messageContent: "",
      messageList: [],
      flag: true,
      roleAlias: { user: "ME", assistant: "ChatGPT", system: "System" },

      // dataSources: [],
      loading: false,
      controller: new AbortController(),
      prompt: "",
      // 最多请求3次
      maxRequest: 3,
      errorRequest: 0
    }
  },
  computed: {
    dataSources() {
      if (this.chat.length) {
        return [...this.chat[0].data]
      } else {
        return []
      }
    }
  },
  created() {},
  mounted() {
    this.scrollToBottom()
  },
  watch: {},
  methods: {
    handleKeyDown(event) {
      console.log(event, "event")
      if (event.keyCode === 13) {
        if (!event.ctrlKey) {
          event.preventDefault()
          this.handleSubmit()
        } else {
          console.log("?????")
          this.prompt += "\n"
        }
      }
    },
    handleSubmit() {
      this.onConversation()
    },
    async onConversation() {
      const message = this.prompt

      if (this.loading) return

      if (!message || message.trim() === "") return

      this.controller = new AbortController()

      this.addChat(+this.uuid, {
        dateTime: new Date().toLocaleString(),
        text: message,
        inversion: true,
        error: false,
        conversationOptions: null,
        requestOptions: { prompt: message, options: null }
      })
      this.scrollToBottom()

      this.loading = true
      this.prompt = ""

      const options = { conversationId: window.location.hash }
      // const lastContext = conversationList.value[conversationList.value.length - 1]?.conversationOptions

      // if (lastContext && usingContext.value)
      //   options = { ...lastContext }

      this.addChat(+this.uuid, {
        dateTime: new Date().toLocaleString(),
        text: "",
        loading: true,
        inversion: false,
        error: false,
        conversationOptions: null,
        requestOptions: { prompt: message, options: { ...options } }
      })
      this.scrollToBottom()
      console.log(this.dataSources, "dataSources")
      try {
        // 记录开始时间
        const startTime = Date.now()
        let lastText = ""
        const response = await fetchChatAPIProcess({
          prompt: message,
          signal: this.controller.signal
        })
        const reader = response.body.getReader()
        const decoder = new TextDecoder()
        let extractedJson = null
        let duration = 0
        let thinkingEnd = false
        for (;;) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value, { stream: true })

          // 检查chunk是否包含JSON标记
          const jsonStartIndex = chunk.indexOf("<JSON_START>")
          const jsonEndIndex = chunk.indexOf("<JSON_END>")
          if (chunk.includes("</think>")) {
            const endTime = Date.now()
            duration = (endTime - startTime) / 1000
            thinkingEnd = true
            // return // 不显示</think>标记本身
          }

          if (
            jsonStartIndex !== -1 &&
            jsonEndIndex !== -1 &&
            jsonEndIndex > jsonStartIndex
          ) {
            try {
              const jsonString = chunk.substring(
                jsonStartIndex + "<JSON_START>".length,
                jsonEndIndex
              )
              extractedJson = JSON.parse(jsonString)

              this.updateChat(+this.uuid, this.dataSources.length - 1, {
                dateTime: new Date().toLocaleString(),
                text: lastText + (chunk ?? ""),
                json: extractedJson,
                inversion: false,
                error: false,
                loading: false,
                duration,
                thinkingEnd,
                conversationOptions: {},
                requestOptions: { prompt: message, options: { ...options } }
              })
            } catch (error) {
              console.error("JSON解析失败:", error)
            }
          } else {
            this.updateChat(+this.uuid, this.dataSources.length - 1, {
              dateTime: new Date().toLocaleString(),
              text: lastText + (chunk ?? ""),
              json: extractedJson,
              inversion: false,
              duration,
              thinkingEnd,
              error: false,
              loading: false,
              conversationOptions: {},
              requestOptions: { prompt: message, options: { ...options } }
            })
          }

          lastText += chunk

          this.scrollToBottom()
        }
      } catch (error) {
        console.log(error.status, "error")
        if (error.status === 0) {
          this.errorRequest++
          this.loading = false
          return this.onRegenerate(this.dataSources.length - 1)
        }
        const errorMessage = error?.content ?? "好像出错了，请稍后再试。"

        if (error.text === "canceled") {
          this.updateChatSome(+this.uuid, this.dataSources.length - 1, {
            loading: false
          })
          this.scrollToBottom()
          return
        }

        const currentChat = this.getChatByUuidAndIndex(
          +this.uuid,
          this.dataSources.length - 1
        )

        if (currentChat?.text && currentChat.text !== "") {
          this.updateChatSome(+this.uuid, this.dataSources.length - 1, {
            text: `${currentChat.text}\n[${errorMessage}]`,
            error: false,
            loading: false
          })
          return
        }

        this.updateChat(+this.uuid, this.dataSources.length - 1, {
          dateTime: new Date().toLocaleString(),
          text: errorMessage,
          inversion: false,
          error: true,
          loading: false,
          conversationOptions: null,
          requestOptions: { prompt: message, options: { ...options } }
        })
        this.scrollToBottom()
      } finally {
        this.loading = false
      }
    },
    // 刷新重新
    async onRegenerate(index) {
      console.log(index)
      if (this.loading) return

      this.controller = new AbortController()

      const { requestOptions } = this.dataSources[index]
      console.log(requestOptions, "requestOptions")
      const message = requestOptions?.prompt ?? ""

      let options = {}

      if (requestOptions.options) options = { ...requestOptions.options }

      this.loading = true

      this.updateChat(+this.uuid, index, {
        dateTime: new Date().toLocaleString(),
        text: "",
        inversion: false,
        error: false,
        loading: true,
        conversationOptions: null,
        requestOptions: { prompt: message, ...options }
      })
      try {
        const startTime = Date.now()
        let lastText = ""
        const response = await fetchChatAPIProcess({
          prompt: message,
          signal: this.controller.signal
        })
        const reader = response.body.getReader()
        const decoder = new TextDecoder()
        let extractedJson = null
        let duration = 0
        let thinkingEnd = false

        for (;;) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value, { stream: true })
          console.log(chunk, "chunk/////")

          // 检查chunk是否包含JSON标记
          const jsonStartIndex = chunk.indexOf("<JSON_START>")
          const jsonEndIndex = chunk.indexOf("<JSON_END>")
          if (chunk.includes("</think>")) {
            const endTime = Date.now()
            duration = (endTime - startTime) / 1000
            thinkingEnd = true
            // return // 不显示</think>标记本身
          }

          if (
            jsonStartIndex !== -1 &&
            jsonEndIndex !== -1 &&
            jsonEndIndex > jsonStartIndex
          ) {
            try {
              const jsonString = chunk.substring(
                jsonStartIndex + "<JSON_START>".length,
                jsonEndIndex
              )
              extractedJson = JSON.parse(jsonString)

              this.updateChat(+this.uuid, this.dataSources.length - 1, {
                dateTime: new Date().toLocaleString(),
                text: lastText + (chunk ?? ""),
                json: extractedJson,
                inversion: false,
                error: false,
                loading: false,
                duration,
                thinkingEnd,
                conversationOptions: {},
                requestOptions: { prompt: message, options: { ...options } }
              })
            } catch (error) {
              console.error("JSON解析失败:", error)
            }
          } else {
            this.updateChat(+this.uuid, this.dataSources.length - 1, {
              dateTime: new Date().toLocaleString(),
              text: lastText + (chunk ?? ""),
              json: extractedJson,
              inversion: false,
              duration,
              error: false,
              loading: false,
              thinkingEnd,
              conversationOptions: {},
              requestOptions: { prompt: message, options: { ...options } }
            })
          }

          lastText += chunk

          this.scrollToBottom()
        }
      } catch (error) {
        if (error.status === 0 && this.errorRequest < this.maxRequest) {
          this.errorRequest++
          this.loading = false
          return this.onRegenerate(index)
        }
        const errorMessage = error?.content ?? "好像出错了，请稍后再试。"
        if (error.text === "canceled") {
          this.updateChatSome(+this.uuid, index, {
            loading: false
          })
          return
        }

        this.updateChat(+this.uuid, index, {
          dateTime: new Date().toLocaleString(),
          text: errorMessage,
          inversion: false,
          error: true,
          loading: false,
          conversationOptions: null,
          requestOptions: { prompt: message, ...options }
        })
      } finally {
        this.loading = false
      }
    },
    handleDelete(index) {
      if (this.loading) {
        return
      }
      this.$confirm("是否删除此信息?", "删除信息", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.deleteChatByUuid(+this.uuid, index)
        })
        .catch(() => {})
    },
    handleStop() {
      if (this.loading) {
        this.loading = false
        this.controller.abort()
      }
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: calc(100vh - 20vh - 110px);
  main {
    //滚动条样式
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    ::-webkit-scrollbar-track {
      background-color: transparent;
    }
    ::-webkit-scrollbar-thumb {
      background-color: #fff;
      border-radius: 4px;
    }
  }

  .btn {
    margin-left: 16px;
    width: 80px;
  }
}

.input-area {
  position: relative;
  width: 700px;
  height: 94px;
  margin: 0 auto;
  border-radius: 6px;
  background-image: linear-gradient(#e6ebff, #e6ebff),
    // 跟背景色保持一致，根据实际情况修改
    linear-gradient(
        180deg,
        rgba(67, 192, 255, 1),
        rgba(120, 140, 255, 1)
      ); // 取border-image的渐变色，按实际来修改
  background-origin: border-box;
  background-clip: content-box, border-box;
  padding: 2px;
  border-radius: 14px;

  ::v-deep .el-textarea__inner {
    height: 90px;
    background-color: transparent;
    border: none;
  }

  .send {
    position: absolute;
    cursor: pointer;
    right: 28px;
    bottom: 14px;
    width: 28px;
    height: 28px;
    background: url("../../assets/images//ai//send.png") no-repeat;
  }
}
</style>
