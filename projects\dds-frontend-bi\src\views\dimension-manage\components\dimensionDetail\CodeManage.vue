<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    title="维度值代码管理"
    width="480px"
    custom-class="custom-dialog"
    @close="handleClose"
  >
    <el-form ref="form" :model="formData" :rules="rules">
      <el-form-item label="维度层级">
        <el-select
          v-model="formData.currentLevel"
          placeholder="请选择维度层级"
          style="width: 240px"
          @change="getDimValue"
          value-key="levelCode"
        >
          <el-option
            :label="item.levelName"
            :value="item"
            v-for="item in dimensionLevels"
            :key="item.levelCode"
          />
        </el-select>
      </el-form-item>

      <div class="action-buttons">
        <div class="btn" @click="handleMatchType('1')">选择字典表匹配代码</div>

        <div class="btn" @click="handleMatchType('2')">上传文件匹配代码</div>
      </div>
      <el-table
        :data="tableData"
        stripe
        style="width: 100%"
        border
        height="300px"
      >
        <el-table-column prop="value" label="维度值"></el-table-column>
        <el-table-column prop="code" label="代码">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.code"
              style="width: 80%"
              placeholder="请输入代码"
            ></el-input>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <DT-Pagination
        :hidden="pagination.total == 0"
        :total="pagination.total"
        :page-size="pagination.pageSize"
        :current-page="pagination.currentPage"
        @sizeChange="handlePageSizeChange"
        @currentChange="handlePageCurrentChange"
      />
    </el-form>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </template>

    <el-dialog
      :visible.sync="dialogVisible1"
      :close-on-click-modal="false"
      title="字典表匹配维度值代码"
      width="400px"
      append-to-body
      custom-class="custom-dialog"
    >
      <el-form ref="form" :model="formData" :rules="rules">
        <el-form-item label="选择匹配表" v-if="matchType == '1'">
          <TableSingleSelect 
           v-model="dictCode"
          />
          <!-- <el-select
            filterable
            v-model="dictCode"
            placeholder="请选择匹配表"
            v-if="dataDomainList.length > 0"
            style="width: 240px"
          >
            <el-option
              :label="item.c"
              :value="item.id"
              v-for="item in dataDomainList"
              :key="item.id"
            >
              <span style="float: left">{{ item ? item.c : "" }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">
                {{ item ? item.id : "" }}
              </span>
            </el-option>
          </el-select> -->
        </el-form-item>
        <el-form-item label="选择文件" v-if="matchType == '2'">
          <div style="display: flex">
            <template v-if="filePath">
              {{ excelName }}
              <div class="download-templates" @click="closeImport">取消</div>
            </template>
            <template v-else>
              <el-upload
                action="/file-api/upms-service/upload"
                multiple
                :show-file-list="false"
                style="display: contents"
                accept=".xls,.XLS,.xlsx,.XLSX"
                :headers="$utils.auth.getAdminheader()"
                :on-success="handleSuccess"
              >
                <!-- 点击下载导入模板 -->
                <el-button type="primary" icon="el-icon-upload2">
                  上传文件(xlsx)
                </el-button>
              </el-upload>
              <div class="download-templates" @click="downloadTemplates">
                下载模板
              </div>
            </template>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible1 = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleNext"
            :disabled="!filePath && matchType === '2'"
          >
            下一步
          </el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      :visible.sync="dialogVisible2"
      :close-on-click-modal="false"
      title="字典表匹配维度值代码"
      width="400px"
      append-to-body
      custom-class="custom-dialog"
      @close="handleClose"
    >
      <p class="col1">
        成功完成
        <span class="success-num">{{ successCount }}</span>
        项维度值匹配，示例：
      </p>
      <div class="examples" v-if="successList.length">
        <div
          class="example"
          v-for="item in successList.slice(0, 3)"
          :key="item"
        >
          <div class="dim-label">{{ item.value }}</div>
          <div class="dim-arrrow"></div>
          <div class="dim-value">{{ item.code }}</div>
        </div>
      </div>

      <div class="error-dim" v-if="errorCount > 0">
        {{ errorList[0].value }}等
        <span class="error-count">{{ errorCount }}</span>
        个维度值匹配代码失败
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible2 = false">取消</el-button>
          <el-button
            type="primary"
            @click="useMatchResult"
            :disabled="!successCount"
          >
            使用匹配结果
          </el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script>
import axios from "axios"
import Request from "@/service"
import TableSingleSelect from './TableSingleSelect.vue'
export default {
  components: {
    TableSingleSelect
  },
  props: {
    currentVersionItem: {
      type: Array,
      default: () => {}
    },
    dimensionLevels: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      excelName: "",
      filePath: "",
      matchType: "1", // 匹配类型 1 字典表匹配, 2 文件匹配
      dictCode: "",
      dialogVisible: false,
      dialogVisible1: false,
      dialogVisible2: false,
      dataDomainList: [],
      key: "", // Add key to data
      tableData: [],
      formData: {
        currentLevel: {}
      },
      successCount: 0,
      errorCount: 0,
      successList: [],
      errorList: [],
      pagination: {
        total: 0,
        pageSize: 10,
        currentPage: 1
      },
      page1: {
        total: 0,
        pageSize: 10,
        currentPage: 1
      },
      loading:false
    }
  },
  created() {
    // this.getTableList()
  },
  methods: {
    handleMatchType(type) {
      // 请选择维度层级
      if (
        !this.formData.currentLevel ||
        !this.formData.currentLevel.levelCode
      ) {
        this.$message.warning("请先选择维度层级")
        return
      }
      this.matchType = type
      this.dialogVisible1 = true
    },
    openDialog() {
      this.formData = {}
      this.dialogVisible = true
    },
    handleClose() {
      this.$refs.form.resetFields()
    },
    handleSubmit() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          await Request.api.paramPost("/dimValueManage/saveMatched",{
            dimCode: this.currentVersionItem.definitionCode,
            level: this.formData.currentLevel.levelCode,
            dimValueMatchItems: this.tableData
          })
          this.$message.success("保存成功")
          this.dialogVisible = false
        }
      })
    },
    handleNodeClick(data) {
      console.log("节点点击:", data)
    },
    useMatchResult() {
      this.dialogVisible2 = false
      this.$message.success("使用匹配结果成功")
      this.tableData = this.successList
    },
    async handleNext() {
      if (this.matchType === "2") {
        const { data } = await Request.api.paramGet(
          "/dimValueManage/fileMatching",
          {
            filePath: this.filePath,
            level: this.formData.currentLevel.levelCode,
            dimCode: this.currentVersionItem.definitionCode
          }
        )
        this.successList = data.successList || []
        this.successCount = data.successCount
        this.errorCount = data.errorCount
        this.errorList = data.errorList || []
      } else {
        const { data } = await Request.api.paramGet(
          "/dimValueManage/dictMatching",
          {
            dictCode: this.dictCode,
            level: this.formData.currentLevel.levelCode,
            dimCode: this.currentVersionItem.definitionCode
          }
        )
        this.successList = data.successList || []
        this.successCount = data.successCount
        this.errorCount = data.errorCount
        this.errorList = data.errorList || []

        console.log(data)
      }

      this.dialogVisible1 = false

      this.dialogVisible2 = true
    },
    // 获取维度值匹配信息
    async getDimValueList() {
      const { data } = await Request.api.paramGet(
        "/dimValueManage/dimValueMatched",
        {
          level: this.formData.currentLevel.levelCode,
          dimCode: this.currentVersionItem.definitionCode
        }
      )
      this.tableData = data
    },
    // 获取维度层级维度值
    async getDimValue(item) {
      const { data } = await Request.api.paramGet(
        "/dimValueManage/dimValueMatched",
        {
          level: item.levelCode,
          dimCode: this.currentVersionItem.definitionCode
        }
      )
      this.tableData = data
    },

    // 上传成功回调
    handleSuccess(response, file) {
      this.filePath = response.data.path
      this.excelName = file.name
    },
    // 关闭导入
    closeImport() {
      this.filePath = null
      this.excelName = null
    },
    // 下载模板
    downloadTemplates() {
      axios({
        method: "get",
        url: "/file-api/upms-service/download/template/dim_file.xls",
        headers: { ...this.$utils.auth.getAdminheader() },
        responseType: "blob"
      }).then(
        _res => {
          const blob = new Blob([_res.data], {
            type: "application/vnd.ms-excel;"
          })
          const a = document.createElement("a")
          // 生成文件路径
          let href = window.URL.createObjectURL(blob)
          a.href = href
          // 文件名中有中文 则对文件名进行转码
          a.download = decodeURIComponent("导入模板.xls")
          // 利用a标签做下载
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
          window.URL.revokeObjectURL(href)
        },
        () => {}
      )
    },

        getSearchTable () {
      this.page.currentPage = 1
      this.dataDomainList = []
      this.getTableList()
    },
    handleScroll () {
      if (!this.loading && this.dataDomainList.length < this.page1.total) {
        this.page1.currentPage++
        this.getTableList()
      }
    },
    // 获取数据表列表
    async getTableList() {
      try {
        const { data } = await this.$httpBi.indicatorAnagement.getTableList({
          key: this.key,
               currentPage: this.page1.currentPage,
          pageSize: this.page1.pageSize
        })
        this.dataDomainList.push(...data.list)
        this.page1.total = data.totalCount


      } catch (error) {
        console.error("Failed to fetch table list:", error)
        this.dataDomainList = []
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.download-templates {
  margin-left: 16px;
  color: #409eff;
  cursor: pointer;
  text-decoration: underline;
}
.col1 {
  height: 14px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #222222;
  line-height: 14px;
  text-align: left;
  font-style: normal;
  margin-bottom: 14px;

  .success-num {
    height: 18px;
    font-family: AlibabaSans102Ver2, AlibabaSans102Ver2;
    font-weight: 500;
    font-size: 18px;
    color: #00cc87;
    line-height: 18px;
    text-align: left;
    font-style: normal;
  }
}
.example {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  .dim-label {
    height: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #222222;
    line-height: 14px;
    text-align: left;
    font-style: normal;
  }
  .dim-arrrow {
    width: 12px;
    height: 12px;
    margin: 0 12px;
    background: url("~@/assets/images/dim-arrow.png") no-repeat center;
    background-size: 100%;
  }
  .dim-value {
    height: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #222222;
    line-height: 14px;
    text-align: left;
    font-style: normal;
  }
}
.error-dim {
  margin-top: 10px;
  height: 14px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 14px;
  text-align: left;
  font-style: normal;
  .error-count {
    height: 18px;
    font-family: AlibabaSans102Ver2, AlibabaSans102Ver2;
    font-weight: 500;
    font-size: 18px;
    color: #ff5256;
    line-height: 18px;
    text-align: left;
    font-style: normal;
  }
}
::v-deep .el-dialog__header {
  margin: 0;
  padding: 20px 24px;
  padding-bottom: 10px;
  border-bottom: 1px solid #dcdee0;
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #222222;
}

::v-deep .el-dialog__body {
  padding: 20px 24px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}

::v-deep .el-row {
  margin-bottom: 0;
}

.action-buttons {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  .btn {
    padding: 0 8px;
    height: 32px;
    border-radius: 4px;
    border: 1px solid #1563ff;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #1563ff;
    line-height: 32px;
    text-align: center;
    font-style: normal;
    cursor: pointer;

    &:hover {
      background: #f4f7ff;
    }

    margin-right: 8px;
  }
}

::v-deep .el-form--label-top .el-form-item__label {
  padding: 0 0 8px 0;
  line-height: 14px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #222222;
  line-height: 14px;
  text-align: left;
  font-style: normal;
}

::v-deep .el-form-item__error {
  font-size: 10px;
}

.custom-dialog {
  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #eee;

    .title {
      font-size: 16px;
      font-weight: 600;
    }

    .close-btn {
      cursor: pointer;
      color: #999;

      &:hover {
        color: #666;
      }
    }
  }

  ::v-deep .el-dialog__body {
    padding: 20px;
  }

  .el-form-item {
    margin-bottom: 12px;
  }

  .el-select,
  .el-input {
    width: 100%;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    padding: 0px 20px 0;

    .el-button {
      margin-left: 10px;
    }
  }
}
</style>
