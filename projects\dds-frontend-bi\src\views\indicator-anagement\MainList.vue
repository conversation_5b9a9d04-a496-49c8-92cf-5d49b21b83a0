<template>
  <DT-View :inner-style="{ padding: '20px' }">
    <template v-if="!currentType">
      <SearchForm
        @search="initData"
        :columns="columns"
        :search-param.sync="form"
        :is-card="false"
        style="margin-bottom: 2px"
      />
      <div class="content">
        <div class="left-tree">
          <div class="left-head">
            <div class="left-title">指标域</div>
            <div class="left-btn" @click="handleDataDomainVisible">
              新建指标域
            </div>
          </div>
          <el-tree
            class="model-tree"
            style="padding: 16px 6px"
            :data="viewGroup[0].children"
            v-loading="treeLoading"
            :props="{
              children: 'children',
              label: 'name'
            }"
            node-key="id"
            :expand-on-click-node="false"
            ref="tree"
          >
            <!-- 原有内容保持不变 -->
            <span
              class="custom-tree-node droppable"
              @dragover.prevent
              @drop="handleIndicatorDropToTree(data)"
              slot-scope="{ node, data }"
              @click="treeNodeClick(data)"
            >
              <div class="node-item">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="node.label"
                  placement="top"
                >
                  <div class="node-label">
                    {{ node.label }}
                  </div>
                </el-tooltip>
                <div class="node-num">({{ data.zbsl }})</div>
              </div>
              <span class="custom-tree-btns">
                <el-dropdown @command="handleCommand($event, data)">
                  <span class="el-dropdown-link">
                    <i
                      class="el-icon-more"
                      style="transform: rotate(90deg)"
                    ></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="add">新增</el-dropdown-item>
                    <el-dropdown-item command="edit">编辑</el-dropdown-item>
                    <el-dropdown-item command="del">删除</el-dropdown-item>
                    <el-dropdown-item command="auth">权限</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </span>
            </span>
          </el-tree>
        </div>
        <div class="right-content">
          <el-row>
            <el-col :span="12" style="display: flex; align-items: center">
              <template v-if="isBatchEdit">
                <el-button icon="" @click="handleMakeTag"> 打标签 </el-button>
                <el-button icon="" @click="handleBatchEnable">
                  启用指标
                </el-button>
                <el-button icon="" @click="handleBatchDisable">
                  禁用指标
                </el-button>
              </template>
              <template v-else>
                <el-button
                  icon="el-icon-plus"
                  type="primary"
                  @click="addDialogVisible = true"
                >
                  新建指标
                </el-button>

                <el-button
                  @click="goToAnalysis"
                  v-if="$checkPermission(['index:analysis'])"
                >
                  多维数据分析
                </el-button>
                <el-button @click="isBatchEdit = true"> 批量管理 </el-button>
              </template>
            </el-col>
            <el-col :span="12" style="text-align: right">
              <el-button
                @click="goPage('/ddsMain/system/warningManage')"
                v-if="$checkPermission(['ind:warning'])"
              >
                指标预警管理
              </el-button>
              <el-button v-if="isBatchEdit" @click="isBatchEdit = false">
                退出批量编辑状态
              </el-button>
            </el-col>
          </el-row>
          <el-table
            :data="tableData"
            v-loading="loading"
            ref="table"
            height="calc(100vh - 350px)"
            :row-key="row => row.indCode"
            @sort-change="sortChange"
          >
            <el-table-column
              v-if="isBatchEdit"
              type="selection"
              width="55"
              key="selection"
              reserve-selection
            >
            </el-table-column>
            <!-- 拖拽图标列 -->
            <el-table-column
              v-if="isBatchEdit"
              label="拖拽"
              width="60"
              align="center"
              key="drag"
            >
              <template #default="{ row }">
                <div
                  class="drag-handle"
                  draggable="true"
                  @dragstart="handleTableDragStart($event, row)"
                  @dragend="handleTableDragEnd"
                  title="拖拽到左侧指标域"
                >
                  <i
                    class="el-icon-rank"
                    style="cursor: move; color: #409eff"
                  ></i>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="zbmc"
              label="指标名称"
              min-width="150"
              key="zbmc"
            ></el-table-column>
            <el-table-column
              label="指标类型"
              width="100"
              prop="zblx"
              key="zblx"
            ></el-table-column>
            <el-table-column
              prop="cjr"
              label="创建人"
              sortable="customer"
              width="100"
              key="cjr"
            ></el-table-column>
            <el-table-column
              prop="updateTime"
              label="编辑日期"
              sortable="customer"
              width="170"
              key="updateTime"
            ></el-table-column>
            <el-table-column prop="bq" label="标签" width="220" key="bq">
              <template #default="{ row }">
                <DynamicTagList v-if="row.bq" :tags="row.bq.split(',')" />
              </template>
            </el-table-column>
            <el-table-column
              prop="enabled_flag"
              label="状态"
              width="220"
              key="enabled_flag"
            >
              <template #default="{ row }">
                {{ Number(row.enabled_flag) ? '启用' : '禁用' }}
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              width="250px"
              fixed="right"
              key="action"
              v-if="!isBatchEdit"
            >
              <template #default="{ row }">
                <template v-if="row.lxbm !== 'yz'">
                  <el-button type="text" @click="calculation(row)">
                    试计算
                  </el-button>
                  <el-divider direction="vertical"></el-divider>
                </template>

                <el-button type="text" @click="editIndicator(row)">
                  编辑
                </el-button>
                <el-divider direction="vertical"></el-divider>
                <el-button
                  type="text"
                  @click="handleDisable(row)"
                  v-if="Number(row.enabled_flag) === 1"
                >
                  禁用
                </el-button>
                <el-button type="text" @click="handleEnable(row)" v-else>
                  启用
                </el-button>
                <el-divider direction="vertical"></el-divider>
                <el-button type="text" @click="delIndicator(row)">
                  删除
                </el-button>
                <el-divider direction="vertical"></el-divider>
                <el-button type="text" @click="goDetail(row)">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <DT-Pagination
            :hidden="page.total === 0"
            :total="page.total"
            :page-size="page.pageSize"
            :current-page="page.currentPage"
            @sizeChange="handleSizeChange"
            @currentChange="handleCurrentChange"
          />
        </div>
      </div>

      <el-dialog
        title="新建指标"
        :visible.sync="addDialogVisible"
        :before-close="handleClose"
        width="800px"
      >
        <div class="card-list">
          <template v-for="item in addTypeList">
            <div
              class="card-item"
              v-if="item.isShow"
              :key="item.id"
              @click="clickCard(item.id)"
            >
              <div class="name">
                {{ item.name }}
              </div>
              <div class="desc">
                {{ item.desc }}
              </div>
              <div class="card-item-children" v-if="item.children">
                <div
                  class="card-item-children-item"
                  v-for="(child, index) in item.children"
                  :key="index"
                  @click.stop="clickCard(child.id)"
                >
                  <div class="card-item-children-item-name">
                    {{ child.name }}
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </el-dialog>
    </template>
    <!-- 新增数据域 -->
    <AddDataDomain
      v-if="addDataDomainVisible"
      :title="title"
      :add-data-domain-visible.sync="addDataDomainVisible"
      :view-group="viewGroup"
      :form="dataDomainForm"
      @refresh="initData"
    />
    <!-- 指标分析 -->
    <!-- <IndicatorAnalysis v-if="currentType == 'analysis'" @closeAdd="closeAdd" /> -->
    <!-- 编辑指标 -->
    <EditAtomIndicator
      ref="EditAtomIndicator"
      :yzzb-list="yzzbList"
      :form="editForm"
      :view-group="viewGroup"
      @refresh="initData"
    />
    <EidtDeriveIndicator
      ref="EidtDeriveIndicator"
      :yzzb-list="yzzbList"
      :form="editForm"
      :view-group="viewGroup"
      @refresh="initData"
    />
    <DeriveIndicatorCalculate ref="DeriveIndicatorCalculate" />
    <CompositeIndicatorCalculate ref="CompositeIndicatorCalculate" />
    <SqlIndicatorCalculate ref="SqlIndicatorCalculate" />
    <AlgorithmIndicator ref="AlgorithmIndicator" />
    <el-dialog
      title="常模配置"
      :visible.sync="normalVisible"
      width="678px"
      :before-close="handleClose"
    >
      <el-form ref="form" :model="normalForm" label-width="120px">
        <el-form-item label="指标名称:">
          {{ normalForm.name }}
        </el-form-item>
        <el-form-item label="选择常模类型:">
          <el-select
            v-model="normalForm.types"
            placeholder="请选择"
            style="width: 100%"
            multiple
            @change="handleNomalType"
            value-key="id"
          >
            <el-option
              v-for="item in normalOPtions"
              :key="item.id"
              :label="item.name"
              :value="item"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="配置常模值:">
          <el-table
            :data="normalForm.zbNormativeTypes"
            style="width: 100%; margin-top: 0px"
            :max-height="300"
            border
          >
            <el-table-column
              prop="zbnormativetypename"
              label="常模类型"
            ></el-table-column>
            <el-table-column prop="zbnormativevalue" label="常模值">
              <template #default="{ row }">
                <el-input
                  style="width: 200px; padding-right: 20px"
                  v-model="row.zbnormativevalue"
                  placeholder="请输入常模值"
                ></el-input>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="normalVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSaveNomal">确 定</el-button>
      </span>
    </el-dialog>
    <DT-Transfer
      type="dialog"
      :visible.sync="visible"
      title="授权范围"
      v-model="defaultVal"
      single-check
      :data="data"
      @getChildren="transferChild"
    />
    <AffectScopeDialog ref="AffectScopeDialog" />

    <MakeTag
      ref="MakeTag"
      v-model="tags"
      :dialog-visible.sync="dialogVisible"
      @handleSaveTags="handleSaveTags"
    >
      <template #header>
        <div style="margin-bottom: 10px">
          为{{
            selectedRows
              .slice(0, 2)
              .map(item => item.zbmc)
              .join('；')
          }}
          <span v-if="selectedRows.length > 2"
            >等一共{{ selectedRows.length }}个</span
          >
          指标增加标签
        </div>
      </template>
    </MakeTag>
  </DT-View>
</template>

<script>
import SearchForm from '@/components/SearchForm/index.vue'
import options from './mixins/options'
// 添加指标域
import AddDataDomain from './components/AddDataDomain'
// 原子指标编辑
import EditAtomIndicator from './atom-indicator/Edit.vue'
// 编辑派生指标
import EidtDeriveIndicator from './derive-indicator/EditIndicator.vue'
// 派生指标试计算
import DeriveIndicatorCalculate from './derive-indicator/Calculate.vue'
// 复合指标试计算
import CompositeIndicatorCalculate from './composite-indicator/Calculate.vue'
// SQL指标试计算
import SqlIndicatorCalculate from './sql-indicator/Calculate.vue'
// 算法指标试计算
import AlgorithmIndicator from './algorithm/Calculate.vue'
// 变更影响弹窗
import AffectScopeDialog from './components/AffectScopeDialog.vue'
// 标签组件
import DynamicTagList from './components/DynamicTagList.vue'
//  打标签
import MakeTag from './components/MakeTag.vue'

export default {
  components: {
    SearchForm,
    AddDataDomain,
    EditAtomIndicator,
    EidtDeriveIndicator,
    DeriveIndicatorCalculate,
    CompositeIndicatorCalculate,
    SqlIndicatorCalculate,
    AlgorithmIndicator,
    AffectScopeDialog,
    DynamicTagList,
    MakeTag
  },
  mixins: [options],
  props: {},
  data () {
    return {
      tags: [],
      treeLoading: false,
      loading: false,
      page: {
        total: 0,
        pageSize: 10,
        currentPage: 1
      },
      dialogVisible: false, // 试计算弹框
      expandRowKeys: [],
      yzzbList: [], // 原子指标
      title: '新增数据域',
      editVisible: false,
      defaultExpandAll: false,
      editForm: {},
      dataDomainForm: {
        name: '',
        description: '',
        index: 0, // 排序
        parentId: null, // 父级主题id
        publish: 1 // 这里固定是 1
      },
      form: {
        zbmc: '',
        lxbm: '',
        sortKey: '', // 排序字段
        sortType: '', // 排序类型,
        currentId: '0' // 当前数据域id
      },
      currentType: null,
      pageShow: true,
      addDialogVisible: false,
      addDataDomainVisible: false, // 新建数据域

      columns: [
        {
          label: '指标名称',
          prop: 'zbmc',
          search: {
            el: 'input',
            props: {
              placeholder: '请输入指标名称、标签名称'
            }
          }
        },
        {
          label: '指标类型',
          prop: 'lxbm',
          type: 'select',
          search: {
            el: 'select'
          },
          enum: this.getIndicatorType
        }
      ],
      addTypeList: [
        {
          name: '创建原子指标',
          id: 'atomIndicator',
          desc: '适合基于数据表创建大量基础指标',
          isShow: true
        },
        {
          name: '创建派生指标',
          id: 'deriveIndicator',
          desc: '在原子指标的基础上，约定特定维度；如某学院、某学科维度的指标',
          isShow: true
        },
        {
          name: '创建衍生指标',
          id: 'compositeIndicator',
          desc: '创建需要多个指标关联计算才能得出的进阶指标；如生师比',
          isShow: true
        },
        {
          name: '创建算法指标',
          id: 'algorithmIndex',
          desc: '通过算法创建挖掘指标',
          isShow: true
        },
        {
          name: '创建SQL指标',
          id: 'sqlIndicator',
          desc: '通过SQL语句创建自定义指标',
          isShow: true
          // children: [
          //   {
          //     name: "SQL视图",
          //     id: "sqlIndicator"
          //   },
          //   {
          //     name: "表视图",
          //     id: "sqlTableview"
          //   }
          // ]
        }
        // {
        //   name: "创建SQL指标",
        //   id: "sql",
        //   desc: "通过SQL语句创建自定义指标"
        // }
      ],
      tableData: [], // 指标列表
      // 常模
      normalVisible: false,
      normalForm: {
        types: [],
        name: '',
        zbid: '',
        zblxm: '',
        zbNormativeTypes: []
      },
      normalOPtions: [],
      dwList: [],
      labels: [],
      visible: false,
      defaultVal: [],
      data: [],
      draggingNode: null,
      // 批量编辑状态
      isBatchEdit: false,
      // 拖拽相关
      draggedIndicator: null,
      draggedIndicators: [], // 批量拖拽的指标
      isDragging: false,
      selectedRows: [] // 选中的行
    }
  },
  computed: {
    username () {
      return this.$store.state.user.username
    }
  },
  created () {},
  mounted () {
    if (this.$route.query.code) {
      let { code } = this.$route.query
      this.$nextTick(() => {
        this.expandRowKeys = code.split(',')
        console.log(this.expandRowKeys, 'this.expandRowKeys')
      })
    }
    this.initData()
    this.getYzList()
    this.getAllNormativeType()
    this.initTransferData()
  },
  watch: {
    // isBatchEdit: {
    //   handler () {
    //     this.$nextTick(() => {
    //       console.log('111111111')
    //       this.$refs.table.doLayout()
    //     })
    //   }
    // }
  },
  methods: {
    // 判断是否有选中行
    isSelected () {
      this.selectedRows = this.$refs.table.selection
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要操作的指标')
        return false
      }
      return this.selectedRows.length > 0
    },
    // 启用
    async handleBatchEnable () {
      if (!this.isSelected()) {
        return
      }
      const { code } = await this.$httpBi.indicatorAnagement.batchEnable(
        this.selectedRows.map(item => ({
          indicatorCode: item.indCode,
          indicatorType: item.lxbm
        }))
      )
      if (code === 200) {
        this.$message.success('启用成功')
        this.initData()
      }

      console.log('启用')
    },
    // 批量禁用
    async handleBatchDisable () {
      if (!this.isSelected()) {
        return
      }
      const { code } = await this.$httpBi.indicatorAnagement.batchDisable(
        this.selectedRows.map(item => ({
          indicatorCode: item.indCode,
          indicatorType: item.lxbm
        }))
      )
      if (code === 200) {
        this.$message.success('禁用成功')
        this.initData()
      }
      console.log('禁用')
    },
    handleEnable (row) {
      this.$confirm(
        '指标启用后，才能在指标对比分析、ai问数、图表模型管理中检索到该指标，是否继续？',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        const { code } = await this.$httpBi.indicatorAnagement.enable({
          indicatorCode: row.indCode,
          indicatorType: row.lxbm
        })
        if (code === 200) {
          this.$message.success('启用成功')
          this.initData()
        }
      })
    },
    // 禁用
    handleDisable (row) {
      this.$confirm(
        '指标禁用后，无法在指标对比分析、ai问数、图表模型管理中检索到该指标，是否继续？',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        const { code } = await this.$httpBi.indicatorAnagement.disable({
          indicatorCode: row.indCode,
          indicatorType: row.lxbm
        })
        if (code === 200) {
          this.$message.success('禁用成功')
          this.initData()
        }
      })
    },
    // 打标签
    handleMakeTag () {
      if (!this.isSelected()) {
        return
      }
      this.$refs.MakeTag.dialogVisible = true
      this.$refs.MakeTag.dialogTitle = '批量打标签'
    },
    async handleSaveTags (tags) {
      console.log(tags, 'tags')

      const { code } = await this.$httpBi.indicatorAnagement.batchMakeTag(
        {
          newTag: tags.join(',')
        },
        this.selectedRows.map(item => ({
          indicatorCode: item.indCode,
          indicatorType: item.lxbm
        }))
      )
      if (code === 200) {
        this.$message.success('标签添加成功')
        this.$refs.MakeTag.dialogVisible = false
        this.$refs.MakeTag.dialogTitle = '批量打标签'
        this.initData()
      }
    },
    async getBaseUnit () {
      const { data } = await this.$httpBi.indicatorAnagement.getBaseUnit()
      this.dwList = data
      data.push({
        bm: '其他',
        name: '其他'
      })
    },
    // 获取所有标签
    async getLabelSelectList () {
      const { data } = await this.$httpBi.indicatorAnagement.getIndicatorTags(
        ''
      )
      this.labels = data
    },
    goToAnalysis () {
      this.$router.push('/ddsBi/indicatorAnalysis')
    },
    handleExpandChange (row, expanded) {
      console.log(row, expanded, 'expanded')
      if (expanded) {
        if (row.parentId === 0) {
          this.expandRowKeys = ['0']
        }
        this.expandRowKeys.push(String(row.id))
      }
      console.log(this.expandRowKeys, 'this.expandRowKeys')
    },
    // 保存常模
    async handleSaveNomal () {
      const { code, data } =
        await this.$httpBi.indicatorAnagement.updateNormativeTypeByZb({
          zbid: this.normalForm.zbid,
          zblxm: this.normalForm.zblxm,
          zbNormativeTypes: this.normalForm.zbNormativeTypes
        })
      if (code === 200) {
        this.normalVisible = false
        this.$message.success(data)
      }
    },
    // 选择常模
    handleNomalType (val) {
      console.log(val, 'val')
      let tempList = [...this.normalForm.zbNormativeTypes]
      console.log(tempList, ' let tempList')
      this.normalForm.zbNormativeTypes = val.map(item => {
        return {
          ...item,
          zbnormativetypeid: item.id,
          zbnormativetypename: item.name,
          zbnormativevalue:
            tempList.find(e => Number(e.zbnormativetypeid) === Number(item.id))
              ?.zbnormativevalue || ''
        }
      })
      console.log(this.normalForm.zbNormativeTypes)
    },
    // 获取常模下拉选择
    async getAllNormativeType () {
      const { data } =
        await this.$httpBi.indicatorAnagement.getAllNormativeType()
      this.normalOPtions = data
    },

    // 常模配置
    async normConfiguration (row) {
      const { data } =
        await this.$httpBi.indicatorAnagement.getNormativeTypeByZb({
          zbid: row.id,
          zblxm: row.lxbm
        })
      this.normalVisible = true
      this.normalForm.name = row.zbmc
      this.normalForm.zbid = row.id
      this.normalForm.zblxm = row.lxbm
      this.normalForm.zbNormativeTypes = data
      this.normalForm.types = data.map(item => ({
        ...item,
        id: Number(item.zbnormativetypeid),
        name: item.zbnormativetypename
      }))
    },
    // 跳转常模指标
    goNormIndicator () {
      const routeUrl = this.$router.resolve({
        path: `/ddsBi/normIndicator`
      })
      window.open(routeUrl.href, '_blank')
    },
    goDetail (row) {
      const routeUrl = this.$router.resolve({
        path: `/ddsBi/appDetail`,
        query: {
          indCode: row.indCode,
          lxbm: row.lxbm
        }
      })
      window.open(routeUrl.href, '_blank')
    },
    clickCard (id) {
      if (Number(this.form.currentId) === 0) {
        this.$router.push(id)
      } else {
        this.$router.push(id + '?sysjy=' + this.form.currentId)
      }
      // if (id === "sql") {
      //   this.$router.push("/ddsBi/EditView?isFullPage=true&group=0")
      // } else {
      //   this.currentType = id
      // }
    },
    // 节点操作
    handleCommand (command, data) {
      console.log(data, 'data')
      if (command === 'add') {
        // 初始化数据
        this.dataDomainForm = {
          name: '',
          description: '',
          index: 0, // 排序
          parentId: data.id, // 父级主题id
          publish: 1 // 这里固定是 1
        }
        this.title = '新增数据域'
        this.addDataDomainVisible = true
        this.addDataDomainVisible = true
      } else if (command === 'edit') {
        this.editDataDomain(data)
      } else if (command === 'del') {
        this.deleteDataDomain(data)
      } else if (command === 'auth') {
        console.log('///')
        this.visible = true
      }
    },
    // tree点击
    treeNodeClick (data) {
      this.page.currentPage = 1
      this.form.currentId = data.id
      this.getAllIndicatorList()
    },

    handleCurrentChange (val) {
      this.page.currentPage = val.currentPage
      this.getAllIndicatorList()
    },
    handleSizeChange (val) {
      this.page.currentPage = 1
      this.page.pageSize = val.pageSize
      this.getAllIndicatorList()
    },
    sortChange ({ prop, order }) {
      this.page.currentPage = 1
      this.form.sortKey = prop
      this.form.sortType = order === 'ascending' ? 'asc' : 'desc'
      this.getAllIndicatorList()
    },
    initData () {
      this.page.currentPage = 1
      this.$nextTick(() => {
        this.$refs.table.clearSort()
      })
      this.getAllIndicatorList()
      this.getAllViewGroup()
    },
    // 获取所有数据域分组
    async getAllViewGroup () {
      this.treeLoading = true
      const { data } = await this.$httpBi.indicatorAnagement.getAllViewGroup({
        zbmc: this.form.zbmc,
        lxbm: this.form.lxbm,
        sysjyid: ''
      })
      this.viewGroup[0].children = data
      this.treeLoading = false
    },
    // 获取指标列表
    async getAllIndicatorList () {
      this.loading = true
      this.tableData = []
      const { data } =
        await this.$httpBi.indicatorAnagement.getAllIndicatorList({
          zbmc: this.form.zbmc,
          lxbm: this.form.lxbm,
          sysjyid: this.form.currentId, // 数据域id
          pxmc: this.form.sortKey, // 排序字段名称
          px: this.form.sortType, // 排序方式
          pageSize: this.page.pageSize,
          currentPage: this.page.currentPage
        })
      this.tableData = data.list || []
      this.page.total = data.totalCount || 0
      this.loading = false
      if (this.form.zbmc) {
        this.defaultExpandAll = true
      } else {
        this.defaultExpandAll = false
      }
      console.log(this.tableData, 'this.tableData')
    },
    // 编辑指标
    async editIndicator (row) {
      console.log(row, 'row')
      let Api = null
      if (row.lxbm === 'yz') {
        Api = this.$httpBi.indicatorAnagement.getAtomIndicatorInfo
        const { data } = await Api({
          indCode: row.indCode
        })
        this.editForm = {
          ...data,
          diydw: '',
          sysjy: data.sysjy + ''
        }
        this.$refs.EditAtomIndicator.open(this.editForm)

        return
      }
      if (row.lxbm === 'ps') {
        Api = this.$httpBi.indicatorAnagement.getDeriveIndicatorInfo
        const { data } = await Api({
          id: row.id,
          indCode: row.indCode
        })
        this.editForm = {
          ...data,
          diydw: '',
          initWarn: data.isWarnThreshold,
          sysjy: data.sysjy + ''
        }

        this.$refs.EidtDeriveIndicator.open()
        return
      }
      if (row.lxbm === 'ys') {
        return this.$router.push({
          path: '/ddsBi/compositeIndicator',
          query: {
            indCode: row.indCode
          }
        })
      }
      if (row.lxbm === 'sq') {
        return this.$router.push({
          path: '/ddsBi/sqlIndicator',
          query: {
            indCode: row.indCode
          }
        })
      }
      if (row.lxbm === 'sf') {
        return this.$router.push({
          path: '/ddsBi/algorithmIndex',
          query: {
            indCode: row.indCode,
            id: row.id,
            mode: 'edit'
          }
        })
      }
    },
    // 编辑数据域
    editDataDomain (row) {
      this.dataDomainForm = {
        ...this.dataDomainForm,
        ...row
      }
      this.title = '编辑数据域'

      this.addDataDomainVisible = true
    },
    // 新增数据域
    handleDataDomainVisible () {
      // 初始化数据
      this.dataDomainForm = {
        name: '',
        description: '',
        index: 0, // 排序
        parentId: 0, // 父级主题id
        publish: 1 // 这里固定是 1
      }
      this.title = '新增数据域'
      this.addDataDomainVisible = true
    },
    // 删除数据域
    deleteDataDomain (row) {
      this.$confirm('此操作将永久删除该数据域, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { code } = await this.$httpBi.indicatorAnagement.deleteZty({
          id: row.id
        })
        if (code === 200) {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.initData()
        }
      })
    },
    // 删除指标
    async delIndicator (row) {
      await this.$refs.AffectScopeDialog.isAffectSpace(
        {
          indCode: row.indCode,
          indType: row.lxbm
        },
        async () => {
          var Api = null
          if (row.lxbm === 'yz') {
            Api = this.$httpBi.indicatorAnagement.deleteYz
          }
          if (row.lxbm === 'ps') {
            Api = this.$httpBi.indicatorAnagement.deletePs
          }
          if (row.lxbm === 'ys') {
            Api = this.$httpBi.compositeIndicator.deleteCompositeIndicator
          }
          if (row.lxbm === 'sq') {
            Api = this.$httpBi.indicatorAnagement.deleteSql
          }
          if (row.lxbm === 'sf') {
            Api = this.$httpBi.indicatorAnagement.deleteSf
          }

          this.$confirm('此操作将永久删除该指标, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(async () => {
            const param = row.lxbm !== 'sf' ? {
              id: row.id,
              indCode: row.indCode
            } : row.id
            const { code } = await Api(param)
            if (code === 200) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.initData()
            }
          })
        }
      )
    },
    // 获取原子指标
    async getYzList () {
      const { data } = await this.$httpBi.indicatorAnagement.getYzList({
        zbmc: ''
      })
      this.yzzbList = data
    },
    closeAdd () {
      this.currentType = null
      this.addDialogVisible = false
      this.initData()
    },
    // 式计算
    async calculation (row) {
      if (row.lxbm === 'ps') {
        this.$refs.DeriveIndicatorCalculate.calculation(row)
        return
      }
      if (row.lxbm === 'ys') {
        this.$refs.CompositeIndicatorCalculate.initData(row.indCode)
      }
      if (row.lxbm === 'sq') {
        this.$refs.SqlIndicatorCalculate.initData(row)
      }
      if (row.lxbm === 'sf') {
        this.$refs.AlgorithmIndicator.initData(row)
      }
    },
    async getCalculationResult () {
      const { data } = await this.$httpBi.indicatorAnagement.tryToCaluIndicator(
        {
          ...this.sjsForm,
          startTime: this.sjsForm.timeRange?.[0] || null,
          endTime: this.sjsForm.timeRange?.[1] || null,
          indCode: this.calculationRow.indCode,
          xsc: this.xsc.map(item => ({
            indCode: item.indCode,
            id: item.id,
            lxbm: item.lxbm,
            zdmc: item.zdmc,
            wdzd: item.wdzd,
            wdzval: item.wdzval.filter(e => e !== '')
          }))
        }
      )
      this.sjstableData = data
    },
    goPage (path) {
      // 另标签打开
      const routerUrl = this.$router.resolve({
        path,
        query: {
          type: 'indicator'
        }
      })
      window.open(routerUrl.href, '_blank')
    },

    // 初始化数据
    async initTransferData () {
      const { data } = await this.$httpBi.api.orgMember({
        id: ''
      })
      this.data = data.map(item => ({
        ...item,
        id: item.id,
        __id: item.id,
        title: item.name,
        hideChildren: false,
        hasChildren: item.type === 1
      }))
    },

    // 获取下级数据
    async transferChild (event) {
      console.log(event.parent)
      const { data } = await this.$httpBi.api.orgMember({
        id: event.parent.id
      })
      const children = data.map(item => ({
        ...item,
        id: item.id,
        __id: item.id,
        title: item.name,
        hideChildren: false,
        hasChildren: item.type === 1,
        parentId: item.parentId
      }))
      event.update(children)
    },

    // 查找最接近的树节点元素
    findClosestTreeNode (element) {
      let current = element
      while (current && !current.classList.contains('el-tree-node__content')) {
        current = current.parentElement
        if (!current || current.classList.contains('el-tree')) {
          return null
        }
      }
      return current
    },

    // 在树数据中查找指定节点
    findNodeInTree (treeData, nodeId) {
      for (const node of treeData) {
        if (String(node.id) === String(nodeId)) {
          return node
        }
        if (node.children && node.children.length > 0) {
          const found = this.findNodeInTree(node.children, nodeId)
          if (found) {
            return found
          }
        }
      }
      return null
    },

    // 处理指标拖拽到树节点的逻辑
    handleIndicatorDropToTree (targetNode) {
      if (this.draggedIndicators.length === 0) {
        return
      }

      this.$confirm(
        `确定要将 ${this.draggedIndicators.length} 个指标移动到 "${targetNode.name}" 指标域吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(async () => {
          try {
            // 收集指标ID列表
            const indCodeList = this.draggedIndicators.map(indicator => ({
              indicatorCode: indicator.indCode,
              indicatorType: indicator.lxbm
            }))
            console.log(this.draggedIndicators, 'this.draggedIndicators')

            // 调用API移动指标到新的指标域
            const { code } =
              await this.$httpBi.indicatorAnagement.batchMoveDomain(
                {
                  domainId: targetNode.id
                },
                indCodeList
              )

            if (code === 200) {
              this.$message.success(
                `成功移动 ${this.draggedIndicators.length} 个指标到 ${targetNode.name}`
              )
              // 刷新数据
              this.initData()
            } else {
              this.$message.error('移动指标失败')
              this.draggedIndicator = null
              this.draggedIndicators = []
            }
          } catch (error) {
            console.error('移动指标时发生错误:', error)
            this.$message.error('移动指标时发生错误')
            this.draggedIndicator = null
            this.draggedIndicators = []
          }
        })
        .catch(() => {
          // 用户取消操作
        })
    },
    // 表格拖拽开始
    handleTableDragStart (event, row) {
      this.isDragging = true
      this.draggedIndicator = row

      // 获取选中的行
      const selectedRows = this.$refs.table.selection
      if (selectedRows && selectedRows.length > 0) {
        // 如果当前行在选中列表中，则拖拽所有选中的行
        const isSelected = selectedRows.some(selected => selected.id === row.id)
        if (isSelected) {
          this.draggedIndicators = selectedRows
        } else {
          this.draggedIndicators = [row]
        }
      } else {
        this.draggedIndicators = [row]
      }

      // 设置拖拽数据
      event.dataTransfer.setData(
        'text/plain',
        JSON.stringify({
          type: 'indicator',
          indicators: this.draggedIndicators
        })
      )

      // 设置拖拽效果
      event.dataTransfer.effectAllowed = 'move'

      // 创建自定义拖拽图像
      const dragImage = this.createDragImage(this.draggedIndicators)
      event.dataTransfer.setDragImage(dragImage, 10, 10)

      // 添加拖拽样式
      event.target.style.opacity = '0.5'
      event.target.classList.add('dragging')

      console.log('开始拖拽指标:', this.draggedIndicators)
    },

    // 表格拖拽结束
    handleTableDragEnd (event) {
      this.isDragging = false

      // 恢复样式
      event.target.style.opacity = '1'
      event.target.classList.remove('dragging')

      // 清理拖拽图像
      const dragImage = document.getElementById('drag-image')
      if (dragImage) {
        dragImage.remove()
      }

      console.log('拖拽结束')
    },

    // 创建拖拽图像
    createDragImage (indicators) {
      // 清理之前的拖拽图像
      const existingImage = document.getElementById('drag-image')
      if (existingImage) {
        existingImage.remove()
      }

      const dragImage = document.createElement('div')
      dragImage.id = 'drag-image'
      dragImage.style.cssText = `
        position: absolute;
        top: -1000px;
        left: -1000px;
        background: #fff;
        border: 2px solid #409eff;
        border-radius: 4px;
        padding: 8px 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        font-size: 12px;
        color: #333;
        white-space: nowrap;
        z-index: 9999;
      `

      if (indicators.length === 1) {
        dragImage.textContent = `移动指标: ${indicators[0].zbmc}`
      } else {
        dragImage.textContent = `移动 ${indicators.length} 个指标`
      }

      document.body.appendChild(dragImage)
      return dragImage
    }
  }
}
</script>

<style scoped lang="scss">
.el-button {
  display: inline-flex;
}
.tags-container {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  overflow: hidden;
  position: relative;
}
.tags-wrapper {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}
::v-deep .el-table:before {
  height: 0;
}
::v-deep .avue-form__group--flex {
  display: flex;

  padding: 15px 24px 0;
  align-items: center;
  background: #fff;
}

.content {
  box-sizing: border-box;
  background: #fff;
  height: calc(100vh - 240px);
  display: flex;

  .left-tree {
    flex: 0 0 224px;
    max-width: 224px;
    height: 100%;
    background: #ffffff;
    border-radius: 5px 0px 0px 5px;
    border: 1px solid #e4e7ed;
    margin-right: 20px;

    .left-head {
      width: 100%;
      height: 40px;
      border-bottom: 1px solid #e4e7ed;
      padding: 0 12px;
      display: flex;
      justify-content: space-between;

      .left-title {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #2f3338;
        line-height: 40px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      .left-btn {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #1563ff;
        line-height: 40px;

        text-align: left;
        font-style: normal;
        text-transform: none;
        cursor: pointer;
      }
    }
  }

  .right-content {
    flex: 1;
    width: 0;
  }
}

::v-deep .el-form-item--small.el-form-item {
  margin-bottom: 15px;
}

.module-content {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  height: calc(100vh - 232px);
  overflow: auto;
}

::v-deep .el-card__body {
  padding: 0 !important;
}

.page {
  display: flex;
  justify-content: flex-end;
}

.card-list {
  display: grid;
  display: grid;
  gap: 24px;
  grid-template-columns: repeat(auto-fill, minmax(170px, 1fr));

  .card-item {
    position: relative;
    height: 170px;
    padding: 40px 19px 0;
    box-sizing: border-box;
    background: #f9f9f9;
    border-radius: 4px;
    text-align: center;

    cursor: pointer;
    border: 1px solid transparent;

    &:hover {
      background: rgba(91, 143, 249, 0.06);
      border-radius: 4px;
      border: 1px solid rgba(91, 143, 249, 0.6);

      .name {
        color: #5b8ff9;
      }

      .desc {
        color: #5b8ff9;
      }

      .card-item-children {
        height: 44px;
      }
    }

    .name {
      height: 16px;
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #222222;
    }

    .desc {
      height: 60px;
      margin-top: 16px;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
      line-height: 20px;
    }

    .card-item-children {
      height: 0;
      overflow: hidden;
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-around;
      background: rgba(2, 31, 86, 0.5);
      border-radius: 0px 0px 4px 4px;
      transition: all 0.3s;

      .card-item-children-item {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50%;
        height: 100%;

        &:last-child {
          position: relative;

          &::after {
            position: absolute;
            left: 0;
            content: '';
            width: 1px;
            height: 14px;
            border: 1px solid #ffffff;
            opacity: 0.55;
          }
        }

        .card-item-children-item-name {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
          line-height: 14px;
          text-align: right;
          font-style: normal;
          text-decoration-line: underline;

          &:hover {
            color: #409eff;
          }
        }
      }
    }
  }
}

.sub-title {
  display: flex;
  align-items: center;
  margin-bottom: 24px;

  .sub-text {
    font-size: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #1d2129;
    margin-right: 5px;
  }

  .sub-line {
    height: 1px;
    flex: 1;
    background: #e5e6eb;
  }
}

::v-deep .el-button--small {
  line-height: 13px;
}
::v-deep .el-dialog__header {
  padding-bottom: 10px;
  border-bottom: 1px solid #edeff0;
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #222222;
}

::v-deep .el-dialog__body {
  padding: 20px 24px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}

::v-deep .el-dialog__wrapper.ys {
  .el-dialog__header {
    margin: 0 24px;
    padding: 20px 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #edeff0;
    font-size: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #222222;
  }

  .el-dialog__body {
    padding: 0px 24px 24px;
    color: #606266;
    font-size: 14px;
    word-break: break-all;
  }
}
</style>
<style lang="scss">
.drag-element {
  /* 禁止文本选择 */
  user-select: none;
  /* 禁用默认拖拽效果 */
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

#project_frame
  .model-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .el-tree-node__expand-icon {
  background-color: transparent;
}

#project_frame
  .model-tree
  .el-tree-node.is-current
  > .el-tree-node__content:has(> span.item-style) {
  position: relative;
  background: #f4f7ff !important;
  box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
    0px 6px 6px -4px rgba(0, 42, 128, 0.12);
  border-radius: 4px;
  border: 1px solid #1563ff;
  cursor: move;

  .el-tree-node__label {
    background: transparent !important;
  }
}

.el-tree-node.dragging > .el-tree-node__content {
  opacity: 0.2;
}
.el-tree.model-tree {
  height: calc(100% - 40px);
  overflow: auto;
  //滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: #cbced1;
  }
  &::-webkit-scrollbar-track {
    border-radius: 2px;
    background-color: #ffffff;
  }
}

.el-tree-node > .el-tree-node__content {
  &.is-current {
    background: #f5f7fa !important;
  }

  &.is-focusable {
    background: #f5f7fa !important;
  }

  &:hover {
    background: #f5f7fa !important;

    > .el-checkbox {
      background-color: transparent !important;
    }

    .el-tree-node__expand-icon {
      background-color: transparent !important;

      border-top-left-radius: 2px;
      border-bottom-left-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }

    .custom-tree-node,
    .el-tree-node__label {
      background-color: transparent !important;

      border-top-right-radius: 2px;
      border-bottom-right-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }

    .custom-tree-btns {
      opacity: 1;
    }
  }
}

#project_frame .el-tree .el-tree-node.is-current > .el-tree-node__content {
  background-color: #f5f7fa !important;

  .el-tree-node__label {
    background-color: transparent;
  }

  .custom-tree-btns {
    opacity: 1;
  }
}

#project_frame
  .el-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .custom-tree-node,
#project_frame
  .el-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .el-tree-node__label {
  background-color: transparent !important;
}

.custom-tree-node {
  padding: 0 10px;
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  .node-item {
    display: flex;
    width: calc(100% - 20px);

    .node-label {
      max-width: calc(100% - 10px);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .custom-tree-btns {
    opacity: 0;
  }
}

.el-table__body-wrapper,
.el-table__fixed-body-wrapper,
.el-table__fixed-right-body-wrapper {
  scrollbar-color: #d4d7de #f5f7fa;
}

.tags-container {
  overflow: hidden;
}

.tags-wrapper {
  display: flex;
  flex-wrap: wrap;
  max-height: 40px;
  overflow: hidden;
}

/* 修复固定列底部多余空白 */
.el-table__fixed-right::before,
.el-table__fixed::before {
  display: none !important;
}

/* 拖拽相关样式 */
.drag-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  cursor: move;
  transition: all 0.2s ease;
}

.drag-handle:hover {
  background-color: #f5f7fa;
  border-radius: 4px;
}

.drag-handle i {
  font-size: 16px;
  transition: transform 0.2s ease;
}

.drag-handle:hover i {
  transform: scale(1.1);
}

/* 树节点拖拽悬停样式 */
.el-tree-node__content.drag-over {
  background-color: #e6f7ff !important;
  border: 2px dashed #1890ff !important;
  border-radius: 4px;
}

/* 拖拽时的表格行样式 */
.el-table__row.dragging {
  opacity: 0.5;
  background-color: #f0f9ff;
}

/* 拖拽图标动画 */
@keyframes dragPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.drag-handle.dragging i {
  animation: dragPulse 1s infinite;
}
</style>
