<template>
  <DT-View class="rolePage-mainView">
    <!-- 按钮 -->
    <el-button type="primary" @click="$emit('handleAdd')">新增</el-button>
    <el-button @click="handleExport">导入</el-button>
    <!-- 表格 -->
    <DT-Table :data="data" :column="tableColumn"></DT-Table>
    <!-- 分页 -->
    <DT-Pagination
      :hidden="pagination.total == 0"
      :total="pagination.total"
      :page-size="pagination.pageSize"
      :current-page="pagination.currentPage"
      @sizeChange="handlePageSizeChange"
      @currentChange="handlePageCurrentChange"
    />
  </DT-View>
</template>

<script>
import Request from "@/service"
export default {
  name: "main-view",

  props: {
    // 数据
    data: {
      type: Array,
      default: () => []
    },
    // 搜索（与index同步）
    search: {
      type: Object,
      default: () => {
        return {
          roleName: "",
          roleTitle: ""
        }
      }
    },
    // 分页配置
    pagination: {
      type: Object,
      default: () => {
        return {
          total: 0,
          pageSize: 10,
          currentPage: 1
        }
      }
    },
    channels: {
      type: Array,
      default: () => []
    },
    sceneType: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      searchForm: {},
      searchRender: [
        {
          label: "预警指标名称",
          type: "input",
          key: "keywords",
          props: {
            placeholder: "请输入预警指标名称"
          }
        }
      ],
      // 表格渲染配置
      tableColumn: [
        {
          label: "算法名称",
          prop: "col1"
        },
        {
          label: "算法介绍",
          prop: "col2"
        },
        {
          label: "引用次数",
          prop: "col3",
        },
        {
          label: "修改时间",
          prop: "col4",
         
        },
        // 操作
        {
          label: "操作",
          width: 150,
          button: [
            {
              label: "编辑",
              onClick: ({ row }) => this.$emit("handleEdit", row.sceneCode)
            },
             {
              label: "删除",
              onClick: ({ row }) => this.$emit("handleDel", row.sceneCode)
            },
            
          ]
        }
      ]
      // // 表格渲染配置
      // tableColumn2: [
      //   {
      //     label: "预警指标id",
      //     prop: "id"
      //   },
      //   {
      //     label: "指标名称",
      //     prop: "sceneName"
      //   },
      //   {
      //     label: "推送时机",
      //     prop: "expectPushTime"
      //   },
      //   {
      //     label: "重复推送",
      //     prop: "isRepeatedPush",
      //     formatter: ({ row }) => {
      //       return row.isRepeatedPush ? "是" : "否"
      //     }
      //   },
      //   {
      //     label: "推送途径",
      //     prop: "types",
      //     slotName: "slot",
      //     slot: true,
      //     width: 600
      //   },
      //   {
      //     label: "接收人",
      //     prop: "pushChannelAccountRole"
      //   },
      //   {
      //     label: "推送状态",
      //     formatter: ({ row }) => {
      //       switch (row.msgStatus) {
      //       case 10:
      //         return <el-tag disable-transitions>新建</el-tag>
      //       case 20:
      //         return (
      //           <el-tag type="danger" disable-transitions>
      //               停用
      //           </el-tag>
      //         )
      //       case 30:
      //         return (
      //           <el-tag type="success" disable-transitions>
      //               启用
      //           </el-tag>
      //         )
      //       case 40:
      //         return <el-tag disable-transitions>等待发送</el-tag>
      //       case 50:
      //         return <el-tag disable-transitions>发送中</el-tag>
      //       case 60:
      //         return (
      //           <el-tag type="success" disable-transitions>
      //               发送成功
      //           </el-tag>
      //         )
      //       case 70:
      //         return (
      //           <el-tag type="danger" disable-transitions>
      //               发送失败
      //           </el-tag>
      //         )
      //       default:
      //         break
      //       }
      //     }
      //   },

      //   // 操作
      //   {
      //     label: "操作",
      //     width: 150,
      //     button: [
      //       {
      //         label: "编辑",
      //         onClick: ({ row }) => this.$emit("handleEdit", row)
      //       },
      //       {
      //         label: "启用",
      //         show: ({ row }) => row.status === 0,
      //         onClick: ({ row }) => this.$emit("handleOpen", row)
      //       },
      //       {
      //         label: "禁用",
      //         show: ({ row }) => row.status === 1,
      //         onClick: ({ row }) => this.$emit("handleClose", row)
      //       }
      //     ]
      //   }
      // ]
    }
  },
  mounted() {
    // 页面初始化时配置搜索双向绑定数据，使表单页返回时搜索框数据保持与之前一致
    this.searchForm = { ...this.search }
  },
  computed: {
    // tableColumn() {
    //   return this.sceneType === 0 ? this.tableColumn1 : this.tableColumn2
    // }
  },
  methods: {
    // 筛选表单 - 查询
    handleSearch(form) {
      this.pagination.currentPage = 1
      Object.keys(form).forEach(key => (this.search[key] = form[key]))
      this.$emit("update:search", this.search)
      this.$emit("search")
    },
    // 分页 - 每页条数改变
    handlePageSizeChange(event) {
      this.pagination.pageSize = event.pageSize
      this.pagination.currentPage = 1
      this.$emit("paginationChange")
    },
    // 分页 - 当前页码改变
    handlePageCurrentChange(event) {
      this.pagination.currentPage = event.currentPage
      this.$emit("paginationChange")
    },
    async handleEditType(val, row) {
      console.log(val, row)
      await Request.msg.paramPost("messageTemplate/save", {
        ...row,
        channelList: val
      })
      this.$emit("search")

      this.$message.success("更新成功")
    }
  }
}
</script>
