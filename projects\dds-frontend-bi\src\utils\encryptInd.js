import CryptoJS from 'crypto-js'

export default {
  // 默认密钥
  defaultKey: 'R23m#7zK$pL9vB1x',
  // 默认初始化向量
  defaultIv: 'N8bE3kY6nD9fH2jQ',

  /**
   * 加密函数 - 使用AES/CBC/PKCS5Padding模式
   * @param {string} word - 需要加密的字符串
   * @param {string} keyStr - 加密密钥（可选）
   * @param {string} ivStr - 初始化向量（可选）
   * @returns {string} Base64编码的加密结果
   */
  encrypt(word, keyStr, ivStr) {
    keyStr = keyStr || this.defaultKey
    ivStr = ivStr || this.defaultIv
    
    const key = CryptoJS.enc.Utf8.parse(keyStr)
    const iv = CryptoJS.enc.Utf8.parse(ivStr)
    const srcs = CryptoJS.enc.Utf8.parse(word)
    
    const encrypted = CryptoJS.AES.encrypt(srcs, key, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    })

    // 返回Base64编码的结果
    return encrypted.toString()
  },

  /**
   * 解密函数 - 使用AES/CBC/PKCS5Padding模式
   * @param {string} word - 需要解密的字符串（Base64编码）
   * @param {string} keyStr - 解密密钥（可选）
   * @param {string} ivStr - 初始化向量（可选）
   * @returns {string} 解密后的原始字符串
   */
  decrypt(word, keyStr, ivStr) {
    keyStr = keyStr || this.defaultKey
    ivStr = ivStr || this.defaultIv
    
    const key = CryptoJS.enc.Utf8.parse(keyStr)
    const iv = CryptoJS.enc.Utf8.parse(ivStr)
    
    const decrypt = CryptoJS.AES.decrypt(word, key, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    })

    return CryptoJS.enc.Utf8.stringify(decrypt).toString()
  },
}