<template>
  <div class="module-wrap">
    <SearchForm
      @search="handleSearch"
      :columns="columns"
      :is-card="false"
      :search-param.sync="form"
      style="margin-bottom: 24px"
    ></SearchForm>
    <CommonTable
      :page.sync="page"
      :table-data="tableData"
      :show-batch-tag="false"
      :loading="loading"
      @handleSortChange="handleSortChange"
      @handleExport="handleExportExcel"
      :table-columns.sync="tableColumns"
      @onload="getData"
      ref="CommonTable"
      id="indCode"
    >
      <template #actionSlot="{ row }">
        <el-button
          type="text"
          style="margin-right: 10px"
          @click="handleDetail(row)"
        >
          查看明细
        </el-button>
      </template>
    </CommonTable>
  </div>
</template>

<script>
import Request from '@/service'
import { exportExcel } from '@/utils'
import SearchForm from '@/components/SearchForm/index.vue'
import CommonTable from '@/components/CommonTable.vue'
export default {
  name: 'AnomalyLog',
  components: {
    SearchForm,
    CommonTable
  },
  props: {},
  data () {
    return {
      form: {
        sortBy: '',
        sortOrder: '',
        dataSource: [],
        indName: '',
        time: [] // 当前日期]
      },
      columns: [
        {
          label: '时间',
          prop: 'time',
          search: {
            el: 'date-picker',
            props: {
              type: 'datetimerange',
              valueFormat: 'yyyy-MM-dd HH:mm:ss'
            }
          }
        },
        {
          label: '指标名称',
          prop: 'indName',
          search: {
            el: 'input'
          }
        },
        {
          prop: 'dataSource',
          label: '数据源',
          fieldNames: {
            label: 'nr',
            value: 'id'
          },
          search: {
            el: 'select',
            props: {
              multiple: true
            }
          }
        }
      ],
      tableColumns: [
        {
          label: '指标名称',
          prop: 'indName',
          visible: true,
          sortable: 'custom'
        },
        {
          label: '当前未解决异常次数',
          prop: 'unresolvedCount',
          visible: true,
          sortable: 'custom'
        },
        {
          label: '近30日异常次数',
          prop: 'recentOneMonthCount',
          visible: true,
          sortable: 'custom'
        },
        {
          label: '历史异常次数',
          prop: 'historyCount',
          visible: true,
          sortable: 'custom'
        },
        {
          label: '最新异常时间',
          prop: 'latestExceptionTime',
          visible: true,
          sortable: 'custom'
        },
        {
          label: '数据源',
          prop: 'dataSource',
          visible: true,
          sortable: false
        },
        {
          label: '已处理异常原因汇总',
          prop: 'handledExceptionReasons',
          visible: true,
          sortable: false
        },
        {
          label: '操作',
          prop: 'action',
          width: 100,
          visible: true,
          slot: true,
          sortable: false
        }
      ],
      page: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      loading: false,
      tableData: []
    }
  },
  computed: {},
  created () {
    this.getData()
  },
  mounted () {},
  watch: {},
  methods: {
    // 处理排序
    handleSortChange ({ prop, order }) {
      if (!order) {
        this.form.sortBy = ''
        this.form.sortOrder = ''
      } else {
        this.form.sortBy = prop
        this.form.sortOrder = order === 'ascending' ? 'asc' : 'desc'
      }
      this.page.currentPage = 1
      this.page.pageSize = 10

      this.getData()
    },
    handleSearch () {
      this.$refs.CommonTable.clearSort()
      this.page.currentPage = 1
      this.getData()
    },
    async getData () {
      this.loading = true
      const { data } = await Request.api.paramPost('zeroWarn/getZeroWarnList', {
        pageNum: this.page.currentPage,
        pageSize: this.page.pageSize,
        dataSource: this.form.dataSource,
        indName: this.form.indName,
        sortBy: this.form.sortBy,
        sortOrder: this.form.sortOrder,
        startTime: this.form.time[0],
        endTime: this.form.time[1]
      })
      this.page.total = data.total
      this.tableData = data.records
      this.loading = false
    },
    // 导出
    handleExportExcel (selection) {
      const indCodes = selection.map(item => item.indCode)
      exportExcel('/api/dds-server-bi/zeroWarn/exportZeroWarnIndicatorList', {
        indName: this.form.indName,
        sortBy: this.form.sortBy,
        sortOrder: this.form.sortOrder,
        startTime: this.form.time[0],
        endTime: this.form.time[1],
        indCodes
      })
    },
    handleDetail (row) {
      this.$emit('handleDetail', row)
    }
  }
}
</script>

<style scoped lang="scss">
.module-wrap {
  width: 100%;
  background: #ffffff;
  padding: 24px;
  border-radius: 4px;
  margin-bottom: 24px;
  box-sizing: border-box;
}
</style>
