<template>
  <el-dialog
    title="图表模型预览"
    :visible.sync="dialogVisible"
    width="880px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <div class="preview-content" v-loading="previewLoading">
      <div v-if="previewError" class="preview-error">
        {{ previewError }}
      </div>
      <div v-else-if="currentChartComponent" class="chart-container">
        <!-- 动态图表组件 -->
        <component
          :is="currentChartComponent"
          v-bind="chartProps"
          @chartClick="handleChartClick"
        />
      </div>
      <div v-else class="preview-empty">请选择图表类型进行预览</div>
    </div>
  </el-dialog>
</template>

<script>
// import axios from 'axios'
import Request from '@/service'
import {
  chartComponentMap,
  // getStaticPreviewData,
  getChartProps
} from './chartComponents.js'

export default {
  props: {},
  data () {
    return {
      dialogVisible: false,
      previewError: '',
      currentChartComponent: null,
      chartProps: {},
      previewLoading: false
    }
  },
  computed: {},
  created () {},
  mounted () {},
  watch: {},
  methods: {
    async openDialog (row) {
      console.log(row)
      this.form = {
        ...row,
        dataScenarios: ''
      }
      this.dialogVisible = true

      this.previewError = ''

      try {
        this.previewLoading = true
        const { data } = await Request.display.getChartData({
          chatCode: row.chartCode
        })
        this.previewLoading = false

        this.chartProps = getChartProps(this.form.chartType, data)

        // 动态加载图表组件
        await this.loadChartComponent(this.form.chartType)
      } catch (error) {
        console.error('预览失败:', error)
        this.previewError = '预览失败，请检查图表类型配置'
      } finally {
        console.log('/////////////////////')
      }
    },
    // 动态加载图表组件
    async loadChartComponent (chartType) {
      try {
        if (chartComponentMap[chartType]) {
          const component = await chartComponentMap[chartType]()
          this.currentChartComponent = component.default || component
        } else {
          this.previewError = `不支持的图表类型: ${chartType}`
        }
      } catch (error) {
        console.error('加载图表组件失败:', error)
        this.previewError = '加载图表组件失败'
      } finally {
        this.previewLoading = false
      }
    }
  }
}
</script>

<style scoped lang="scss">
.preview-content {
  padding: 16px;
  min-height: 200px;

  .preview-error {
    text-align: center;
    color: #f56c6c;
    padding: 40px 0;
  }

  .preview-empty {
    text-align: center;
    color: #909399;
    padding: 40px 0;
  }

  .chart-container {
    width: 100%;
  }
}
::v-deep .el-dialog__header {
  margin: 0 24px;
  padding: 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #edeff0;
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #222222;
}

::v-deep .el-dialog__body {
  padding: 20px 24px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}
</style>
