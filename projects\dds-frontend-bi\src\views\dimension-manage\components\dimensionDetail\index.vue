<template>
  <DT-View :show="true">
    <div class="dimension-page">
      <!-- 顶部导航区 -->
      <div class="header">
        <div class="back" @click="handleBack"></div>
        <span class="title">{{ currentVersionItem.dimName }}</span>
        <div class="attr btn" @click="handleDimAttr">属性</div>
        <div class="version-list">
          <div
            class="version-item"
            :class="{
              active: currentVersionItem.definitionCode === item.definitionCode
            }"
            v-for="item in allVersions"
            :key="item.definitionCode"
            @click="handleVersionClick(item)"
          >
            {{ item.version }}
            <el-dropdown
              v-if="currentVersionItem.definitionCode === item.definitionCode"
              @command="handleCommand"
            >
              <div class="version-dropdown">
                <svg-icon class="" icon-class="more3" />
              </div>

              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="edit">编辑</el-dropdown-item>
                <el-dropdown-item command="del">删除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
        <div class="add" @click="handleAdd"></div>
      </div>

      <div class="content">
        <!-- 左侧边栏 -->
        <div class="sidebar">
          <h3 v-if="currentVersionItem.createType == 0">
            {{ getUpdateFrequencyLabel(currentVersionItem.updateFrequency)
            }}{{
              currentVersionItem.updateType == 1 ? '全量' : '增量'
            }}更新维度值
            <div class="btn" @click="refreshDimValue">更新</div>
          </h3>
          <h3>
            维度层级
            <div class="btn" @click="handleAddDimLevel">增加</div>
          </h3>
          <div
            @dragover="handleDragOver"
            @drop="handleTargetDrop"
            class="target-container"
            ref="targetContainer"
            :style="{
              height: currentVersionItem.createType == 0 ? '284px' : '620px'
            }"
          >
            <draggable
              v-model="dimensionLevels"
              group="dimension"
              animation="500"
              @change="handleMoveLevel"
              handle=".mover"
            >
              <transition-group>
                <div
                  v-for="item in dimensionLevels"
                  class="dim-item"
                  :key="item.level"
                  @click="handleDimLevelClick(item)"
                >
                  <span class="mover"></span>
                  {{ item.sourceField }}
                  <span class="dim-cname">
                    {{ item.levelName }}
                    <i
                      class="el-icon-delete"
                      @click.stop="handleDeleteDimLevel(item)"
                      style="color: red"
                    ></i>
                  </span>
                </div>
              </transition-group>
            </draggable>
          </div>
          <template v-if="currentVersionItem.createType == 0">
            <h3>
              维度值来源表

              <div class="btn" @click="handleSelectTable">选择</div>
            </h3>
            <div class="dimension-body">
              <el-tree
                ref="tree"
                :data="sourceTables"
                :props="{
                  children: 'children',
                  label: 'label',
                  isLeaf: 'leaf'
                }"
                node-key="id"
                :expand-on-click-node="false"
                class="tree"
                draggable
                lazy
                :load="loadNode"
                :allow-drop="allowDrop"
                :allow-drag="allowDrag"
                @node-drag-start="handleTreeDragStart"
                @node-drag-end="handleTreeDragEnd"
              >
                <span
                  slot-scope="{ node }"
                  class="custom-node"
                  :style="{
                    cursor: node.level === 2 ? 'move' : 'default',
                    display: 'flex'
                  }"
                >
                  {{ node.label }}

                  <span style="margin-left: 10px">
                    {{ node.level === 2 ? `(${node.data.comment})` : '' }}
                  </span>
                </span>
              </el-tree>
            </div>
          </template>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
          <h3>
            维度树预览

            <div class="action-buttons">
              <template v-if="isEditDimValue">
                <el-button @click="handleCancelEditDimValue">取消</el-button>
                <el-button type="primary" @click="handleSaveEditDimValue">
                  保存
                </el-button>
              </template>
              <template v-else>
                <div class="btn" @click="handleCodeManage">维度值代码管理</div>
                <div class="btn" @click="handleEditDimValue">编辑维度值</div>
              </template>
            </div>
          </h3>
          <div
            class="table-wrap"
            :style="{
              height: currentVersionItem.createType == 0 ? '618px;' : '494px'
            }"
          >
            <el-table
              v-loading="tableLoading"
              :data="
                dataSource.slice(
                  (currentPage - 1) * pageSize,
                  currentPage * pageSize
                )
              "
              style="width: 100%"
              class="custom-table"
              :max-height="
                currentVersionItem.createType == 0 ? '376px' : '500px'
              "
              border
            >
              <el-table-column prop="level" label="层级" key="level">
                <!-- <template slot-scope="scope">
                <div>
                  {{ scope.row.level }}
                  <el-button
                    v-if="scope.row.children && scope.row.children.length > 0"
                    @click="toggleExpand(scope.row)"
                    type="text"
                  >
                    <i
                      :class="
                        scope.row.isExpanded
                          ? 'el-icon-caret-top'
                          : 'el-icon-caret-right'
                      "
                    ></i>
                  </el-button>
                </div>
              </template> -->
              </el-table-column>
              <!-- 名称列 -->
              <el-table-column
                :label="item.label"
                :prop="item.prop"
                v-for="(item, index) in tableHead"
                :key="item.prop"
              >
                <template slot-scope="scope">
                  <div>
                    <svg-icon
                      v-if="
                        scope.row.children &&
                        scope.row.children.length > 0 &&
                        index + 1 == scope.row.level
                      "
                      @click="toggleExpand(scope.row)"
                      class="icon"
                      :icon-class="
                        scope.row.isExpanded
                          ? 'expanded-bottom'
                          : 'expanded-right'
                      "
                    />
                    <el-input
                      v-if="
                        isEditDimValue &&
                        index + 1 == scope.row.level &&
                        scope.row.fromDataSource != 1
                      "
                      v-model="scope.row.value"
                      style="width: 80%"
                      placeholder="请输入内容"
                      @input="handleInputChange(scope.row, $event)"
                    ></el-input>

                    <span v-else>
                      {{ scope.row[item.prop] }}

                      <template
                        v-if="scope.row.clusterName && scope.row[item.prop]"
                      >
                        ({{ scope.row.clusterName }})
                      </template>
                    </span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="count"
                label="维度值数量"
                key="count"
                v-if="!isEditDimValue"
              ></el-table-column>
              <el-table-column v-else key="action" label="操作">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    @click="insertRow(scope.row, scope.$index, 'current')"
                  >
                    插入
                  </el-button>
                  <el-button
                    type="text"
                    v-if="scope.row.level < tableHead.length"
                    @click="insertRow(scope.row, scope.$index, 'bottom')"
                  >
                    子集追加
                  </el-button>
                  <el-button
                    type="text"
                    size="mini"
                    @click="handleDeleteRow(scope.$index, scope.row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 1000]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="dataSource.length"
            ></el-pagination>
          </div>
          <template v-if="relations.length">
            <h3>表关联关系图示</h3>
            <div class="relations">
              <div
                class="relation-item"
                v-for="(item, index) in relations"
                :key="index"
              >
                <div class="left-table" v-if="index == 0">
                  <div class="inner-text">{{ item.leftTable }}</div>
                </div>
                <div class="left-icon" v-else></div>

                <el-popover
                  placement="right"
                  width="400"
                  trigger="click"
                  content="xxx"
                >
                  <div class="popover-content">
                    <el-radio-group
                      v-model="item.joinType"
                      @change="handleRelations"
                    >
                      <el-radio-button label="LEFT">左关联</el-radio-button>
                      <el-radio-button label="INNER">内关联</el-radio-button>
                      <el-radio-button label="RIGHT">右关联</el-radio-button>
                    </el-radio-group>
                    <div
                      class="fields"
                      style="display: flex; align-items: center"
                    >
                      <div class="right-field">
                        <p>左键</p>

                        <el-select
                          v-model="item.leftField"
                          placeholder="请选择"
                          @change="handleRelations"
                        >
                          <el-option
                            v-for="fieldItem in talbeMap[item.leftTable] || []"
                            :key="fieldItem.name"
                            :label="fieldItem.comment"
                            :value="fieldItem.name"
                          ></el-option>
                        </el-select>
                      </div>
                      <p style="min-width: 60px; text-align: center">等于</p>
                      <div class="right-field">
                        <p>右键</p>
                        <el-select
                          v-model="item.rightField"
                          placeholder="请选择"
                          @change="handleRelations"
                        >
                          <el-option
                            v-for="fieldItem in talbeMap[item.rightTable] || []"
                            :key="fieldItem.name"
                            :label="fieldItem.comment"
                            :value="fieldItem.name"
                          ></el-option>
                        </el-select>
                      </div>
                    </div>
                  </div>

                  <div
                    class="relation-icon"
                    :class="item.joinType"
                    slot="reference"
                  ></div>
                </el-popover>
                <div class="right-table">
                  <div class="inner-text">{{ item.rightTable }}</div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <DimAttrDialog ref="DimAttrDialog" :all-versions="allVersions" />
      <LevelAttrDialog
        ref="LevelAttrDialog"
        :current-version-item="currentVersionItem"
        :dimension-levels="dimensionLevels"
        :source-tables="sourceTables"
        @refresh="getDimValueLevel"
      />
      <AddEditVersion ref="AddEditVersion" @createDimension="createDimension" />
      <CodeManage
        ref="CodeManage"
        :dimension-levels="dimensionLevels"
        :current-version-item="currentVersionItem"
      />

      <el-dialog
        title="选择数据表"
        :visible.sync="innerDialog.dialogVisible"
        width="780px"
        append-to-body
      >
        <div class="table-list">
          <el-input
            placeholder="输入关键字搜索"
            prefix-icon="el-icon-search"
            v-model="key"
            class="search-input"
            @input="getSearchTable"
          ></el-input>
          <div
class="checkbox-warp"
v-loading="loading"
              v-load-more="handleScroll"
          >
            <el-checkbox-group v-model="tableNames">
              <el-checkbox
                v-for="item in dataDomainList"
                :label="item.id"
                :key="item.id"
              >
                <svg-icon icon-class="sjy" />
                {{ item.a }}/{{ item.c }}/{{ item.id }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button
@click="innerDialog.dialogVisible = false"
            >取 消</el-button
          >
          <el-button type="primary" @click="selectTable">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </DT-View>
</template>

<script>
import Request from '@/service'
import draggable from 'vuedraggable'
// 维度属性
import DimAttrDialog from './DimAttrDialog'
// 维度层级属性
import LevelAttrDialog from './LevelAttrDialog'
// 新增编辑维度版本
import AddEditVersion from './AddEditVersion'
// 维度值代码管理
import CodeManage from './CodeManage'
import cloneDeep from 'lodash/cloneDeep'

// import * as tableDataDefault from "./const"
export default {
  components: {
    draggable,
    DimAttrDialog,
    LevelAttrDialog,
    AddEditVersion,
    CodeManage
  },
    directives: {
    loadMore: {
      bind: function (el, binding) {
        el.addEventListener("scroll", function () {
          const threshold = 2 // 容差像素
          const isBottom =
            el.scrollHeight - el.scrollTop - el.clientHeight <= threshold
          if (isBottom) {
            binding.value()
          }
        })
      }
    }
  },

  props: {
    dimItem: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data () {
    return {
      total: 2, // 总条数
      currentPage: 1, // 当前页码
      pageSize: 10, // 每页的数据条数
      currentVersionItem: {}, // 当前版本
      allVersions: [], // 所有版本
      dimensionLevels: [], // 维度层级
      sourceTables: [], // 数据表,
      dataSource: [], // 列表数据
      copyDataSource: [], // 列表数据-备份
      tableHead: [], // 表头
      isEditDimValue: false, // 编辑维度值
      relations: [], // 表关联关系
      talbeMap: {},
      changeInputParentIds: new Set(), // 最顶级的父id
      inputValueMap: {},
      // 内部dimItem，用于路由模式
      internalDimItem: {},
      // 添加选择数据表弹窗相关数据
      innerDialog: {
        dialogVisible: false
      },
      key: '', // 搜索关键字
      loading: false, // 加载状态
      dataDomainList: [], // 数据表列表
      tableNames: [], // 选中的表名
      tableLoading: false, // 表加载状态
      delValueCode: [], // 删除维度值的集合
      updateFrequencys: [
        {
          label: '按日',
          value: 1
        },
        {
          label: '按周',
          value: 2
        },
        {
          label: '按月',
          value: 3
        },
        {
          label: '按学期',
          value: 4
        },
        {
          label: '按学年',
          value: 5
        },
        {
          label: '按年',
          value: 6
        }
      ],
      page: {
        currentPage: 1,
        pageSize: 30,
        total: 0
      }
    }
  },
  computed: {
    // 获取当前使用的dimItem
    currentDimItem () {
      // 如果是路由模式，使用内部的dimItem
      if (this.$route.query.definitionCode) {
        return this.internalDimItem
      }
      // 否则使用props传入的dimItem
      return this.dimItem
    }
  },
  watch: {
    'currentVersionItem.definitionCode': {
      handler (newVal, oldVal) {
        if (newVal === oldVal) return
        this.getCurrentLevel()
        // this.getDimensionValue()
      }
    }
  },
  created () {
    console.log(this.$route, '///////////////')
    // 如果是路由页面模式，从路由参数获取definitionCode
    if (this.$route.query.definitionCode) {
      this.getDimensionInfo(this.$route.query.definitionCode)
      // 从路由参数构造dimItem
      this.internalDimItem = {
        definitionCode: this.$route.query.definitionCode,
        dimName: this.$route.query.dimName || '维度详情'
      }
    } else {
      // 组件模式，使用传入的props
      this.getDimensionInfo()
    }
    this.getVersions()
  },
  methods: {
    // 返回处理
    handleBack () {
      // 如果是路由页面模式，使用路由返回
      if (this.$route.query.definitionCode) {
        this.$router.push({ name: 'DimensionManage' })
      } else {
        // 组件模式，触发back事件
        this.$emit('back')
      }
    },
    // 刷新维度值
    async refreshDimValue () {
      await Request.api.paramPost('/DimManage/refreshDimValue', {
        definitionCode: this.currentVersionItem.definitionCode,
        updateType: this.currentVersionItem.updateType
      })
      this.$message.success('维度值刷新成功')
      this.getDimensionValue()
    },
    getUpdateFrequencyLabel (frequency) {
      return this.updateFrequencys.find(item => item.value === frequency).label
    },
    handleSizeChange (val) {
      console.log(`每页 ${val} 条`)
      this.currentPage = 1
      this.pageSize = val
    },
    handleCurrentChange (val) {
      console.log(`当前页: ${val}`)
      this.currentPage = val
    },
    // handleInputChange
    handleInputChange (row, val) {
      this.changeInputParentIds.add(row.id)

      // 递归查找并更新目标节点
      const updateNode = (nodes, targetId, val) => {
        for (const node of nodes) {
          if (node.id === targetId) {
            // 使用 $set 确保响应式更新
            this.$set(node, 'value', val)
            return true
          }
          if (node.children && node.children.length > 0) {
            const found = updateNode(node.children, targetId, val)
            if (found) return true
          }
        }
        return false
      }

      // 调用递归函数更新数据
      updateNode(this.dataSource, row.id, val)

      console.log(this.dataSource, 'Updated dataSource')
    },
    // 获取维度层级和维度值
    getDimValueLevel () {
      this.getCurrentLevel()
      this.getDimensionValue()
    },
    // 编辑维度值
    handleEditDimValue () {
      this.delValueCode = []
      this.isEditDimValue = true

      // 如果数据为空，添加一条空数据
      if (this.dataSource.length === 0) {
        const timestamp = Date.now()
        const result = Object.fromEntries(
          this.tableHead.map(item => [item.prop, null])
        )

        // 获取第一个层级的levelCode
        const firstLevel = this.dimensionLevels.find(item => item.level === 1)

        const newRow = {
          ...result,
          level: 1,
          levelCode: firstLevel?.levelCode || '',
          definitionCode: this.currentVersionItem.definitionCode,
          isDisplay: 1,
          children: [],
          valueCode: '',
          fromDataSource: 0,
          id: timestamp,
          value: '',
          isExpanded: false
        }

        this.dataSource.push(newRow)
        this.copyDataSource = cloneDeep(this.dataSource)
      }
    },
    // 保存编辑维度值
    async handleSaveEditDimValue () {
      // 检查是否有空值
      const hasEmptyValue = this.dataSource.some(row => {
        if (row.fromDataSource === 1) return false // 数据源数据不检查
        return !row.value || row.value.trim() === ''
      })

      if (hasEmptyValue) {
        this.$message.warning('存在未填写的内容，请填写完整后保存')
        return
      }

      const paramsData = this.dataSource.filter(child => child.level === 1)
      // 对delValueCode进行去重
      const uniqueDelValueCode = [...new Set(this.delValueCode)]
      console.log(paramsData, 'paramsData')
      console.log(uniqueDelValueCode, 'uniqueDelValueCode')

      const { data } = await Request.api.paramPost('/dimValueManage/edit', {
        dimCode: this.currentVersionItem.definitionCode,
        delValueCode: uniqueDelValueCode,
        dimValue: paramsData
      })

      this.$message.success('保存成功')
      this.isEditDimValue = false
      this.getDimensionValue()

      console.log(data, 'data')
    },
    // 取消编辑维度值
    handleCancelEditDimValue () {
      this.dataSource = cloneDeep(this.copyDataSource)
      this.isEditDimValue = false
    },
    // 获取维度值
    async getDimensionValue () {
      this.tableLoading = true
      this.tableHead = []
      const { data } = await Request.api.paramGet('/dimValueManage/queryList', {
        dimCode: this.currentVersionItem.definitionCode
      })
      // console.log(data, "data")
      // console.log(tableDataDefault, "tableDataDefault")
      // const data = tableDataDefault.data
      this.dataSource = data.tableData
      this.copyDataSource = cloneDeep(data.tableData)
      this.tableHead = data.tableHead
      this.tableLoading = false
    },
    // 切换展开状态
    toggleExpand (node) {
      node.isExpanded = !node.isExpanded
      const index = this.dataSource.indexOf(node)
      if (node.isExpanded) {
        // 展开子节点
        console.log('展开子节点')
        console.log('收起子节点前 dataSource:', [...this.dataSource])

        const children =
          node.children?.map(child => ({
            ...child,
            level: node.level + 1
          })) || []
        this.dataSource.splice(index + 1, 0, ...children)
        console.log('收起子节点后 dataSource:', [...this.dataSource])
      } else {
        // 收起子节点
        console.log('收起子节点前 dataSource:', [...this.dataSource])
        let removeCount = 0
        while (this.dataSource[index + 1 + removeCount]?.level > node.level) {
          removeCount++
        }
        this.dataSource.splice(index + 1, removeCount)
        console.log('收起子节点后 dataSource:', [...this.dataSource])
      }
    },
    // 维度值管理
    handleCodeManage () {
      this.$refs.CodeManage.openDialog()
    },
    // 新增维度层级
    handleAddDimLevel () {
      this.$refs.LevelAttrDialog.openDialog({
        definitionCode: this.currentVersionItem.definitionCode
      })
    },
    async handleDeleteDimLevel (item) {
      await this.$confirm('确定要删除该维度层级吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      // 调用删除接口，需替换为实际接口
      await Request.api.paramGet('/dimLevel/delDimLevel', {
        levelCode: item.levelCode
      })
      this.$message.success('删除成功')
      // 重新获取维度层级和维度值
      this.getDimValueLevel()
    },
    // 获取当前维度层级
    async getCurrentLevel () {
      const { data } = await Request.api.paramPost(
        '/dimLevel/searchDimLevelList',
        {
          definitionCode: this.currentVersionItem.definitionCode,
          version: this.currentVersionItem.version
        }
      )
      this.dimensionLevels = data.sort((a, b) => a.level - b.level)
      if (this.dimensionLevels.length > 0) {
        this.initRelations()
        this.handleMoveLevel()
      } else {
        this.getDimensionValue()
      }
    },
    // 获取维度信息及层级信息
    async getDimensionInfo (
      definitionCode = this.currentDimItem.definitionCode
    ) {
      const { data } = await Request.api.paramPostQuery(
        'DimManage/getDimInformation',
        {
          definitionCode: definitionCode
        }
      )

      this.currentVersionItem = data.dimDefinition
      this.relations = data.dimTableRelations
      // this.dimensionLevels = data.dimLevels

      // 使用 Map 去重 并且item.sourceTable存在

      const uniqueMap = new Map()
      data.dimLevels.forEach(item => {
        if (!uniqueMap.has(item.sourceTable)) {
          uniqueMap.set(item.sourceTable, {
            id: item.sourceTable,
            label: item.sourceTable
          })
        }
      })
      this.$nextTick(() => {
        // 去重过滤手动添加为空的数据源表
        this.sourceTables = Array.from(uniqueMap.values()).filter(
          item => item.id
        )
      })
    },
    // 查看维度属性
    handleDimAttr () {
      this.$refs.DimAttrDialog.openDialog(this.currentVersionItem)
    },
    // 维度层级点击
    async handleDimLevelClick (item) {
      console.log('点击的 item:', item) // 添加调试信息

      const { data } = await Request.api.paramGet(
        '/dimLevel/searchDimLevelByLevelCode',
        {
          levelCode: item.levelCode
        }
      )
      this.$refs.LevelAttrDialog.openDialog(data)
    },
    // 维度下拉菜单点击
    handleCommand (command) {
      console.log(command)
      if (command === 'edit') {
        this.$refs.AddEditVersion.openDialog(this.currentVersionItem)
      }
      if (command === 'del') {
        this.handleDelete()
      }
    },
    // 新增
    handleAdd () {
      this.$refs.AddEditVersion.openDialog({
        categoryCode: this.currentVersionItem.categoryCode,
        categoryName: this.currentVersionItem.categoryName,
        dimName: this.currentVersionItem.dimName
      })
    },
    // 创建成功
    createDimension (item) {
      this.getVersions()

      this.getDimensionInfo(item.definitionCode)
    },
    // 编辑
    handleVersionClick (item) {
      this.getDimensionInfo(item.definitionCode)
      // this.$refs.AddEditVersion.openDialog(item)
    },
    // 删除
    handleDelete () {
      this.$confirm('是否删除选中版本', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          Request.api
            .paramDel('/DimManage/deleteDimByCode', {
              code: this.currentVersionItem.definitionCode
            })
            .then(async () => {
              this.$message.success('删除成功')
              // 如果只有一个版本那就返回列表页
              if (this.allVersions.length === 1) {
                this.handleBack()
              } else {
                // 否则获取版本列表第一个版本
                const { data } = await Request.api.paramPostQuery(
                  'DimManage/getDimListByName',
                  {
                    dimName: this.currentDimItem.dimName
                  }
                )
                this.allVersions = data
                this.getDimensionInfo(data[0].definitionCode)
              }
            })
        })
        .catch(error => {
          console.error(error)
        })
    },
    // 表关联关系初始化
    initRelations () {
      const sourceTables = this.dimensionLevels.map(item => item.sourceTable)
      const uniqueSourceTables = [...new Set(sourceTables)]
      // 将this.relations中leftTable和rightTable和joinType值存储
      const selectedRelations = [...this.relations]
      uniqueSourceTables.forEach(async item => {
        if (!this.talbeMap[item.label]) {
          const { data } = await Request.api.paramGet(
            '/bi/source/getTableAndColumn',
            {
              dbName: 'ods',
              tableName: item
            }
          )
          this.$set(this.talbeMap, item, data.columns)
        }
      })
      console.log(this.talbeMap, 'this.talbeMap')
      // 记录如果已经选择关联关系和字段
      if (uniqueSourceTables.length > 1) {
        this.relations = uniqueSourceTables
          .slice(0, uniqueSourceTables.length - 1)
          .map((item, index) => ({
            definitionCode: this.currentVersionItem.definitionCode,
            leftTable: selectedRelations[index]?.leftTable || item,
            leftField: selectedRelations[index]?.leftField || '',
            rightTable: uniqueSourceTables[index + 1],
            rightField: selectedRelations[index]?.rightField || '',
            joinType: selectedRelations[index]?.joinType || 'LEFT'
          }))
      } else {
        this.relations = []
      }
      console.log(this.relations, 'relation****************s')
    },
    // 处理维度表关联关系
    async handleRelations () {
      let flag = false
      this.relations.forEach(item => {
        if (!item.rightField || !item.leftField) {
          flag = true
        }
      })
      if (flag) {
        this.$message.warning('请选择表关联键值')
        this.createLoading = false

        return
      }
      await Request.api.paramPost('/DimManage/configRelations', this.relations)
      this.getDimensionValue()
    },
    async insertRow (currRow, index, locat) {
      // 获取时间戳
      const timestamp = Date.now()
      const result = Object.fromEntries(
        this.tableHead.map(item => [item.prop, null])
      )

      const i = (this.currentPage - 1) * this.pageSize + index
      console.log(result, 'result')
      if (locat === 'current') {
        const newRow = {
          ...currRow,
          ...result,
          level: currRow.level,
          children: [],
          valueCode: '',
          fromDataSource: 0,
          isDisplay: 1,
          id: timestamp,
          value: '',
          parentId: currRow.parentId,
          parentCode: currRow.parentCode
        }

        // 递归查找包含当前节点的父节点
        const findParentWithChild = (nodes, targetId) => {
          for (const node of nodes) {
            if (node.children) {
              // 检查当前节点的children中是否包含目标节点
              const hasChild = node.children.some(
                child => child.id === targetId
              )
              if (hasChild) {
                return node
              }
              // 递归检查子节点
              const found = findParentWithChild(node.children, targetId)
              if (found) {
                return found
              }
            }
          }
          return null
        }

        // 找到包含当前节点的父节点
        const parent = findParentWithChild(this.dataSource, currRow.id)
        if (parent) {
          // 确保父节点的children数组存在
          if (!parent.children) {
            this.$set(parent, 'children', [])
          }
          // 在父节点的children中添加新行
          parent.children.push(newRow)
        }

        // 在dataSource中插入新行
        this.dataSource.splice(i, 0, newRow)
      } else if (locat === 'bottom') {
        // 获取下一层级的levelCode
        const nextLevel = this.dimensionLevels.find(
          item => item.level === currRow.level + 1
        )
        if (!nextLevel) {
          this.$message.warning('没有找到下一层级')
          return
        }

        // 创建新的子节点
        const newRow = {
          ...result,
          level: currRow.level + 1,
          levelCode: nextLevel.levelCode,
          parentCode: currRow.valueCode,
          definitionCode: this.currentVersionItem.definitionCode,
          parentId: currRow.id,
          children: [],
          valueCode: '',
          fromDataSource: 0,
          value: '',
          isDisplay: 1,
          id: timestamp,
          isExpanded: false
        }

        // // 递归查找并添加子节点到正确的父节点
        // const addChildToParent = (nodes, parentId, newChild) => {
        //   for (let i = 0; i < nodes.length; i++) {
        //     if (nodes[i].id === parentId) {
        //       // 确保父节点有children数组
        //       if (!nodes[i].children) {
        //         this.$set(nodes[i], 'children', [])
        //       }
        //       // 添加新子节点
        //       nodes[i].children.push(newChild)
        //       // 如果父节点是展开状态，则在dataSource中也添加这个子节点
        //       if (nodes[i].isExpanded) {
        //         const parentIndex = this.dataSource.findIndex(node => node.id === parentId)
        //         if (parentIndex !== -1) {
        //           this.dataSource.splice(parentIndex + 1, 0, newChild)
        //         }
        //       }
        //       return true
        //     }
        //     // 递归检查子节点
        //     if (nodes[i].children && nodes[i].children.length > 0) {
        //       if (addChildToParent(nodes[i].children, parentId, newChild)) {
        //         return true
        //       }
        //     }
        //   }
        //   return false
        // }

        // // 添加子节点到树中
        // addChildToParent(this.dataSource, currRow.id, newRow)

        // // 如果当前节点是展开状态，则在dataSource中也添加这个子节点
        // if (currRow.isExpanded) {
        //   const parentIndex = this.dataSource.findIndex(node => node.id === currRow.id)
        //   if (parentIndex !== -1) {
        //     this.dataSource.splice(parentIndex + 1, 0, newRow)
        //   }
        // }
        // 确保children数组存在
        if (!currRow.children) {
          this.$set(currRow, 'children', [])
        }
        // 使用Vue的响应式方法添加子节点
        currRow.children.push(newRow)

        // 如果父节点是展开状态，则添加到dataSource中
        if (currRow.isExpanded) {
          this.dataSource.splice(i + 1, 0, newRow)
        }
      }

      console.log(this.dataSource, 'this.dataSource')
    },
    // 删除指定行
    handleDeleteRow (index, row) {
      // 递归收集所有子节点的valueCode
      const collectValueCodes = node => {
        let codes = []
        if (node.valueCode) {
          codes.push(node.valueCode)
        }
        if (node.children && node.children.length > 0) {
          node.children.forEach(child => {
            codes = codes.concat(collectValueCodes(child))
          })
        }
        return codes
      }

      // 递归删除dataSource中的子节点
      const removeChildrenFromDataSource = parentId => {
        let i = 0
        while (i < this.dataSource.length) {
          if (this.dataSource[i].parentId === parentId) {
            // 递归删除子节点的子节点
            removeChildrenFromDataSource(this.dataSource[i].id)
            // 删除当前子节点
            this.dataSource.splice(i, 1)
          } else {
            i++
          }
        }
      }

      if (row.parentCode) {
        // 查找父节点
        const parent = this.findParent(this.dataSource, row.parentId)
        if (parent && parent.children) {
          // 在父节点的 children 中删除当前行
          const childIndex = parent.children.findIndex(
            child => child.id === row.id
          )
          if (childIndex !== -1) {
            parent.children.splice(childIndex, 1)
          }
        }
        // 删除当前行及其子节点
        removeChildrenFromDataSource(row.id)
        this.dataSource.splice(index, 1)
      } else {
        // 直接从 dataSource 中删除当前行及其子节点
        removeChildrenFromDataSource(row.id)
        this.dataSource.splice(index, 1)
      }

      // 收集并添加所有相关的valueCode
      const valueCodes = collectValueCodes(row)
      if (valueCodes.length > 0) {
        this.delValueCode.push(...valueCodes)
      }
      console.log(this.delValueCode, 'this.delValueCode')
    },
    // 递归查找父节点
    findParent (tree, targetId, parent = null) {
      for (const node of tree) {
        if (node.id === targetId) {
          return parent || node
        }
        if (node.children && node.children.length > 0) {
          const found = this.findParent(node.children, targetId, node)
          if (found) return found
        }
      }
      return null
    },
    // 获取当前维度所有版本
    async getVersions () {
      // 如果是路由模式且还没有dimName，先获取维度信息
      if (this.$route.query.definitionCode && !this.currentDimItem.dimName) {
        await this.getDimensionInfo(this.$route.query.definitionCode)
      }

      const { data } = await Request.api.paramPostQuery(
        'DimManage/getDimListByName',
        {
          dimName:
            this.currentDimItem.dimName || this.currentVersionItem.dimName
        }
      )
      this.allVersions = data
    },
    // 懒加载维度表维度值
    async loadNode (node, resolve) {
      console.log(node, 'node')
      if (node.level === 0) {
        return
      }

      const { data } = await Request.api.paramGet(
        '/bi/source/getTableAndColumn',
        {
          dbName: 'ods',
          tableName: node.data.label
        }
      )
      resolve(
        data.columns.map(item => ({
          ...item,
          id: item.name,
          label: item.name,
          sourceTable: data.name,
          leaf: true
        }))
      )
    },
    // tree节点能否被拖拽
    allowDrag (draggingNode) {
      console.log(draggingNode, '拖拽节点')
      if (draggingNode.level === 2) {
        return true
      }
    },
    // tree拖拽开始
    handleTreeDragStart (node, event) {
      console.log(node)
      if (node.level === 2) {
        this.isDragging = true
        // 在拖拽开始时设置拖拽节点的数据
        event.dataTransfer.setData('draggingItem', JSON.stringify(node.data))
      } else {
        return false
      }
    },
    allowDrop () {
      return false
    },
    handleDragOver (e) {
      e.preventDefault()
    },
    async handleTargetDrop (e) {
      if (e.target === this.$refs.targetContainer) {
        e.preventDefault() // 阻止默认行为
        const data = JSON.parse(e.dataTransfer.getData('draggingItem'))

        // 检查是否重复
        const isDuplicate = this.dimensionLevels.some(
          item =>
            item.sourceTable === data.sourceTable &&
            item.sourceField === data.label
        )

        if (isDuplicate) {
          this.$message.warning('该维度层级已存在，请勿重复添加')
          return
        }

        // 调用后端接口保存维度层级
        await Request.api.paramPost('/dimLevel/saveOrUpdateDimLevel', {
          sourceField: data.label,
          sourceTable: data.sourceTable,
          levelName: data.comment,
          level: this.dimensionLevels.length + 1,
          definitionCode: this.currentVersionItem.definitionCode,
          timeType: '0',
          hiddenValues: '0',
          fieldValueType: ''
        })

        // 刷新维度层级和维度值
        this.getDimValueLevel()

        console.log(data, this.dimensionLevels, '拖拽节点')
      }
    },
    // 处理维度层级拖拽排序
    async handleMoveLevel () {
      try {
        // 获取拖拽后的新顺序
        const newLevels = this.dimensionLevels.map((item, index) => ({
          ...item,
          level: index + 1,
          parentCode:
            index === 0 ? 0 : this.dimensionLevels[index - 1].levelCode
        }))

        // 调用后端接口更新层级顺序
        await Request.api.paramPost('/dimLevel/updateLevelList', newLevels)

        // 更新本地数据
        this.dimensionLevels = newLevels
        // this.$message.success("层级顺序更新成功")
        this.getDimensionValue()
      } catch (error) {
        console.error('更新层级顺序失败:', error)
        this.$message.error("更新层级顺序失败")
        // 发生错误时重新获取数据
        // this.getDimValueLevel()
      }
    },
    // 打开选择数据表弹窗
    handleSelectTable () {
      this.innerDialog.dialogVisible = true
      this.getTableList()
    },


     getSearchTable () {
      this.page.currentPage = 1
      this.dataDomainList = []
      this.getTableList()
    },
    handleScroll () {
      if (!this.loading && this.dataDomainList.length < this.page.total) {
        this.page.currentPage++
        this.getTableList()
      }
    },
    // 获取数据表列表
    async getTableList () {
      this.loading = true
      try {
        const { data } = await this.$httpBi.indicatorAnagement.getTableList({
          key: this.key,
          currentPage: this.page.currentPage,
          pageSize: this.page.pageSize
        })
        this.dataDomainList.push(...data.list)
        this.page.total = data.totalCount
        this.loading = false
      } catch (error) {
        console.error('获取数据表列表失败:', error)
        this.$message.error('获取数据表列表失败')
      } finally {
        this.loading = false
      }
    },

    // 确认选择数据表
    selectTable () {
      if (this.tableNames.length === 0) {
        this.$message.warning('请至少选择一个数据表')
        return
      }

      // 将选中的表添加到 sourceTables
      const selectedTables = this.tableNames.map(item => ({
        id: item,
        label: item
      }))

      // 更新 sourceTables，避免重复
      const existingTableIds = new Set(this.sourceTables.map(table => table.id))
      const newTables = selectedTables.filter(
        table => !existingTableIds.has(table.id)
      )

      if (newTables.length > 0) {
        this.sourceTables = [...this.sourceTables, ...newTables]
        this.$message.success(`成功添加 ${newTables.length} 个数据表`)
      } else {
        this.$message.warning('所选数据表已存在')
      }

      this.innerDialog.dialogVisible = false
      this.tableNames = [] // 清空选择
      this.key = '' // 清空搜索关键字
    }
  }
}
</script>

<style scoped lang="scss">
.table-list {
  width: 640px;
  margin: 20px auto;

  .checkbox-warp {
    height: calc(100vh - 420px);
    overflow: auto;
    margin-top: 24px;
  }

  ::v-deep .el-checkbox-group {
    display: flex;
    flex-direction: column;
  }

  ::v-deep .el-checkbox {
    margin-bottom: 24px;
  }

  //滚动条样式
  ::-webkit-scrollbar {
    width: 2px;
    height: 2px;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #cbced1;
  }

  ::-webkit-scrollbar-track {
    border-radius: 3px;
    background-color: transparent;
  }
}

.dimension-page {
  display: flex;
  flex-direction: column;
}

.btn {
  padding: 0 8px;
  height: 20px;
  border-radius: 2px;
  border: 1px solid #1563ff;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #1563ff;
  line-height: 20px;
  text-align: center;
  font-style: normal;
  cursor: pointer;

  &:hover {
    background: #f4f7ff;
  }
}

.custom-node {
  display: flex;
}

.header {
  display: flex;
  align-items: center;
  height: 32px;

  .back {
    width: 20px;
    height: 20px;
    background: url('~@/assets/images/back.png') no-repeat center;
    cursor: pointer;
  }

  .title {
    margin-left: 16px;
    margin-right: 8px;
    height: 20px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 20px;
    color: #222222;
    line-height: 20px;
    text-align: left;
    font-style: normal;
  }

  .attr {
    margin-right: 32px;
  }

  .version-list {
    display: flex;

    .version-item {
      position: relative;
      padding: 0 16px;
      height: 32px;
      background: #f9f9f9;
      border-radius: 4px;
      border: 1px solid #c8cbd1;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      line-height: 32px;
      text-align: right;
      font-style: normal;
      margin-right: 8px;
      cursor: pointer;

      &:hover {
        background: #f4f7ff;
        border: 1px solid #1563ff;
        color: #1563ff;
      }

      .version-dropdown {
        width: 23px;
        height: 30px;
        cursor: pointer;
        border-left: 1px solid #1563ff;
        border-radius: 0 4px 4px 0;
        margin-left: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      &.active {
        border: 1px solid #1563ff;
        background: #f4f7ff;
        color: #1563ff;
        padding: 0 0 0 16px;
      }
    }
  }

  .add {
    width: 21px;
    height: 20px;
    background: url('~@/assets/images/add1.png') no-repeat center;
    cursor: pointer;
  }
}

.action-buttons .el-button {
  margin-left: 10px;
}

.content {
  display: flex;
  height: 680px;
  overflow: hidden;
  margin-top: 20px;
}

.sidebar {
  min-width: 240px;
  overflow-y: auto;
  margin-right: 20px;

  h3 {
    display: flex;
    justify-content: space-between;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #222222;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    height: 32px;
    align-items: center;
  }

  .target-container {
    width: 240px;
    background: #f5f7fa;
    border-radius: 8px;
    padding: 16px 12px;
    box-sizing: border-box;
    margin: 8px 0 10px;

    .dim-item {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #666666;
      line-height: 12px;
      text-align: left;
      font-style: normal;
      margin-bottom: 24px;
      display: flex;
      cursor: pointer;

      &:hover {
        color: #1563ff;
      }

      .mover {
        width: 12px;
        height: 12px;
        background: url('~@/assets/images/drag.png') no-repeat;
        cursor: move;
        margin-right: 8px;
      }

      .dim-cname {
        margin-left: auto;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #222222;
        line-height: 12px;
        text-align: center;
        font-style: normal;
      }
    }
  }

  .dimension-body {
    width: 240px;
    height: 252px;
    background: #f5f7fa;
    border-radius: 8px;
    margin-top: 10px;
    padding: 12px 10px;
    box-sizing: border-box;
    overflow: auto;
    //滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 3px;
      background-color: #cbced1;
    }
    &::-webkit-scrollbar-track {
      border-radius: 3px;
      background-color: transparent;
    }

    .el-tree {
      background: #f5f7fa;
    }
  }
}

.main-content {
  width: calc(100% - 260px);

  h3 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #222222;
    text-align: left;
    font-style: normal;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .action-buttons {
      display: flex;
      align-items: center;

      .btn {
        padding: 0 8px;
        height: 32px;
        border-radius: 4px;
        border: 1px solid #1563ff;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #1563ff;
        line-height: 32px;
        text-align: center;
        font-style: normal;
        cursor: pointer;

        &:hover {
          background: #f4f7ff;
        }

        margin-left: 8px;
      }
    }
  }

  .table-wrap {
    margin-top: 8px;
    width: 100%;

    background: #f5f7fa;
    border-radius: 8px;
    padding: 20px 20px 20px;
    box-sizing: border-box;
    margin-bottom: 20px;
  }

  .relations {
    width: 100%;
    height: 72px;
    background: #f5f7fa;
    border-radius: 8px;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    overflow-x: auto;
    margin-top: 16px;

    .relation-item {
      display: flex;
      align-items: center;

      .left-table,
      .right-table {
        position: relative;
        width: 166px;
        height: 32px;
        background: #ffffff;
        border-radius: 6px;
        border: 1px solid #1563ff;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #222222;
        line-height: 32px;
        text-align: center;
        font-style: normal;

        .inner-text {
          width: 100%;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }

      .left-table {
        position: relative;
        z-index: 99;

        &::after {
          position: absolute;
          top: 15px;
          right: -24px;
          content: '';
          width: 24px;
          height: 1px;
          border: 1px solid #1563ff;
        }

        &::before {
          content: '';
          width: 4px;
          height: 8px;
          background: #1563ff;
          position: absolute;
          border-radius: 0 4px 4px 0;
          top: 12px;
          right: -4px;
          // transform: translate(100%);
        }
      }

      .left-icon {
        position: relative;
        z-index: 99;
        height: 32px;

        &::after {
          position: absolute;
          top: 15px;
          right: -24px;
          content: '';
          width: 24px;
          height: 1px;
          border: 1px solid #1563ff;
        }

        &::before {
          content: '';
          width: 4px;
          height: 8px;
          background: #1563ff;
          position: absolute;
          border-radius: 0 4px 4px 0;
          top: 12px;
          right: -4px;
          // transform: translate(100%);
        }
      }

      .right-table {
        position: relative;
        z-index: 99;

        &::after {
          position: absolute;
          top: 15px;
          left: -24px;
          content: '';
          width: 24px;
          height: 1px;
          border: 1px solid #1563ff;
        }

        &::before {
          content: '';
          width: 4px;
          height: 8px;
          background: #1563ff;
          position: absolute;
          border-radius: 4px 0 0 4px;
          /* 左上 + 左下为圆角 */
          top: 12px;
          left: -4px;
          // transform: translate(100%);
        }
      }

      .relation-icon {
        width: 40px;
        height: 24px;
        margin: 0 24px;
        cursor: pointer;

        &.LEFT {
          background: url('~@/assets/images/LEFT.png') no-repeat;
        }

        &.RIGHT {
          background: url('~@/assets/images/RIGHT.png') no-repeat;
        }

        &.INNER {
          background: url('~@/assets/images/INNER.png') no-repeat;
        }
      }
    }
  }
}

.relation-chart {
  height: 200px;
  background-color: #f5f7fa;
  border: 1px solid #ebeef5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
}
</style>
<style>
#project_frame .el-table.custom-table {
  margin: 0 0 10px !important;
}
</style>
