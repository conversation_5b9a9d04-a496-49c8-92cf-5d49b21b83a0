<template>
  <DT-View>
    <div class="title">创建派生指标</div>
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="form"
    >
      <el-form-item label="指标名称" prop="zbmc">
        <el-input v-model="form.zbmc" clearable></el-input>
      </el-form-item>
      <el-form-item label="选择基础指标" prop="atomid">
        <el-select
          v-model="form.atomid"
          placeholder="选择基础指标"
          filterable
          clearable
        >
          <el-option
            v-for="(item, index) in yzzbList"
            :key="index"
            :label="item.zbmc"
            :value="item.id"
          >
            <span style="float: left">{{ item.zbmc }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ item.zblx }}
            </span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="计算方式" prop="jsfs">
        <div style="display: flex">
          <el-select
            v-model="form.jsfs"
            placeholder="请选择计算方式"
            :style="{ width: form.jsfs === 'sort' ? '76px' : '250px' }"
            class="myselect"
          >
            <el-option
              v-for="(item, index) in jsfsList"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <template v-if="form.jsfs === 'sort'">
            <el-select
              style="margin-left: 10px"
              v-model="form.sorttype"
              placeholder="排序方式"
              :style="{ width: '76px' }"
              class="myselect"
            >
              <el-option
                v-for="(item, index) in sortTypeList"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <el-select
              style="margin-left: 10px"
              v-model="form.sortrange"
              v-if="form.jsfs === 'sort'"
              :style="{ width: '76px' }"
              class="myselect"
            >
              <el-option
                v-for="(item, index) in sortDefineList"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <el-input
              :style="{ width: '76px' }"
              style="margin-left: 10px"
              placeholder="数值"
              v-if="form.sortrange === 'top'"
              v-model.number="form.sortlimit"
              clearable
            ></el-input>
          </template>
        </div>
      </el-form-item>
      <el-form-item label="派生维度" style="margin-bottom: 0">
        <div class="form-item" v-for="(item, index) in form.xsc" :key="index">
          <el-row type="flex" gutter="10">
            <el-col>
              <!-- :prop="'xsc.' + index + '.adid'"
                :rules="{
                  required: true,
                  message: '请选择派生维度',
                  trigger: 'blur'
                }" -->
              <el-form-item>
                <div style="display: flex">
                  <el-select
                    v-model="item.adid"
                    clearable
                    placeholder="请选择派生维度"
                    @change="change($event, index)"
                    style="width: 122px"
                    class="myselect"
                  >
                    <el-option
                      v-for="ele in currentDerive.pswd"
                      :disabled="form.xsc.some(e => e.adid === ele.id)"
                      :key="ele.id"
                      :label="ele.zdmc"
                      :value="ele.id"
                    ></el-option>
                  </el-select>

                  <el-select
                    v-if="
                      pswdOptionsMap[item.adid] &&
                      pswdOptionsMap[item.adid].length
                    "
                    v-model="item.wdzval"
                    multiple
                    collapse-tags
                    style="width: 122px; margin-left: 6px"
                    class="myselect"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="e in pswdOptionsMap[item.adid]"
                      :key="e.bm"
                      :label="e.name"
                      :value="e.bm"
                      :disabled="isDisabled(e.bm, item.wdzval)"
                    ></el-option>
                  </el-select>
                </div>
              </el-form-item>
            </el-col>
            <el-col>
              <el-button
                type="danger"
                icon="el-icon-minus"
                circle
                v-if="form.xsc.length > 1"
                @click="removeDerive(item)"
              ></el-button>
              <el-button
                type="primary"
                icon="el-icon-plus"
                @click="addDerive"
                circle
              ></el-button>
            </el-col>
          </el-row>
        </div>
      </el-form-item>
      <el-form-item label="时间维度" prop="sjwd">
        <el-select v-model="form.sjwd" placeholder="请选择时间维度">
          <el-option
            v-for="(item, index) in sjdwList"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="计算周期" prop="jszq">
        <el-select v-model="form.jszq" placeholder="请选择计算周期">
          <el-option
            v-for="(item, index) in jszqList"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="精度" prop="jd">
        <el-row type="flex" gutter="10">
          <el-col>
            <el-input
              v-model="form.jd"
              placeholder="仅支持输入整数，数值则代表小数点的位数"
            ></el-input>
          </el-col>
          <el-col>
            <el-checkbox v-model="form.sswr" true-label="1" false-label="0">
              四舍五入
            </el-checkbox>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="归属域">
        <avue-input-tree
          default-expand-all
          v-model="form.sysjy"
          @change="inputThree"
          :props="{
            label: 'name',
            value: 'id'
          }"
          placeholder="请选择归属域"
          :dic="viewGroup"
        ></avue-input-tree>
      </el-form-item>
      <el-form-item label="设置阈值">
        <template #label>
          <span
            style="
              display: flex;
              align-items: center;
              justify-content: flex-end;
            "
          >
            设置阈值
            <el-tooltip class="item" effect="dark" content="" placement="top">
              <div slot="content">
                1. 如果不填写最小值，仅填写最大值，则表示小于等于最大值；
                <br />
                2. 如果不填写最大值，仅填写最小值，则表示大于等于最小值。
              </div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
          </span>
        </template>
        <div style="display: flex">
          <el-form-item prop="tvmin" style="margin-bottom: 0">
            <el-input
              v-model="form.tvmin"
              placeholder="请输入最小值"
              style="width: 122px"
            ></el-input>
          </el-form-item>
          -
          <el-form-item style="margin-bottom: 0">
            <el-input
              v-model="form.tvmax"
              style="width: 122px"
              placeholder="请输入最大值"
            ></el-input>
          </el-form-item>
        </div>
      </el-form-item>
      <el-form-item label="单位" prop="jldw">
        <div style="display: flex">
          <el-select
            v-model="form.jldw"
            placeholder="请选择单位"
            :style="{ width: form.jldw === '其他' ? '122px' : '250px' }"
            class="myselect"
          >
            <el-option
              v-for="(item, index) in dwList"
              :key="index"
              :label="item.name"
              :value="item.bm"
            ></el-option>
          </el-select>
          <el-input
            v-if="form.jldw === '其他'"
            v-model="form.diydw"
            style="width: 122px; margin-left: 6px"
            placeholder="请输入单位"
          ></el-input>
        </div>
      </el-form-item>
      <el-form-item label="描述">
        <el-input v-model="form.ms" placeholder="请输入描述"></el-input>
      </el-form-item>
      <!-- <el-form-item label="标签">
        <el-input v-model="form.bq" placeholder="请输入标签"></el-input>
      </el-form-item> -->
      <el-form-item label="标签" prop="bq">
        <el-select
          v-model="form.bq"
          filterable
          multiple
          remote
          allow-create
          default-first-option
          @change="(val) => {
            form.bq = val.filter(item => item.trim() !== '')
            changeTag(val)
          }"
          @remove-tag="removeTag"
          :remote-method="remoteMethod"
          placeholder="请创建或者选择标签"
        >
          <el-option
            v-for="item in formatLabels"
            :key="item.bqmc"
            :label="item.bqmc"
            :value="item.bqmc"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="保存后">
        <el-radio v-model="radio" label="1">继续添加指标</el-radio>
        <el-radio v-model="radio" label="2">返回指标管理</el-radio>
      </el-form-item>

      <el-form-item>
        <el-button @click="$router.go(-1)">取消</el-button>
        <el-button type="primary" @click="submitForm">保存指标</el-button>
      </el-form-item>
    </el-form>
  </DT-View>
</template>

<script>
import options from "../mixins/options"
export default {
  components: {},
  mixins: [options],
  computed: {
    // 当前派生维度
    currentDerive() {
      if (!this.yzzbList.length) return []
      return (
        this.yzzbList.filter(item => item.id === this.form.atomid)[0] || {
          pswd: []
        }
      )
    }
  },
  data() {
    var changeZdmc = async (rule, value, callback) => {
      const { data } =
        await this.$httpBi.indicatorAnagement.checkIndicatorRepeat({
          zbmc: value,
          indCode:""
        })
      if (data) {
        callback(new Error("字段名称已存在,请重新输入"))
      } else {
        callback()
      }
    }
    const changeMin = (rule, value, callback) => {
      console.log(value > this.form.tvmax)
      console.log(value, this.form.tvmax)
      if (this.form.tvmax === "") {
        callback()
      } else if (Number(value) > Number(this.form.tvmax)) {
        callback(new Error("最小值不能大于最大值"))
      } else {
        callback()
      }
    }
    const changeMax = (rule, value, callback) => {
      if (this.form.tvmin === "") {
        callback()
      } else if (Number(value) < Number(this.form.tvmin)) {
        callback(new Error("最大值不能小于最小值"))
      } else {
        callback()
      }
    }
    const changeDw = (rule, value, callback) => {
      if (this.form.jldw === "") {
        callback(new Error("请选择单位"))
      } else if (this.form.jldw === "其他" && this.form.diydw === "") {
        callback(new Error("请自定义单位"))
      } else {
        callback()
      }
    }

    return {
      viewGroup: [{ id: 0, name: "根目录", children: [] }], // 数据域分组
      yzzbList: [],
      dic: [],
      radio: "1",
      form: {
        atomid: "",
        zbmc: "",
        zblx: "派生指标",
        lxbm: "ps",
        zbymc: "",
        jsfs: "",
        sorttype: "asc",
        sortrange: "all",
        sortlimit: "",
        jldw: "",
        diydw: "",
        xsc: [
          {
            adid: "", // getAtomIndicatorList接口返回的pswd中的项目id字段
            atomid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
            tabid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
            wdid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
            wdbm: "", // getAtomIndicatorList接口返回的pswd中的相同字段
            wdzd: "", // getAtomIndicatorList接口返回的zddm字段
            zdmc: "", // getAtomIndicatorList接口返回的zbmc字段
            wdlx: "", // getAtomIndicatorList接口返回的pswd中的相同字段
            sjgs: "", // getAtomIndicatorList接口返回的pswd中的相同字段
            gldm: "", // getAtomIndicatorList接口返回的pswd中的相同字段
            wdzval: [] // 用户选择的维度值
          }
        ],
        tvmin: "",
        tvmax: "",
        sysjy: 0,
        jszq: "",
        jd: 0,
        sswr: "",
        bq: [],
        ms: "",
        cjr: ""
      },
      rules: {
        zbmc: [
          { required: true, message: "请输入指标名称", trigger: "change" },
          { max: 20, message: "最大为20个字符", trigger: "change" },
          { validator: changeZdmc, trigger: "change" }
        ],
        tvmin: { validator: changeMin, trigger: "change" },
        tvmax: { validator: changeMax, trigger: "change" },
        atomid: {
          required: true,
          message: "请选择原子指标",
          trigger: "change"
        },
        jsfs: { required: true, message: "请选择计算方式", trigger: "change" },
        sjwd: { required: true, message: "请选择时间维度", trigger: "change" },
        jszq: { required: true, message: "请选择计算周期", trigger: "change" },
        jd: { required: true, message: "请输入精度", trigger: "change" },
        jldw: {
          required: true,
          validator: changeDw,
          trigger: "change"
        }
      },
      labels: [],
      newTag: "", // 新建标签
      tempTag: "", // 临时存储标签
      pswdOptionsMap: {},
      parentIds: ""
    }
  },
  created() {
    this.getYzList()
    this.getAllViewGroup()
  },
  mounted() {},
  watch: {},
  methods: {
    findParentIds(data, targetId, parentIds = []) {
      parentIds.push(data.id) // 将当前节点的 id 加入父级数组

      if (data.id === targetId) {
        return parentIds
      }

      if (data.children) {
        for (const child of data.children) {
          console.log(child, "child")
          const result = this.findParentIds(child, targetId, [...parentIds])
          if (result) {
            return result
          }
        }
      }

      return null // 如果找不到目标 id
    },

    inputThree({ dic, value }) {
      this.parentIds = this.findParentIds(dic[0], value)
    },
    // 获取原子指标
    async getYzList() {
      const { data } = await this.$httpBi.indicatorAnagement.getYzList({
        zbmc: ""
      })
      this.yzzbList = data
    },
    // 获取所有数据域分组
    async getAllViewGroup() {
      const { data } = await this.$httpBi.indicatorAnagement.getAllViewGroup()
      this.viewGroup[0].children = data
    },
    // 选择派生维度
    async change(id, index) {
      const { data } =
        await this.$httpBi.indicatorAnagement.getDimensionValueById({
          id: id,
          lxbm: "yz"
        })
      this.pswdOptionsMap[id] = data
      console.log(this.pswdOptionsMap)
      const item = { ...this.currentDerive.pswd.find(item => item.id === id) }
      delete item.id
      this.$set(this.form.xsc, index, {
        ...item,
        adid: id,
        wdzval: [""]
      })
      console.log(this.form.xsc)
    },

    // 添加派生维度
    addDerive() {
      if (this.form.xsc.length >= this.currentDerive.pswd.length) {
        return this.$message({
          message: "暂无派生维度，请先选择基础指标",
          type: "warning"
        })
      }
      this.form.xsc.push({
        adid: "", // getAtomIndicatorList接口返回的pswd中的项目id字段
        atomid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        tabid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        wdid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        wdbm: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        wdzd: "", // getAtomIndicatorList接口返回的zddm字段
        zdmc: "", // getAtomIndicatorList接口返回的zbmc字段
        wdlx: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        sjgs: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        gldm: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        wdzval: [] // 用户选择的维度值
      })
    },
    // 删除派生维度
    removeDerive(item) {
      var index = this.form.xsc.indexOf(item)
      if (index !== -1 && this.form.xsc.length > 1) {
        this.form.xsc.splice(index, 1)
      }
    },
    submitForm() {
      console.log(this.form)
      this.$refs.form.validate(async valid => {
        if (valid) {
          const { code } = await this.$httpBi.indicatorAnagement.addPszb([
            {
              ...this.form,
              xsc: this.form.xsc.filter(
                (item, index) => item.adid !== "" || index === 0
              ),
              jldw:
                this.form.jldw === "其他" ? this.form.diydw : this.form.jldw,
              diydw: this.form.jldw === "其他" ? 1 : 0
            }
          ])
          if (code === 200) {
            this.$message({
              message: "添加成功",
              type: "success"
            })
            if (this.radio === "2") {
              this.$router.push({
                path: "/ddsBi/indicatorAnagement",
                query: {
                  code: this.parentIds.join(",")
                }
              })
            } else {
              this.getBaseUnit()
              this.$refs.form.resetFields()

              // 初始化data
              this.form = {
                atomid: "",
                zbmc: "",
                zblx: "",
                zbymc: "",
                jsfs: "",
                xsc: [
                  {
                    adid: ""
                  }
                ],
                sysjy: 0,
                jszq: "",
                jd: 0,
                sswr: "",
                bq: [],
                ms: "",
                cjr: ""
              }
            }
          } else {
            this.$message({
              message: "添加失败",
              type: "warning"
            })
          }
        } else {
          console.log("error submit!!")
          return false
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 20px;
  margin-bottom: 24px;
}

.form {
  width: 500px;
  margin: 0 auto;
}

::v-deep .el-input--small {
  width: 250px;
}

::v-deep .myselect {
  .el-input--small {
    width: 100%;
  }
}

.el-form-item__content {
  display: flex;
}

::v-deep .el-row {
  margin-bottom: 0px;
}
</style>
