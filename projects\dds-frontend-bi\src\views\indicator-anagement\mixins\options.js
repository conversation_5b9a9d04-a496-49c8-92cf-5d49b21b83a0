export default {
  data () {
    return {
      // 计算方式
      jsfsList: [
        {
          label: '求和',
          value: 'sum'
        },
        {
          label: '平均',
          value: 'avg'
        },
        {
          label: '计数',
          value: 'count'
        },
        {
          label: '去重计数',
          value: 'distinct'
        },
        {
          label: '最大值',
          value: 'max'
        },
        {
          label: '最小值',
          value: 'min'
        },
        {
          label: '排序',
          value: 'sort'
        }
      ],
      sortTypeList: [
        {
          label: '升序',
          value: 'asc'
        },
        {
          label: '降序',
          value: 'desc'
        }
      ],
      sortDefineList: [
        {
          label: '全部',
          value: 'all'
        },
        {
          label: 'TOP',
          value: 'top'
        }
      ],
      // 时间维度
      sjdwList: [
        {
          label: '今日',
          value: '1'
        },
        {
          label: '昨日',
          value: '2'
        },
        {
          label: '近7天',
          value: '3'
        },
        {
          label: '近30天',
          value: '4'
        },
        {
          label: '本月',
          value: '5'
        },
        {
          label: '本学期',
          value: '6'
        },
        {
          label: '本学年',
          value: '7'
        },
        {
          label: '今年',
          value: '8'
        },
        {
          label: '近3年',
          value: '9'
        }
      ],
      // 计算周期
      jszqList: [
        {
          label: '按日',
          value: '1'
        },
        {
          label: '按周',
          value: '2'
        },
        {
          label: '按月',
          value: '3'
        },
        {
          label: '按学期',
          value: '4'
        },
        {
          label: '按学年',
          value: '5'
        },
        {
          label: '按年',
          value: '6'
        }
      ],
      sjfcList: [
        {
          label: '数据层',
          value: 1
        },
        {
          label: '集成层',
          value: 2
        },
        {
          label: '访问层',
          value: 3
        },
        {
          label: '汇总层',
          value: 4
        },
        {
          label: '应用层',
          value: 5
        }
      ],
      sjgs: [
        {
          label: '数值(0.5)',
          value: 0
        },
        {
          label: '百分比(50%)',
          value: 1
        },
        {
          label: '比值(1:2)',
          value: 2
        }
      ],
      zeroList: [
        {
          label: '6:00监测',
          value: '06:00:00'
        },
        {
          label: '12:00监测',
          value: '12:00:00'
        },
        {
          label: '18:00监测',
          value: '18:00:00'
        },
        {
          label: '关闭监测',
          value: '0'
        }
      ],
      dwList: [],
      labels: [],
      newTag: '', // 新建标签
      tempTag: '', // 临时存储标签,
      viewGroup: [{ id: '0', name: '根目录', children: [], parentId: null }], // 数据域分组
      cascaderProps: {
        label: 'name',
        checkStrictly: true,
        value: 'id',
        lazy: true,
        leaf: 'leaf',
        lazyLoad: this.lazyLoad // 懒加载方法
      }
    }
  },
  directives: {
    loadMore: {
      bind: function (el, binding) {
        el.addEventListener('scroll', function () {
          const threshold = 2 // 容差像素
          const isBottom =
            el.scrollHeight - el.scrollTop - el.clientHeight <= threshold
          if (isBottom) {
            binding.value()
          }
        })
      }
    }
  },
  computed: {
    formatLabels () {
      return this.labels.filter(item => item.bqmc.includes(this.newTag))
    }
  },
  created () {
    // this.getAllViewGroup()
    // this.getLabelSelectList()
    // this.getBaseUnit()
  },
  methods: {
    // 获取所有数据域分组
    async getAllViewGroup () {
      const { data } = await this.$httpBi.indicatorAnagement.getAllViewGroup()
      this.viewGroup[0].children = data
    },
    // 根据 scopeId 查找对应的名称
    getScopeNameById (scopeId) {
      const findName = nodes => {
        for (let node of nodes) {
          if (node.id === scopeId) {
            return node.name
          }
          if (node.children) {
            const name = findName(node.children)
            if (name) return name
          }
        }
        return null
      }

      return findName(this.viewGroup)
    },
    // 如果选中全部 不再选择子集
    isDisabled (val, arr) {
      if (!arr.length) return false
      if (val !== '' && arr.includes('')) {
        return true
      }
      if (val === '' && !arr.includes('')) {
        return true
      }
    },
    async getBaseUnit () {
      const { data } = await this.$httpBi.indicatorAnagement.getBaseUnit()
      this.dwList = data
      data.push({
        bm: '其他',
        name: '其他'
      })
    },
    // 获取所有标签
    async getLabelSelectList () {
      const { data } = await this.$httpBi.indicatorAnagement.getIndicatorTags(
        ''
      )
      this.labels = data
    },
    changeTag (val) {
      const isNewLabel = this.labels.every(item =>
        val.every(element => {
          return item.bqmc === element
        })
      )
      if (!isNewLabel) {
        this.addLabel()
      }
    },
    removeTag () {
      this.newTag = ''
    },
    remoteMethod (tagName) {
      if (tagName && tagName.trim() !== '') {
        this.newTag = tagName
      }
    },
    // 添加新标签
    async addLabel () {
      if (this.newTag && this.newTag.trim() !== '') {
        await this.$httpBi.indicatorAnagement.addIndicatorTags([this.newTag])
        this.tempTag = this.newTag
        this.newTag = ''
        this.getLabelSelectList()
      }
    },
    // 指标类型
    getIndicatorType () {
      return {
        data: [
          {
            label: '全部',
            value: ''
          },
          {
            label: '原子指标',
            value: 'yz'
          },
          {
            label: '派生指标',
            value: 'ps'
          },
          {
            label: '衍生指标',
            value: 'ys'
          },
          {
            label: "SQL指标",
            value: "sq"
          },
          {
            label: "算法指标",
            value: "sf"
          },
        ]
      }
    },
    // 处理下拉框显示状态变化
    handleSelectVisibleChange (visible) {
      if (!visible) {
        this.newTag = ''
        this.changeTag([])
      }
    },
    // 懒加载方法
    async lazyLoad (node, resolve) {
      console.log(node, '//////////////////////')
      if (node.level === 0) {
        const { data } = await this.$httpBi.upms.paramGet(
          '/customerDept/allCustomer',
          {}
        )

        const nodes = data.map(item => ({
          id: item.id,
          customerCode: item.customerCode,
          name: item.name,
          children: [],
          leaf: false,
          disabled: true
        }))
        resolve(nodes)
      } else {
        const { data } = await this.$httpBi.upms.paramGet(
          '/customerDept/customerDeptChildren',
          {
            id: node.data.id,
            customerCode: node.data.customerCode
          }
        )
        let nodes = data.map(item => ({
          id: item.id,
          customerCode: item.customerCode,
          name: item.name,
          children: [],
          leaf: !item.hasChildren
        }))
        resolve(nodes)
      }
    }
  }
}
