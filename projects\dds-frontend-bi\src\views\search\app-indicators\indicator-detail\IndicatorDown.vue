<template>
  <div class="indicator-down">
    <el-form
      :inline="true"
      :model="formInline"
      class="demo-form-inline"
      :disabled="loading"
    >
      <el-row>
        <el-form-item label="下钻维度：">
          <el-select
            v-model="formInline.drillDownDim"
            filterable
            placeholder="请选择"
            value-key="levelCode"
            @change="getDimByLevelCode"
          >
            <el-option
              v-for="item in dimLevel"
              :key="item"
              :label="item.dimAndLevelName"
              :value="item"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="时间范围：">
          <el-date-picker
            v-model="formInline.date"
            type="date"
            placeholder="选择日期"
            value-format="yyyy年M月"
          ></el-date-picker>
        </el-form-item> -->
      </el-row>
      <el-row type="flex">
        <el-form-item label="过滤维度：">
          <el-form-item
            v-for="item in formInline.filterDims"
            :label="item.dimAndLevelName"
            :key="item.dimCol"
          >
            <LevelMultipleSelect
              v-if="item.levelCode && !item.enableClustering"
              style="width: 170px; margin-left: 6px"
              v-model="item.dimValList"
              :level-code="item.levelCode"
              :dim-id="item.id"
              :ind-type="item.intType"
              :ind-code="parent.indCode"
              :props="{
                label: 'value',
                value: 'value'
              }"
              @change="getChartData"
              :disabled="item.levelCode === formInline.drillDownDim.levelCode"
            />
            <!-- 使用聚类 -->
            <ClusterMultipleSelect
              v-if="item.levelCode && item.enableClustering"
              :dim-values.sync="item.wdzval"
              v-model="item.clusterCodes"
              :is-selected-all="item.isSelectedAll"
              :is-selected-all-name="item.isSelectedAllName"
              :level-code="item.levelCode"
              style="width: 205px; margin-left: 6px"
              @change="getChartData"
              :disabled="item.levelCode === formInline.drillDownDim.levelCode"
            />
          </el-form-item>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-plus" @click="openFilterDimDialog">
            添加
          </el-button>
        </el-form-item>
        <el-form-item
          label="图表类型："
          style="margin-left: auto; min-width: 290px"
        >
          <el-select v-model="formInline.chartType" placeholder="请选择">
            <el-option
              v-for="item in chartType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-row>
    </el-form>
    <div class="chart-box" v-loading="loading">
      <ChartColumn
        v-if="formInline.chartType == 'ChartColumn'"
        :chart-data="chartData"
        x-field="dimValue"
        y-field="formatValue"
        series-name=""
        y-axis-name=""
        :bar-width="16"
        show-data-zoom
        :show-num="6"
        :unit="unit"
        ref="ChartColumn"
        height="307px"
        :data-format="parent.indicatorData.dataFormat"
        @clickSeries="onClickSeries"
      />
      <CommonTable
        v-if="formInline.chartType == 'ChartTable'"
        :page.sync="page"
        :table-data="
          chartData.slice(
            (page.currentPage - 1) * page.pageSize,
            page.currentPage * page.pageSize
          )
        "
        :show-selection="false"
        :show-batch-tag="false"
        :loading="loading"
        :table-columns.sync="tableColumn"
        @onload="getTableData"
        ref="CommonTable"
      ></CommonTable>
    </div>

    <FilterDimDialog
      ref="FilterDimDialog"
      :filter-dims.sync="formInline.filterDims"
      :same-dimension="dimLevel"
      :props="{
        label: 'dimAndLevelName',
        value: 'dimCol',
        valueList: 'dimValList'
      }"
    />
  </div>
</template>

<script>
import ChartColumn from '@/components/Charts/ChartColumn'
import CommonTable from '@/components/CommonTable.vue'
import LevelMultipleSelect from '../../../indicator-anagement/components/LevelMultipleSelect.vue'
import ClusterMultipleSelect from '../../../indicator-anagement/components/ClusterMultipleSelect.vue'

import FilterDimDialog from '../../../indicator-anagement/components/FilterDimDialog.vue'

export default {
  name: 'IndicatorDown',
  components: {
    ChartColumn,
    CommonTable,
    FilterDimDialog,
    LevelMultipleSelect,
    ClusterMultipleSelect
  },
  props: {},
  data () {
    return {
      unit: '',
      dimLevel: [],
      formInline: {
        drillDownDim: {
          dimCol: '',
          dimValList: []
        }, // 下钻维度
        filterDims: [],
        chartType: 'ChartColumn'
      },
      loading: false,
      chartType: [
        {
          value: 'ChartColumn',
          label: '柱状图'
        },
        {
          value: 'ChartTable',
          label: '表格'
        }
      ],
      chartData: [],
      page: {
        total: 0,
        currentPage: 1,
        pageSize: 10
      },
      tableColumn: [
        {
          label: '维度',
          prop: 'dimValue',
          visible: true,
          sortable: false
        },
        {
          label: '值',
          prop: 'value',
          visible: true,
          sortable: false
        }
      ]
    }
  },
  inject: ['parent'],
  watch: {
    'parent.activeName': {
      handler () {
        this.resize()
      }
    }
  },

  created () {
    this.unit = this.parent.indicatorData.unit
    // this.loading = true
    this.getDimLevel()
    // this.getChartData()
  },
  mounted () {},
  methods: {
    async getChartData () {
      // this.loading = true
      const { data } = await this.$httpBi.api.paramPost(
        '/indicator/drill/down/getResult',
        {
          indCode: this.parent.indCode,
          indType: this.parent.lxbm,
          drillDownDim: { ...this.formInline.drillDownDim, dimType: 1 },
          filterDims: this.formInline.filterDims.map(item => ({
            ...item,
            dimCol: item.dimCol,
            dimType: 2,
            clusterCodes: item.clusterCodes.join(','),
            dimValList: item.dimValList.map(val => ({
              dimVal: val,
              dimCol: item.dimCol
            }))
          }))
        }
      )
      // 百分比
      if (Number(this.parent.indicatorData.dataFormat) === 1) {
        this.unit = '%'
        this.chartData = data.map(item => ({
          ...item,
          formatValue: item.value.replace('%', '')
        }))
        // 比值
      } else if (Number(this.parent.indicatorData.dataFormat) === 2) {
        this.unit = ''
        this.chartData = data.map(item => ({
          ...item,
          formatValue: item.valueOrigin
        }))
      } else {
        this.chartData = data.map(item => ({
          ...item,
          formatValue: item.value
        }))
      }
      this.page.total = data.length
      this.loading = false
    },
    openFilterDimDialog () {
      this.$refs.FilterDimDialog.open(this.dimLevel)
    },
    onClickSeries (params) {
      console.log(params)
      if (params.data.nextLevelCode) {
        const levelCode = this.formInline.drillDownDim.levelCode
        const idx = this.formInline.filterDims.findIndex(
          item => item.levelCode === levelCode
        )
        if (idx !== -1) {
          // 已有，直接修改
          this.formInline.filterDims[idx].dimValList = [params.data.dimValue]
        } else {
          // 没有，添加
          this.formInline.filterDims.push({
            ...this.formInline.drillDownDim,
            dimValList: [params.data.dimValue]
          })
        }

        const nextLevelItem = this.dimLevel.find(
          item => item.levelCode === params.data.nextLevelCode
        )
        this.getDimByLevelCode(nextLevelItem)
      }
    },
    // 获取指标维度层级
    async getDimLevel () {
      const { code, data } = await this.$httpBi.api.paramGet(
        '/DimManage/getDimLevelByIndCode',
        {
          indCode: this.parent.indCode
        }
      )

      if (code === 200) {
        this.dimLevel = data
        if (data.length > 1) {
          this.formInline.drillDownDim = data[1]
          this.getDimByLevelCode(data[1])
        }
      }
    },
    // 根据levelCode获取维度
    async getDimByLevelCode (levelItem) {
      // 禁用并清空过滤维度中与下钻维度相同的项
      this.formInline.filterDims = this.formInline.filterDims.map(item => {
        if (item.levelCode === levelItem.levelCode) {
          return { ...item, dimValList: [] }
        }
        return item
      })

      let dimValList = []
      if (levelItem.enableClustering) {
        const clusterData = await this.$httpBi.api.paramPostQuery(
          '/DimManage/getDimClusterByLevelCode',
          {
            levelCode: levelItem.levelCode
          }
        )

        await Promise.all(
          clusterData.data.map(async (item, index) => {
            const { data } = await this.$httpBi.api.paramPost(
              '/DimManage/getDimValueByClusterCodes',
              [item.clusterCode]
            )
            this.$set(dimValList, index, {
              dimVal: item.cluName,
              clusterDimValList: data.map(item => ({
                dimVal: item.value,
                dimCol: levelItem.dimCol,
                dimValCode: item.valueCode
              }))
            })
          })
        )
        console.log(dimValList, 'dimValList')
      } else {
        const { data } = await this.$httpBi.api.paramPostQuery(
          '/DimManage/getDimValueByLevelCode',
          {
            levelCode: levelItem.levelCode,
            dimId: levelItem.dimId,
            indType: this.parent.lxbm,
            size: -1,
            page: 1
          }
        )
        dimValList = data.list.map(item => ({
          dimVal: item.value,
          dimValCode: item.valueCode
        }))
      }

      this.formInline.drillDownDim = {
        ...this.formInline.drillDownDim,
        ...levelItem,
        dimValList: dimValList
      }
      this.getChartData()
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-row {
  margin-bottom: 0;
}
.indicator-down {
  width: 100%;
}
.chart-box {
  width: 100%;
}
</style>
