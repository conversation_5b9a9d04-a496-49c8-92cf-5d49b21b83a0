<template>
  <DT-View :inner-style="{ padding: 0 }">
    <div class="title">创建派生指标</div>
    <el-form
      ref="formEl"
      :model="form"
      :rules="rules"
      label-width="120px"
      label-position="top"
      class="form"
      hide-required-asterisk
    >
      <div class="base-info">
        <div class="sub-title">基础信息</div>
        <div class="base-content">
          <div class="base-row">
            <el-form-item label="指标名称" prop="zbmc">
              <el-input
                v-model="form.zbmc"
                placeholder="请输入指标名称，不超过40个汉字"
                clearable
              ></el-input>
            </el-form-item>

            <el-form-item label="所属指标域" prop="sysjy">
              <avue-input-tree
                default-expand-all
                v-model="form.sysjy"
                @change="inputThree"
                :props="{
                  label: 'name',
                  value: 'id'
                }"
                placeholder="请选择指标域"
                :dic="viewGroup"
              ></avue-input-tree>
            </el-form-item>
          </div>
          <div class="base-row">
            <el-form-item label="数据格式" prop="dataFormat">
              <el-select
                v-model="form.dataFormat"
                placeholder="请选择数据格式"
                :popper-append-to-body="false"
              >
                <el-option
                  v-for="item in sjgs"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="单位" prop="jldw" v-if="form.dataFormat == 0">
              <div style="display: flex">
                <el-select
                  v-model="form.jldw"
                  placeholder="请选择单位"
                  :style="{ width: form.jldw === '其他' ? '100px' : '240px' }"
                  class="myselect"
                  :popper-append-to-body="false"
                >
                  <el-option
                    v-for="(item, index) in dwList"
                    :key="index"
                    :label="item.name"
                    :value="item.bm"
                  ></el-option>
                  <el-option label="无单位" :value="null"></el-option>
                </el-select>
                <el-input
                  v-if="form.jldw === '其他'"
                  v-model="form.diydw"
                  style="width: 134px; margin-left: 6px"
                  placeholder="请输入单位"
                ></el-input>
              </div>
            </el-form-item>
          </div>
          <div class="base-row">
            <el-form-item label="精度">
              <el-input
                v-model.number="form.jd"
                placeholder="仅支持输入整数，数值则代表小数点的位数"
                style="width: 240px"
              ></el-input>
              <el-checkbox
                v-model="form.sswr"
                :true-label="1"
                :false-label="0"
                style="margin-left: 20px"
              >
                四舍五入
              </el-checkbox>
            </el-form-item>
          </div>
          <div class="base-row">
            <el-form-item label="标签" prop="bq">
              <el-select
                v-model="form.bq"
                filterable
                multiple
                remote
                allow-create
                default-first-option
                @visible-change="handleSelectVisibleChange"
                @change="(val) => {
                  form.bq = val.filter(item => item.trim() !== '')
                  changeTag(val)
                }"  
                @remove-tag="removeTag"
                :remote-method="remoteMethod"
                placeholder="请创建或者选择标签"
              >
                <el-option
                  v-for="item in formatLabels"
                  :key="item.bqmc"
                  :label="item.bqmc"
                  :value="item.bqmc"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="归属部门">
              <el-cascader
                v-model="form.deptAllCode"
                clearable
                style="width: 100%"
                placeholder="请选择归属部门"
                :props="cascaderProps"
                @change="handleChange"
              ></el-cascader>
            </el-form-item>
          </div>
          <div class="base-row">
            <el-form-item label="描述">
              <el-input v-model="form.ms" placeholder="请输入描述"></el-input>
            </el-form-item>
          </div>
        </div>
      </div>
      <div class="model-info">
        <div class="sub-title">模型信息</div>
        <div class="model-content">
          <div class="model-row">
            <el-form-item label="基础指标" prop="baseIndCode">
              <el-select-v2
                v-model="form.baseIndCode"
                placeholder="选择基础指标"
                filterable
                ref="optionRef"
                clearable
                :popper-append-to-body="false"
                @change="changeAtom"
                :options="yzzbList"
                :props="{
                  label: 'zbmc',
                  value: 'indCode'
                }"
              >
                <template #default="{ item }">
                  <span style="float: left">{{ item.zbmc }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">
                    {{ item.zblx }}
                  </span>
                </template>
              </el-select-v2>
            </el-form-item>
          </div>
          <div class="model-row">
            <el-form-item label="派生维度" style="margin-bottom: 0">
              <div
                class="form-item"
                v-for="(item, index) in form.xsc"
                :key="index"
              >
                <el-row type="flex" gutter="10">
                  <el-col>
                    <el-form-item>
                      <div style="display: flex">
                        <el-select
                          v-model="item.adid"
                          clearable
                          placeholder="请选择派生维度"
                          @change="change($event, index)"
                          @clear="clearDerive"
                          filterable
                          style="width: 205px"
                          :popper-append-to-body="false"
                          class="myselect"
                          :disabled="item.isDisabled"
                        >
                          <el-option
                            v-for="ele in currentDerive.pswd"
                            :disabled="form.xsc.some(e => e.adid === ele.adid)"
                            :key="ele.adid"
                            :label="ele.dimAndLevelName || ele.zdmc"
                            :value="ele.adid"
                          ></el-option>
                        </el-select>
                        <!-- 使用维度维度值 -->
                        <LevelMultipleSelect
                          v-model="item.wdzval"
                          v-if="item.levelCode && !item.enableClustering"
                          :is-selected-all="item.isSelectedAll"
                          :is-selected-all-name="item.isSelectedAllName"
                          :dim-id="item.id"
                          :disabled="item.isDisabled"
                          :ind-type="baseIndType"
                          style="width: 205px; margin-left: 6px"
                          :level-code="item.levelCode"
                        />
                        <!-- 使用聚类 -->
                        <ClusterMultipleSelect
                          v-if="item.levelCode && item.enableClustering"
                          :dim-values.sync="item.wdzval"
                          v-model="item.clusterCodes"
                          :is-selected-all="item.isSelectedAll"
                          :is-selected-all-name="item.isSelectedAllName"
                          :disabled="item.isDisabled"
                          :level-code="item.levelCode"
                          style="width: 205px; margin-left: 6px"
                        />
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-button
                      type="danger"
                      icon="el-icon-minus"
                      circle
                      v-if="form.xsc.length > 1"
                      @click="removeDerive(item)"
                      :disabled="item.isDisabled"
                    ></el-button>
                    <el-button
                      type="primary"
                      icon="el-icon-plus"
                      @click="addDerive"
                      circle
                    ></el-button>
                  </el-col>
                </el-row>
              </div>
            </el-form-item>
          </div>
          <div class="model-row">
            <el-form-item label="可扩展维度">
              <el-select
                v-model="form.extDim"
                placeholder="请选择可扩展维度"
                value-key="adid"
                multiple
                :popper-append-to-body="false"
              >
                <template v-if="this.extendDimensionList.length">
                  <el-option
                    v-for="item in this.extendDimensionList"
                    :key="item.id"
                    :label="item.dimAndLevelName || item.zdmc"
                    :value="item"
                  ></el-option>
                </template>
              </el-select>
            </el-form-item>
          </div>
          <div class="model-row">
            <el-form-item label="计算方式" prop="jsfs">
              <div style="display: flex">
                <el-select
                  v-model="form.jsfs"
                  placeholder="请选择计算方式"
                  :popper-append-to-body="false"
                  :style="{ width: form.jsfs === 'sort' ? '76px' : '240px' }"
                  class="myselect"
                >
                  <el-option
                    v-for="(item, index) in jsfsList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <template v-if="form.jsfs === 'sort'">
                  <el-select
                    style="margin-left: 10px"
                    v-model="form.sorttype"
                    placeholder="排序方式"
                    :style="{ width: '76px' }"
                    class="myselect"
                    :popper-append-to-body="false"
                  >
                    <el-option
                      v-for="(item, index) in sortTypeList"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                  <el-select
                    style="margin-left: 10px"
                    v-model="form.sortrange"
                    v-if="form.jsfs === 'sort'"
                    :style="{ width: '76px' }"
                    class="myselect"
                  >
                    <el-option
                      v-for="(item, index) in sortDefineList"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                  <el-input
                    :style="{ width: '76px' }"
                    style="margin-left: 10px"
                    placeholder="数值"
                    v-if="form.sortrange === 'top'"
                    v-model.number="form.sortlimit"
                    clearable
                  ></el-input>
                </template>
              </div>
            </el-form-item>
            <el-form-item label="计算周期" prop="jszq">
              <el-select
                v-model="form.jszq"
                placeholder="请选择计算周期"
                :popper-append-to-body="false"
              >
                <el-option
                  v-for="(item, index) in jszqList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="model-row">
            <el-form-item label="时间范围">
              <el-select
                v-model="form.sjwd"
                placeholder="请选择时间范围"
                :popper-append-to-body="false"
              >
                <el-option
                  v-for="(item, index) in sjdwList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="0值预警">
              <el-select
                v-model="form.zeroWarnTime"
                placeholder="请选择0值预警"
                :popper-append-to-body="false"
              >
                <el-option
                  v-for="(item, index) in zeroList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="model-row">
            <el-form-item label="设置阈值">
              <template #label>
                <span
                  style="
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                  "
                >
                  设置阈值
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content=""
                    placement="top"
                  >
                    <div slot="content">
                      1. 如果不填写最小值，仅填写最大值，则表示小于等于最大值；
                      <br />
                      2. 如果不填写最大值，仅填写最小值，则表示大于等于最小值。
                    </div>
                    <i class="el-icon-warning-outline"></i>
                  </el-tooltip>
                </span>
              </template>
              <div style="display: flex">
                <el-form-item
                  prop="tvmin"
                  style="margin-bottom: 0; width: 107px"
                >
                  <el-input
                    v-model.number="form.tvmin"
                    placeholder="请输入最小值"
                    style="width: 107px"
                  ></el-input>
                </el-form-item>
                <div style="flex: 0 0 26px; text-align: center">-</div>
                <el-form-item
                  style="margin-bottom: 0; width: 107px"
                  prop="tvmax"
                >
                  <el-input
                    v-model.number="form.tvmax"
                    placeholder="请输入最大值"
                    style="width: 107px"
                  ></el-input>
                </el-form-item>
                <el-checkbox
                  v-model="form.isWarnThreshold"
                  :true-label="1"
                  :false-label="0"
                  style="margin-left: 20px"
                >
                  开启阈值外预警
                </el-checkbox>
              </div>
            </el-form-item>
          </div>
          <div class="model-row">
            <el-form-item label="数据过滤">
              <el-input
                v-model="form.dataFilters"
                placeholder="请输入过滤数值，多个数值间使用&quot;,&quot;号隔断"
                style="width: 240px"
              ></el-input>
              <el-checkbox
                v-model="form.nullFilter"
                :true-label="1"
                :false-label="0"
                style="margin-left: 20px"
              >
                空数值过滤
              </el-checkbox>
            </el-form-item>
          </div>
        </div>
      </div>
      <div class="footer-item">
        <div class="sub-title">保存后</div>
        <el-radio v-model="radio" label="1">继续添加指标</el-radio>
        <el-radio v-model="radio" label="2">返回指标管理</el-radio>
      </div>
      <el-form-item style="margin-left: 12px">
        <el-button @click="$router.go(-1)">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="isSubmitting">
          保存指标
        </el-button>
      </el-form-item>
    </el-form>
  </DT-View>
</template>

<script>
import options from '../mixins/options'
import debounce from 'lodash/debounce'
// 深拷贝
import cloneDeep from 'lodash/cloneDeep'
import LevelMultipleSelect from '../components/LevelMultipleSelect.vue'
import ClusterMultipleSelect from '../components/ClusterMultipleSelect.vue'
export default {
  components: {
    // MultipleSelect,
    LevelMultipleSelect,
    ClusterMultipleSelect
  },
  mixins: [options],
  computed: {
    // 当前派生维度
    // currentDerive() {
    //   if (!this.yzzbList.length)
    //     return {
    //       pswd: []
    //     }
    //   return (
    //     this.yzzbList.filter(
    //       item => item.indCode === this.form.atomIndCode
    //     )[0] || {
    //       pswd: []
    //     }
    //   )
    // }
  },
  data () {
    var changeZdmc = async (rule, value, callback) => {
      const { data } =
        await this.$httpBi.indicatorAnagement.checkIndicatorRepeat({
          zbmc: value,
          indCode: ''
        })
      console.log('请求')
      if (data) {
        callback(new Error('字段名称已存在,请重新输入'))
      } else {
        callback()
      }
    }
    const changeMin = (rule, value, callback) => {
      console.log(value > this.form.tvmax)
      console.log(value, this.form.tvmax)
      if (this.form.tvmax === '' || this.form.tvmax == null || value === '') {
        callback()
      } else if (Number(value) > Number(this.form.tvmax)) {
        console.log(value, 'this.form.tvmax')
        console.log(this.form.tvmax, '/////////')

        callback(new Error('最小值不能大于最大值'))
      } else {
        callback()
      }
    }
    const changeMax = (rule, value, callback) => {
      if (this.form.tvmin === '' || this.form.tvmin == null || value === '') {
        callback()
      } else if (Number(value) < Number(this.form.tvmin)) {
        callback(new Error('最大值不能小于最小值'))
      } else {
        callback()
      }
    }
    const changeDw = (rule, value, callback) => {
      if (this.form.jldw === '') {
        callback(new Error('请选择单位'))
      } else if (this.form.jldw === '其他' && this.form.diydw === '') {
        callback(new Error('请自定义单位'))
      } else {
        callback()
      }
    }

    return {
      baseIndType: 'yz',
      baseIndCode: '',
      cascaderValue: [],
      yzzbList: [],
      dic: [],
      radio: '1',
      form: {
        atomid: '',
        zbmc: '', // 指标名称
        sysjy: '0', // 所属指标域
        dataFormat: 0, // 数据格式
        diydw: '', // 自定义单位 1自定义0单位2无单位单位
        jldw: '', // 单位
        jd: 2, // 精度
        sswr: 1, // 四舍五入
        bq: [], // 标签
        deptName: '', // 归属部门
        ms: '', // 描述
        atomIndCode: '', // 基础指标
        // 派生维度
        xsc: [
          {
            adid: '', // getAtomIndicatorList接口返回的pswd中的项目id字段
            atomid: '', // getAtomIndicatorList接口返回的pswd中的相同字段
            tabid: '', // getAtomIndicatorList接口返回的pswd中的相同字段
            wdid: '', // getAtomIndicatorList接口返回的pswd中的相同字段
            wdbm: '', // getAtomIndicatorList接口返回的pswd中的相同字段
            wdzd: '', // getAtomIndicatorList接口返回的zddm字段
            zdmc: '', // getAtomIndicatorList接口返回的zbmc字段
            wdlx: '', // getAtomIndicatorList接口返回的pswd中的相同字段
            sjgs: '', // getAtomIndicatorList接口返回的pswd中的相同字段
            gldm: '', // getAtomIndicatorList接口返回的pswd中的相同字段
            wdzval: [] // 用户选择的维度值
          }
        ],
        // 扩展维度
        extDim: [],
        jsfs: null, // 计算方式
        jszq: null, // 计算周期
        sorttype: 'asc', // 排序方式
        sortrange: 'all', // 排序范围
        sortlimit: null, // 排序数量
        zeroWarnTime: null, // 0值预警
        isZeroWarn: null, // 是否开启0值预警
        tvmin: null, // 阈值最小值
        tvmax: null, // 阈值最大值
        isWarnThreshold: null, // 是否开启阈值外预警
        dataFilters: null, // 数据过滤
        nullFilter: null, // 是否开启空数值过滤
        zblx: '派生指标',
        lxbm: 'ps',
        zbymc: null,
        cjr: null
      },
      rules: {
        zbmc: [
          { required: true, message: '请输入指标名称', trigger: 'change' },
          { max: 40, message: '最大为40个字符', trigger: 'change' },
          {
            validator: debounce(changeZdmc, 400, { leading: true }),
            trigger: 'change'
          }
        ],
        tvmin: { validator: changeMin, trigger: 'blur' },
        tvmax: { validator: changeMax, trigger: 'blur' },
        sysjy: { required: true, message: '请选择数据域', trigger: 'change' },
        dataFormat: {
          required: true,
          message: '请选择数据格式',
          trigger: 'change'
        },
        baseIndCode: {
          required: true,
          message: '请选择基础指标',
          trigger: 'change'
        },
        jsfs: { required: true, message: '请选择计算方式', trigger: 'change' },
        sjwd: { required: true, message: '请选择时间维度', trigger: 'change' },
        jszq: { required: true, message: '请选择计算周期', trigger: 'change' },
        jd: { required: true, message: '请输入精度', trigger: 'change' },
        jldw: {
          required: true,
          validator: changeDw,
          trigger: 'change'
        }
      },
      labels: [],
      newTag: '', // 新建标签
      tempTag: '', // 临时存储标签
      pswdOptionsMap: {},
      parentIds: '',
      // 可扩展维度
      extendDimensionList: [],
      currentDerive: {
        pswd: []
      },
      isSubmitting: false,
      deriveDimensionError: '' // 新增：派生维度校验错误提示
    }
  },
  created () {
    this.getLabelSelectList()
    this.getBaseUnit()
    this.getAllViewGroup()
    this.getYzList()
  },
  mounted () {},
  watch: {},
  methods: {
    // 选择原子指标获取当前指标派生维度
    async changeAtom (val) {
      if (!val) return
      const lxbm = val.slice(0, 2)
      this.baseIndCode = val

      this.baseIndType = lxbm
      if (lxbm === 'yz') {
        const { data } =
          await this.$httpBi.indicatorAnagement.getAtomIndicatorInfo({
            indCode: val
          })
        this.currentDerive = data
        this.form.temp_atomIndCode = val
        this.$nextTick(() => {
          this.form.extDim = cloneDeep(data.pswd)
          this.extendDimensionList = cloneDeep(this.currentDerive.pswd) || []

          this.form.xsc = [
            {
              adid: '', // getAtomIndicatorList接口返回的pswd中的项目id字段
              atomid: '', // getAtomIndicatorList接口返回的pswd中的相同字段
              tabid: '', // getAtomIndicatorList接口返回的pswd中的相同字段
              wdid: '', // getAtomIndicatorList接口返回的pswd中的相同字段
              wdbm: '', // getAtomIndicatorList接口返回的pswd中的相同字段
              wdzd: '', // getAtomIndicatorList接口返回的zddm字段
              zdmc: '', // getAtomIndicatorList接口返回的zbmc字段
              wdlx: '', // getAtomIndicatorList接口返回的pswd中的相同字段
              sjgs: '', // getAtomIndicatorList接口返回的pswd中的相同字段
              gldm: '', // getAtomIndicatorList接口返回的pswd中的相同字段
              wdzval: [] // 用户选择的维度值
            }
          ]
        })
      }
      if (lxbm === 'ps') {
        const { data } =
          await this.$httpBi.indicatorAnagement.getDeriveIndicatorInfo({
            indCode: val
          })
        this.currentDerive = {
          ...data,
          pswd: [...data.allDim]
        }
        this.form.temp_atomIndCode = data.atomIndCode
        this.$nextTick(() => {
          this.extendDimensionList = cloneDeep(data.allDim) || []

          this.form.extDim = cloneDeep(data.extDim)
          this.form.xsc = data.xsc.length
            ? data.xsc.map(item => {
                return {
                  ...item,
                  wdzval: item.wdzval?.length ? item.wdzval : [],
                  isDisabled: true
                }
              })
            : [
                {
                  adid: '', // getAtomIndicatorList接口返回的pswd中的项目id字段
                  atomid: '', // getAtomIndicatorList接口返回的pswd中的相同字段
                  tabid: '', // getAtomIndicatorList接口返回的pswd中的相同字段
                  wdid: '', // getAtomIndicatorList接口返回的pswd中的相同字段
                  wdbm: '', // getAtomIndicatorList接口返回的pswd中的相同字段
                  wdzd: '', // getAtomIndicatorList接口返回的zddm字段
                  zdmc: '', // getAtomIndicatorList接口返回的zbmc字段
                  wdlx: '', // getAtomIndicatorList接口返回的pswd中的相同字段
                  sjgs: '', // getAtomIndicatorList接口返回的pswd中的相同字段
                  gldm: '', // getAtomIndicatorList接口返回的pswd中的相同字段
                  wdzval: [] // 用户选择的维度值
                }
              ]

          this.extendDimensionList = this.currentDerive.pswd.filter(
            item => !this.form.xsc.some(e => e.adid === item.adid)
          )

          const allDim = cloneDeep(this.extendDimensionList) || []
          const extDim = cloneDeep(data.extDim) || []
          const extDimIds = extDim.map(item => item.adid)
          const extDimList = extDimIds
            .map(adid => allDim.find(item => item.adid === adid))
            .filter(Boolean)
          const restList = allDim.filter(item => !extDimIds.includes(item.adid))
          this.extendDimensionList = [...restList, ...extDimList]
        })
      }
    },

    findParentIds (data, targetId, parentIds = []) {
      parentIds.push(data.id) // 将当前节点的 id 加入父级数组

      if (data.id === targetId) {
        return parentIds
      }

      if (data.children) {
        for (const child of data.children) {
          console.log(child, 'child')
          const result = this.findParentIds(child, targetId, [...parentIds])
          if (result) {
            return result
          }
        }
      }

      return null // 如果找不到目标 id
    },

    inputThree ({ dic, value }) {
      this.parentIds = this.findParentIds(dic[0], value)
    },
    // 获取原子指标
    async getYzList () {
      const { data } = await this.$httpBi.indicatorAnagement.getYzList({
        zbmc: ''
      })
      this.yzzbList = data
    },
    // 获取所有数据域分组
    async getAllViewGroup () {
      const { data } = await this.$httpBi.indicatorAnagement.getAllViewGroup()
      this.viewGroup[0].children = data
      this.$nextTick(() => {
        this.form.sysjy = Number(this.$route.query.sysjy) || 999
      })
    },
    // 删除
    clearDerive (val) {
      console.log(val, 'val')
      this.extendDimensionList = this.currentDerive.pswd.filter(
        item => !this.form.xsc.some(e => e.adid === item.adid)
      )
      this.form.extDim = this.extendDimensionList
    },
    // 选择派生维度
    async change (adid, index) {
      console.log(adid, 'adid')
      console.log(this.currentDerive, 'this.currentDerive')
      if (!adid) return
      const item = {
        ...this.currentDerive.pswd.find(item => item.adid === adid)
      }
      // console.log(item,'item')
      console.log(index, 'index')

      // const { data } =
      //   await this.$httpBi.indicatorAnagement.getDimensionValueById({
      //     wdzd: item.wdzd,
      //     tabid: this.currentDerive.tabid
      //   })
      // this.pswdOptionsMap[adid] = data
      // console.log(this.pswdOptionsMap)
      // console.log(item, "item")

      delete item.adid
      this.$set(this.form.xsc, index, {
        ...item,
        adid: adid,
        wdzval: [],
        clusterCodes: []
      })
      this.extendDimensionList = this.currentDerive.pswd.filter(
        item => !this.form.xsc.some(e => e.adid === item.adid)
      )
      // this.form.extDim = this.extendDimensionList
      this.form.extDim = this.extendDimensionList
    },

    // 添加派生维度
    addDerive () {
      if (this.form.xsc.length >= this.currentDerive.pswd.length) {
        return this.$message({
          message: '暂无派生维度，请先选择基础指标',
          type: 'warning'
        })
      }
      this.form.xsc.push({
        adid: '', // getAtomIndicatorList接口返回的pswd中的项目id字段
        atomid: '', // getAtomIndicatorList接口返回的pswd中的相同字段
        tabid: '', // getAtomIndicatorList接口返回的pswd中的相同字段
        wdid: '', // getAtomIndicatorList接口返回的pswd中的相同字段
        wdbm: '', // getAtomIndicatorList接口返回的pswd中的相同字段
        wdzd: '', // getAtomIndicatorList接口返回的zddm字段
        zdmc: '', // getAtomIndicatorList接口返回的zbmc字段
        wdlx: '', // getAtomIndicatorList接口返回的pswd中的相同字段
        sjgs: '', // getAtomIndicatorList接口返回的pswd中的相同字段
        gldm: '', // getAtomIndicatorList接口返回的pswd中的相同字段
        wdzval: [] // 用户选择的维度值
      })
    },
    // 删除派生维度
    removeDerive (item) {
      const index = this.form.xsc.indexOf(item)
      if (index !== -1 && this.form.xsc.length > 1) {
        // 删除派生维度
        this.form.xsc.splice(index, 1)

        // 重新计算 extendDimensionList
        this.extendDimensionList = this.currentDerive.pswd.filter(
          ele => !this.form.xsc.some(e => e.adid === ele.adid)
        )

        // 同步更新扩展维度
        this.form.extDim = this.extendDimensionList
      }
    },
    validateDeriveDimension () {
      // 校验派生维度：adid有值时wdzval不能为空
      for (let i = 0; i < this.form.xsc.length; i++) {
        const item = this.form.xsc[i]
        console.log(item, 'item')
        if (item.adid && (!item.wdzval || item.wdzval.length === 0)) {
          this.deriveDimensionError = `第${i + 1}个${
            item.zdmc
          }派生维度未选择维度值`
          return false
        }
      }
      this.deriveDimensionError = ''
      return true
    },
    submitForm: debounce(
      function () {
        if (this.isSubmitting) return // 如果正在提交，直接返回
        this.isSubmitting = true // 锁定提交
        // 新增：派生维度校验
        if (!this.validateDeriveDimension()) {
          this.$message.error(this.deriveDimensionError)
          return (this.isSubmitting = false)
        }
        const jldwMapping = {
          其他: 1,
          无单位: 2
        }

        try {
          this.$refs.formEl.validate(async valid => {
            if (valid) {
              console.log(valid, 'vvvvvvvvvvvvvvvvvv')
              const { code } = await this.$httpBi.indicatorAnagement.addPszb([
                {
                  ...this.form,

                  extDim: this.form.extDim.map(item => ({
                    ...item,
                    zdlx: item.wdlx
                  })),
                  atomIndCode:
                    this.form.temp_atomIndCode ?? this.form.atomIndCode,
                  baseIndCode: this.baseIndCode,
                  baseIndType: this.baseIndType,
                  xsc: this.form.xsc
                    .filter(item => item.adid !== '')
                    .map(item => ({
                      ...item,
                      zdlx: item.wdlx
                    })),
                  jldw:
                    this.form.dataFormat !== 0
                      ? ''
                      : this.form.jldw === '其他'
                      ? this.form.diydw
                      : this.form.jldw,
                  diydw: jldwMapping[this.form.jldw] || 0,
                  isZeroWarn: this.form.zeroWarnTime === '0' ? 1 : 0
                }
              ])
              if (code === 200) {
                this.$message({
                  message: '添加成功',
                  type: 'success'
                })
                if (this.radio === '2') {
                  this.$router.push({
                    path: '/ddsBi/indicatorAnagement',
                    query: {}
                  })
                } else {
                  this.getBaseUnit()
                  this.getYzList()
                  this.$refs.formEl.resetFields()

                  // 初始化data
                  this.form = {
                    atomid: '',
                    zbmc: '',
                    zblx: '',
                    zbymc: '',
                    jsfs: '',
                    xsc: [
                      {
                        adid: ''
                      }
                    ],
                    sysjy: Number(this.$route.query.sysjy) || 999,
                    dataFormat: 0, // 数据格式
                    jszq: '',
                    jd: 2,
                    sswr: 1,
                    bq: [],
                    ms: '',
                    cjr: ''
                  }
                }
              } else {
                this.$message({
                  message: '添加失败',
                  type: 'warning'
                })
              }
              this.isSubmitting = false
            } else {
              console.log('error submit!!')
              this.isSubmitting = false

              return false
            }
          })
        } catch (error) {
          console.error('验证失败或提交出错:', error)
        } finally {
          // this.isSubmitting = false // 解锁提交
        }
      },
      400,
      { leading: true }
    )
  }
}
</script>

<style scoped lang="scss">
.title {
  width: 100%;
  padding-left: 20px;
  box-sizing: border-box;
  height: 56px;
  line-height: 56px;
  background: #ffffff;
  border-bottom: 1px solid #ebede0;

  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #323233;
  text-align: left;
  font-style: normal;
}
.base-info {
  margin-top: 24px;
  .sub-title {
    position: relative;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #323233;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    padding-left: 12px;
    margin-bottom: 20px;

    &::after {
      position: absolute;
      left: 0;
      top: 2px;
      content: '';
      width: 4px;
      height: 12px;
      background: #1563ff;
      border-radius: 1px;
    }
  }
  .base-content {
    position: relative;
    padding-left: 12px;
    width: 500px;
    &::after {
      position: absolute;
      bottom: 0;
      left: 12px;
      content: '';
      width: 500px;
      height: 1px;
      background: #ebede0;
    }
    .base-row {
      width: 500px;
      display: flex;
      grid-gap: 20px;
      justify-content: space-between;
      .el-form-item {
        width: 100%;
      }
    }
  }
}
.model-info {
  margin-top: 24px;
  .sub-title {
    position: relative;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #323233;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    padding-left: 12px;
    margin-bottom: 20px;

    &::after {
      position: absolute;
      left: 0;
      top: 2px;
      content: '';
      width: 4px;
      height: 12px;
      background: #1563ff;
      border-radius: 1px;
    }
  }
  .model-content {
    position: relative;
    padding-left: 12px;
    width: 500px;
    &::after {
      position: absolute;
      bottom: 0;
      left: 12px;
      content: '';
      width: 500px;
      height: 1px;
      background: #ebede0;
    }
    .model-row {
      width: 500px;
      display: flex;
      grid-gap: 20px;
      justify-content: space-between;
      .el-form-item {
        width: 100%;
      }
    }
  }
}
.footer-item {
  display: flex;
  margin: 24px 0 20px;
  padding-left: 12px;
  box-sizing: border-box;
  .sub-title {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    line-height: 14px;
    color: rgba(0, 0, 0, 0.88);
    text-align: left;
    font-style: normal;
    margin-right: 24px;
  }
}

.form {
  width: 512px;
  margin: 0 auto;
  padding-bottom: 20px;
}
::v-deep .el-form--label-top .el-form-item__label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  color: rgba(0, 0, 0, 0.88);
  text-align: left;
  font-style: normal;
}
.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 20px;
}
.el-select {
  width: 100%;
}
::v-deep .el-input--small {
  width: 100%;
}

::v-deep .is-required .el-form-item__label::after {
  content: '*';
  color: #ff0000;
  margin-left: 4px;
}

::v-deep .myselect {
  .el-input--small {
    width: 100%;
  }
}

.el-form-item__content {
  display: flex;
  line-height: 32px;
}

::v-deep .el-row {
  margin-bottom: 0px;
}
</style>
