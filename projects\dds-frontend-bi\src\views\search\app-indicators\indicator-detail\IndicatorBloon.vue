<template>
  <div class="wrap">
    <!-- 健康度 -->
    <div class="health">
      血缘健康度：
      <span
        :class="{
          'health-good': Number(health) > 50,
          'health-bad': Number(health) <= 50
        }"
      >
        {{ health }}%
      </span>
    </div>
    <div id="container" style="width: 100%; height: 800px"></div>
    <!-- 添加放大缩小按钮 -->
    <!-- <div class="zoom-controls">
      <div class="zoom-out" @click="zoomOut"></div>
      <div class="zoom-value">{{ (currentZoom * 100).toFixed(0) }}%</div>
      <div class="zoom-in" @click="zoomIn"></div>
    </div> -->
  </div>
</template>

<script>
import { getTextWidth } from "@/utils/index"

import G6 from "@antv/g6"
export default {
  name: "IndicatorBloon",
  components: {
    // 组件注册
  },
  props: {
    // 组件属性
  },
  data() {
    return {
      graph: null,
      zoomStep: 0.1,
      currentZoom: 1,
      errorIcon: require("@/assets/images/error.png"),
      successIcon: require("@/assets/images/success.png"),
      currentNode: null,
      nodes: []
    }
  },
  computed: {
    health() {
      if (!this.nodes.length) return "0"
      const healthyNodes = this.nodes.filter(node => node.currentStatus).length
      return ((healthyNodes / this.nodes.length) * 100).toFixed(0)
    }
  },
  created() {
    this.getData()

    // 创建时的逻辑
  },
  mounted() {},

  inject: ["parent"],
  watch: {},

  methods: {
    initGraph(_nodes, links) {
      const data = {
        nodes: _nodes,
        edges: links
      }
      const container = document.getElementById("container")
      const width = container.scrollWidth
      const height = container.scrollHeight || 500
      this.graph = new G6.Graph({
        container: "container",
        width,
        height,
        autoFit: "view",
        fitView: true,

        modes: {
          default: ["drag-canvas", "zoom-canvas"]
        },
        layout: {
          type: "dagre",
          rankdir: "LR",
          nodesep: 20,
          nodesepFunc: () => 2,
          ranksepFunc: () => 5
        },
        defaultNode: {
          size: [150, 48],
          type: "modelRect",
          style: {
            lineWidth: 0,
            radius: 8,
            stroke: "#5B8FF9",
            fill: "#FFFFFF",
            shadowColor: "rgba(14,49,161,0.12)",
            shadowBlur: 12,
            shadowOffsetX: 0,
            shadowOffsetY: 0
          },
          labelCfg: {
            position: "center",
            offset: 10,
            style: {
              fill: "#323233",
              fontSize: 16,
              fontFamily: "PingFang SC"
              //
            }
          }
        },
        defaultEdge: {
          type: "polyline",
          size: 1,
          color: "#507AFF",
          style: {
            radius: 5
            // endArrow: {
            //   path: "M 0,0 L 8,4 L 8,-4 Z",
            //   fill: "#e2e2e2"
            // }
          }
        },
        behaviors: ["drag-canvas", "zoom-canvas"]
      })
      this.graph.data(data)

      this.graph.render()
      this.graph.zoom(1)

      this.graph.on("node:click", evt => {
        console.log(evt)
        // 是否是指标
        let indTypes = ["yz", "ps", "ys", "sq"]

        if (!indTypes.includes(evt.item._cfg.id.slice(0, 2))) return
        const routeUrl = this.$router.resolve({
          path: `/ddsBi/appDetail`,
          query: {
            indCode: evt.item._cfg.id,
            lxbm: evt.item._cfg.id.slice(0, 2)
          }
        })
        window.open(routeUrl.href, "_blank")
        // const item = evt.item; // 被操作的节点 item
        // const target = evt.target; // 被操作的具体图形
        // ...
      })

      // 由于 window.addPanel 未定义，这里注释掉相关代码
      // window.addPanel((gui) => {
      //   const config = { layout: 'default' };
      //   const layouts = {
      //     default: { type: 'antv-dagre', nodesep: 100, ranksep: 70, controlPoints: true },
      //     LR: { type: 'antv-dagre', rankdir: 'LR', align: 'DL', nodesep: 50, ranksep: 70, controlPoints: true },
      //     'LR&UL': { type: 'antv-dagre', rankdir: 'LR', align: 'UL', controlPoints: true, nodesep: 50, ranksep: 70 }
      //   };

      //   gui.add(config, 'layout', Object.keys(layouts)).onChange(async (layout) => {
      //     this.graph.setLayout(layouts[layout]);
      //     await this.graph.layout();
      //     this.graph.fitCenter();
      //   });
      // });
    },
    zoomOut() {
      if (this.currentZoom > 0.1) {
        this.currentZoom -= this.zoomStep
        this.graph.zoom(this.currentZoom)
      }
    },
    zoomIn() {
      if (this.currentZoom < 2) {
        this.currentZoom += this.zoomStep
        this.graph.zoom(this.currentZoom)
      }
    },
    async getData() {
      const { data } = await this.$httpBi.api.paramGet("/indicator/lineage/", {
        indCode: this.parent.indCode,
        indType: this.parent.lxbm
      })

      this.nodes = data.nodes
      let nodes = data.nodes.map(item => {
        if (item.currentCode === this.parent.indCode) {
          return {
            id: item.currentCode,
            label: item.currentName,
            type: "rect",
            size: [this.getTextWidth(item.currentName) + 50, 48],
            style: {
              fill: "#507AFF",
              shadowColor: "rgba(255,192,0,0.12)",
              shadowBlur: 12,
              shadowOffsetX: 0,
              shadowOffsetY: 0
            },
            labelCfg: {
              position: "center",
              style: {
                fill: "#fff",
                fontSize: 16,
                fontFamily: "PingFang SC"
                //
              }
            },
            stateIcon: {
              // whether to show the icon
              show: false,
              x: 0,
              y: 0,
              // the image url for the icon, string type
              img: this.successIcon,
              width: 16,
              height: 16,
              // adjust hte offset along x-axis for the icon
              offset: -5
            },
            preRect: {
              show: false
            },
            logoIcon: {
              show: false
            }
          }
        }
        return {
          label: item.currentName,
          id: item.currentCode,
          size: [this.getTextWidth(item.currentName) + 50, 48],

          stateIcon: {
            // whether to show the icon
            show: true,
            x: 0,
            y: 0,
            // the image url for the icon, string type
            img: item.currentStatus ? this.successIcon : this.errorIcon,
            width: 16,
            height: 16,
            // adjust hte offset along x-axis for the icon
            offset: -5
          },

          preRect: {
            show: false
          },
          logoIcon: {
            show: false
          }
        }
      })
      let links = data.links.map(item => ({
        source: item.currentCode,
        target: item.targetCode
      }))
      console.log(nodes, "nodes")
      console.log(links, "links")
      this.initGraph(nodes, links)
    },

    getTextWidth
  }
}
</script>

<style scoped lang="scss">
.wrap {
  position: relative;
  width: 100%;
  height: 800px;
  background: #f4f7ff;
  border-radius: 6px;
  box-sizing: border-box;
}

.health {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 130px;
  height: 30px;
  background: #ffffff;
  border-radius: 8px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #222222;
  line-height: 30px;
  text-align: center;
  .health-good {
    color: #00cc88;
  }
  .health-bad {
    color: #ff4d4f;
  }
}
.zoom-controls {
  position: absolute;
  bottom: 40px;
  right: 40px;
  z-index: 10;
  width: 150px;
  height: 36px;
  background: #ffffff;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  box-sizing: border-box;

  .zoom-in {
    width: 16px;
    height: 16px;
    background: url("~@/assets/images/add.png") no-repeat center;
    background-size: cover;
    cursor: pointer;
  }
  .zoom-value {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #222222;
    line-height: 12px;
    text-align: right;
    font-style: normal;
  }
  .zoom-out {
    width: 16px;
    height: 16px;
    background: url("~@/assets/images/zoom-out.png") no-repeat center;
    background-size: cover;
    cursor: pointer;
  }
}
/* 样式 */
</style>
