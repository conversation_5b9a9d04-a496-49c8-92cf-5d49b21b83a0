<template>
  <div class="com__box px-3 py-2 flex">
    {{ loadingText }}
    <div class="loading">
      <div></div>
      <div></div>
      <div></div>
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  props: {
    loadingText:{
      type: String,
      default: "正在为您连接指标库"
    }
  },
 
}

</script>

<style scoped>
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  margin-left: 10px;
}

.loading > div {
  width: 5px;
  height: 5px;
  background-color: #666;
  border-radius: 50%;
  animation: thinking-animation 1.4s infinite ease-in-out;
}

.loading > div:nth-child(1) {
  animation-delay: 0s;
}

.loading > div:nth-child(2) {
  animation-delay: 0.2s;
}

.loading > div:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes thinking-animation {
  0%, 80%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  40% {
    opacity: 1;
    transform: scale(1.2);
  }
}
</style>
