<template>
  <DT-View
    :inner-style="{
      padding: 0,
      position: 'relative',
      height: '100%'
    }"
    class="container-wrapper"
  >
    <div class="create-head">
      <div class="create-text">创建工具类算法</div>
      <div class="steps">
        <div class="step-item active">
          <i class="el-icon-edit"></i>
          Python
        </div>
        <!-- <div class="line" :class="{ active: activeId == 2 }"></div>
        <div class="step-item" :class="{ active: activeId == 2 }">
          <i class="el-icon-data-line"></i>
          指标与维度
        </div> -->
      </div>
      <el-button
        v-if="mode === 'edit'"
        plain
        size="mini"
        type="primary"
        style="margin-left: auto"
        @click="editHistory"
      >
        编辑历史
      </el-button>
    </div>
    <div class="editor-content" v-if="activeId === 1">
      <div class="left">
        <el-form label-width="75px" label-position="top">
          <el-form-item prop="sourceId" label="数据源:">
            <el-select
              @change="sourceDb"
              v-model="form.sourceId"
              placeholder="请选择数据源"
              style="width: 100%"
              disabled
            >
              <el-option
                v-for="item in sourcesList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="数据库表:">
            <el-tree
              :props="defaultProps"
              class="tree-db"
              :load="loadNode"
              :data="dbs"
              node-key="id"
              @node-click="handleNodeClick"
              lazy
            >
              <span class="custom-tree-node-alg" slot-scope="{ node }">
                <span v-show="node.data.id == 'load-more-id'">
                  <span class="tree-node-label">
                    <el-link>{{ node.data.label }}</el-link>
                  </span>
                </span>
                <template
                  v-if="node.level == 1 && node.data.id != 'load-more-id'"
                >
                  <span>
                    <i class="el-icon-s-grid" />
                    {{ node.data.id }}
                    <br /><span class="table-name">{{ node.data.c }}</span>
                  </span>
                </template>
                <template
                  v-if="node.level == 2 && node.data.id != 'load-more-id'"
                >
                  <div
                    style="
                      display: flex;
                      justify-content: space-between;
                      align-items: center;
                      width: 100%;
                      height: 100%;
                      line-height: 100%;
                    "
                  >
                    {{ node.data.id
                    }}<span style="padding-right: 18px">{{
                      node.data.bt || '-'
                    }}</span>
                  </div>
                </template>
              </span>
            </el-tree>
          </el-form-item>
        </el-form>
      </div>
      <div class="right">
        <div class="editor-pane">
          <MonacoEditor
            ref="MonacoEditor"
            :init-value.sync="form.pyScript"
            :read-only="mode === 'view'"
            :hint-data="hintData"
            height="100%"
            language="python"
          />
        </div>
        <div class="config-pane">
          <div class="config-content">
            <!-- 算法基本信息 -->
            <div class="form-section">
              <h3>
                算法属性
                <el-button
                  plain
                  size="mini"
                  type="primary"
                  :disabled="mode === 'view'"
                  @click="openAlgorithmAttr"
                >
                  编辑
                </el-button>
              </h3>
              <div class="attrs">
                <div class="attr-item">
                  <div class="label">算法名称：</div>
                  <div class="value">{{ form.algorithmName }}</div>
                </div>
                <div class="attr-item">
                  <div class="label">算法文件名称：</div>
                  <div class="value">{{ form.algorithmFileName }}</div>
                </div>
                <div class="attr-item">
                  <div class="label">支持环境：</div>
                  <div class="value" v-if="form.environmentCode.length">
                    <span v-for="code in form.environmentCode" :key="code">
                      {{ supportEnv.find(e => e.value == code).label }}
                    </span>
                  </div>
                </div>
                <div class="attr-item">
                  <div class="label">算法介绍：</div>
                  <div class="value">{{ form.introduction }}</div>
                </div>
              </div>
            </div>
            <div class="form-section">
              <h3>算法说明</h3>
              <div class="params">
                <div class="params-item">
                  <div class="label">输入参数</div>
                  <el-button
                    plain
                    size="mini"
                    type="primary"
                    icon="el-icon-plus"
                    :disabled="mode === 'view'"
                    @click="addParam"
                  ></el-button>
                </div>
                <div
                  class="params-item"
                  v-for="(item, index) in form.algorithmParamList"
                  :key="index"
                >
                  <div class="label">{{ item.paramName }}</div>
                  <el-button type="text" @click="openParamDialog(index)">
                    编辑
                  </el-button>
                </div>
              </div>
            </div>
            <div class="form-section">
              <h3>主函数</h3>
              <div class="explain">
                <el-input
                  :disabled="mode === 'view'"
                  placeholder="请输入主函数"
                  v-model="mainMethodName"
                ></el-input>
              </div>
            </div>
            <div class="form-section">
              <h3>输出说明</h3>
              <div class="explain">
                <el-input
                  :disabled="mode === 'view'"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入输出说明"
                  v-model="form.outputDescription"
                ></el-input>
              </div>
            </div>
          </div>
        </div>

        <div class="footer-btn">
          <el-button @click="$router.go(-1)">取消</el-button>
          <el-button
            type="success"
            :disabled="!isPublish"
            @click="handleTryCalc"
          >
            试计算
          </el-button>
          <el-button
            type="primary"
            :disabled="!isPublish"
            @click="handlePublish"
          >
            发布
          </el-button>
        </div>
      </div>
    </div>

    <el-dialog
      title="试运行"
      :visible.sync="dialogVisible"
      width="750px"
      :before-close="handleDone"
    >
      <el-form
        v-if="dialogStep == 1"
        :model="ruleForm"
        status-icon
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <!-- 动态生成参数输入项 -->
        <template v-for="param in this.form.algorithmParamList">
          <!-- 字符串类型 - 文本域 -->
          <el-form-item
            v-if="param.paramType === 'String'"
            :label="param.paramName"
            :prop="param.paramName"
            :key="'string-' + param.id"
          >
            <el-input
              type="textarea"
              :rows="8"
              placeholder="请输入"
              v-model="ruleForm[param.paramName]"
            >
            </el-input>
          </el-form-item>

          <!-- 数字类型 - 输入框 -->
          <el-form-item
            v-else-if="param.paramType === 'Number'"
            :label="param.paramName"
            :prop="param.paramName"
            :key="'number-' + param.id"
          >
            <el-input
              type="text"
              placeholder="请输入"
              v-model.number="ruleForm[param.paramName]"
            >
            </el-input>
          </el-form-item>

          <!-- 布尔类型 - 单选按钮 -->
          <el-form-item
            v-else-if="param.paramType === 'Boolean'"
            :label="param.paramName"
            :prop="param.paramName"
            :key="'boolean-' + param.id"
          >
            <el-input
              type="text"
              placeholder="请输入"
              v-model="ruleForm[param.paramName]"
            >
            </el-input>
          </el-form-item>
        </template>
      </el-form>
      <div class="inner-box" v-if="dialogStep == 2">
        <div class="icon"></div>
        <div class="right">
          <div class="text">运行中...</div>
          <!-- <div class="progress"></div> -->
          <el-progress
            :show-text="false"
            :percentage="uploadPercentage"
          ></el-progress>
        </div>
      </div>
      <div class="step3-box" v-if="dialogStep == 3">
        <div v-if="tryCalculateResList.length > 0">
          <el-select
            v-model="tryCalculateResVal"
            @change="handleEnvironmentChange"
            placeholder="请选择支持环境"
            style="width: 100%"
          >
            <el-option
              v-for="item in tryCalculateResList"
              :key="item.value"
              :label="
                item.status === 'success'
                  ? item.label + '：成功'
                  : item.label + '：失败'
              "
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <p class="label-text">试运行完成，执行时长：{{ executionTime }}s</p>
        <div v-if="selectedEnvironmentError" class="error-message">
          <p class="error-text">错误信息：{{ selectedEnvironmentError }}</p>
        </div>
        <div v-else>
          <p>输出结果：</p>
          <CommonTable
            :page.sync="page"
            id="xh"
            :table-data="tableData"
            :show-selection="false"
            :show-batch-tag="false"
            :table-columns.sync="tableColumns"
          ></CommonTable>
          <!-- <p class="label-text">print：</p>
          <el-input type="textarea" :rows="5" placeholder="" v-model="printVal"></el-input> -->
        </div>
      </div>
      <span slot="footer" class="dialog-footer" v-if="dialogStep !== 2">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleRun" v-if="dialogStep == 1"
          >试运行</el-button
        >

        <el-button type="primary" @click="handleDone" v-if="dialogStep == 3"
          >完成</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      title="算法属性"
      :visible.sync="algorithmAttrVisible"
      width="600px"
      :before-close="handleCloseAlgorithmAttr"
    >
      <el-form
        :model="tempAlgorithmForm"
        ref="algorithmForm"
        :rules="algorithmRules"
        label-width="120px"
      >
        <el-form-item label="算法名称：" prop="algorithmName">
          <el-input
            v-model="tempAlgorithmForm.algorithmName"
            placeholder="请输入算法名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="算法文件名称：" prop="algorithmFileName">
          <el-input
            v-model="tempAlgorithmForm.algorithmFileName"
            placeholder="请输入算法文件名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="支持环境：" prop="environmentCode">
          <el-select
            v-model="tempAlgorithmForm.environmentCode"
            placeholder="请选择支持环境："
            collapse-tags
            multiple
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="item in supportEnv"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="算法介绍：">
          <el-input
            type="textarea"
            :rows="4"
            v-model="tempAlgorithmForm.introduction"
            placeholder="请输入算法介绍"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCloseAlgorithmAttr">取消</el-button>
        <el-button type="primary" @click="handleSaveAlgorithmAttr"
          >确定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      :title="currentParamIndex === -1 ? '新增参数' : '编辑参数'"
      :visible.sync="paramDialogVisible"
      width="500px"
    >
      <el-form
        :model="paramForm"
        ref="paramForm"
        :rules="algorithmExplainRules"
        label-width="100px"
      >
        <el-form-item label="参数名称" prop="paramName">
          <el-input
            v-model="paramForm.paramName"
            placeholder="请输入参数名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="参数类型" prop="paramType">
          <el-select
            v-model="paramForm.paramType"
            placeholder="请选择参数类型"
            style="width: 100%"
            clearable
          >
            <el-option label="字符串" value="String"></el-option>
            <el-option label="数字" value="Number"></el-option>
            <el-option label="布尔" value="Boolean"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="入参示例">
          <el-input
            v-model="paramForm.paramExample"
            placeholder="请输入入参示例"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="参数介绍">
          <el-input
            type="textarea"
            v-model="paramForm.paramIntroduction"
            placeholder="请输入参数介绍"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="paramDialogVisible = false">取消</el-button>
        <el-button
          type="danger"
          plain
          @click="deleteParam"
          v-if="currentParamIndex != -1"
          >删除</el-button
        >
        <el-button type="primary" @click="saveParam">确定</el-button>
      </span>
    </el-dialog>
    <!-- 版本号输入对话框 -->
    <el-dialog
      title="算法发布"
      :visible.sync="versionDialogVisible"
      width="400px"
      @submit.native.prevent
    >
      <el-form
        :model="form"
        ref="versionForm"
        :rules="versionFormRules"
        label-width="100px"
      >
        <el-form-item label="发布版本号" prop="version">
          <el-input
            v-model="form.version"
            placeholder="请输入版本号"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="versionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmPublish">发布</el-button>
      </span>
    </el-dialog>
  </DT-View>
</template>

<script>
import Request from '@/service'
import encryptSql from '@/utils/encryptSql.js'
import MonacoEditor from '@/components/MonacoEditor'
import CommonTable from '@/components/CommonTable.vue'

export default {
  components: { MonacoEditor, CommonTable },

  props: {},
  data () {
    return {
      // 算法属性编辑弹窗可见性
      algorithmAttrVisible: false,
      // 版本号对话框相关
      versionDialogVisible: false,
      version: '',
      mainMethodName: '',
      // 算法表单数据
      algorithmForm: {
        algorithmName: '',
        algorithmFileName: '',
        supportEnv: '',
        description: ''
      },
      // 算法表单验证规则
      algorithmRules: {
        algorithmName: [
          { required: true, message: '请输入算法名称', trigger: 'change' }
        ],
        algorithmFileName: [
          { required: true, message: '请输入算法文件名称', trigger: 'change' },
          {
            pattern: /^[a-zA-Z0-9_]+$/,
            message: '文件名只能包含英文字母、数字、下划线',
            trigger: 'change'
          },
          {
            validator: (rule, value, callback) => {
              if (value && /[\u4e00-\u9fa5]/.test(value)) {
                callback(new Error('文件名不能包含中文字符'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        environmentCode: [
          { required: true, message: '请选择支持环境', trigger: 'change' }
        ]
      },
      // 算法说明表单验证规则
      algorithmExplainRules: {
        paramName: [
          { required: true, message: '请输入参数名称', trigger: 'change' }
        ],
        paramType: [
          { required: true, message: '请选择参数类型', trigger: 'change' }
        ]
      },
      // 算法发布表单验证规则
      versionFormRules: {
        version: [
          { required: true, message: '请输入版本号', trigger: 'change' },
          {
            pattern: /^\d+\.\d{1,2}$/,
            message: '版本号格式不正确，应为X.X格式',
            trigger: 'blur'
          },
          {
            validator: (_rule, value, callback) => {
              if (this.mode === 'edit' && this.historyVersion.includes(value)) {
                callback(new Error('发布版本号与历史版本号相同，请更新版本号'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      },
      // 参数编辑弹窗状态
      paramDialogVisible: false,
      currentParamIndex: -1,
      // 参数表单数据
      paramForm: {
        paramName: '',
        paramType: '',
        paramExample: '',
        paramIntroduction: ''
      },
      supportEnv: [
        {
          label: '基础环境',
          value: 'Base'
        },
        {
          label: '数据分析环境',
          value: 'DataScience'
        },
        {
          label: 'AI环境',
          value: 'AIEnv'
        }
      ],
      mode: 'add', // add/edit
      editId: null, // 编辑进来时的父层id
      editAlgorithmId: null, // 编辑进来时的算法id
      form: {
        algorithmDetailCode: null, // 算法详情编码(版本详情编码)
        algorithmCode: null, // 算法编码
        algorithmName: null, // 算法名称
        algorithmFileName: null, // 算法文件名称
        version: null, // 版本
        introduction: null, // 介绍
        algorithmParamList: [],
        environmentCode: [], // 环境编码
        // mainMethodName: null, // 主方法名
        outputDescription: null, // 输出描述
        pyScript: null // python
      },
      // 算法属性临时数据（用于编辑时暂存）
      tempAlgorithmForm: {
        algorithmName: '',
        algorithmFileName: '',
        environmentCode: [],
        introduction: ''
      },
      tables: [],
      columns: [],

      printVal: '',
      tableColumns: [],
      tableData: [],
      environmentResults: {},
      executionTime: null,
      requestStartTime: null,
      requestEndTime: null,
      uploadPercentage: 0,
      dialogStep: 1,
      ruleForm: {},
      dialogVisible: false,
      textarea: `Dataframe：
| 样本id | 数据值 | cluster | 
| 样本id（学号）|用于聚类的原始数据值 | 聚类类别（0，1，2...）|`,
      activeId: 1,
      sourcesList: [], // 数据源列表

      rules: {
        fieldNameModified: [
          {
            trigger: 'blur',
            required: true
          }
        ]
      },
      dbs: [],
      page: {
        total: 0,
        pageSize: 10,
        currentPage: 1
      },
      defaultProps: {
        label: 'name',
        children: 'zones',
        isLeaf: 'leaf'
      },

      isFull: false, // 是否全屏
      copySql: '', // 备份sql
      sqlInfo: {}, // 保存的sql信息
      indCode: '',
      numericFields: [], // 数值字段

      code: '# 默认示例代码\nprint("Hello World")',
      scriptFromHistory: null,
      cmOptions: {
        tabSize: 4,
        mode: 'text/x-python',
        theme: 'material-darker',
        lineNumbers: true,
        line: true,
        readOnly: false
      },
      algorithm: {
        name: '',
        description: ''
      },
      tryCalculateResVal: '',
      tryCalculateResList: [],
      parameters: [],
      historyVersion: []
    }
  },
  computed: {
    selectedEnvironmentError () {
      const selectedEnv = this.tryCalculateResList.find(
        item => item.value === this.tryCalculateResVal
      )
      return selectedEnv && selectedEnv.status === 'error'
        ? selectedEnv.errMsg
        : ''
    },
    isPublish () {
      // if (this.form.algorithmName && this.form.mainMethodName && this.form.environmentCode.length && this.form.algorithmParamList && this.form.pyScript) {
      if (
        this.form.algorithmName &&
        this.form.algorithmFileName &&
        this.form.environmentCode.length &&
        this.form.algorithmParamList.length &&
        this.form.pyScript
      ) {
        return true
      } else {
        return false
      }
    },
    hintData () {
      return [
        ...this.tables.map(item => item.id),
        ...this.columns.map(item => item.id)
      ]
    },
    isNewSql () {
      console.log(this.copySql, 'this.copySql')
      console.log(this.form.sql, 'this.form.sql')

      return this.indCode
    }
  },
  mounted () {
    console.log(1111, this.form)
    this.mode = this.$route.query.mode
    this.editId = this.$route.query.id || null
    if (this.mode === 'edit' || this.mode === 'view') {
      // 处理从历史版本页面返回的数据
      if (this.$route.query.from === 'history' && this.$route.query.script) {
        this.scriptFromHistory = decodeURIComponent(this.$route.query.script)
        console.log(2323, this.mode, this.scriptFromHistory)
      } else {
        this.scriptFromHistory = null
      }
      this.getDetail()
      this.getVersionInfo()
    }
  },
  watch: {},
  methods: {
    async getVersionInfo () {
      const res = await Request.algorithm.getAlgorithmDetailList(this.editId)
      if (res.code === 200 && res.data) {
        this.historyVersion = res.data.map(item => {
          return item.version
        })
      }
    },
    async getDetail () {
      const res = await Request.algorithm.getAlgorithmDetail(this.editId)
      if (res.code === 200) {
        const data = res.data
        this.form = {
          ...this.form,
          ...data,
          pyScript: this.scriptFromHistory || data.pyScript,
          environmentCode: Array.isArray(data.environmentCode)
            ? data.environmentCode
            : data.environmentCode
            ? data.environmentCode.split(',')
            : []
        }
        this.version = data.version || ''
        this.editAlgorithmId = data.id
        console.log(354, this.form)
        const pyScript = this.scriptFromHistory || data.pyScript
        console.log(12345, pyScript)
        // 更新编辑器内容
        if (pyScript) {
          this.$nextTick(() => {
            if (this.$refs.MonacoEditor) {
              this.$refs.MonacoEditor.setInitValue(pyScript)
            }
          })
        }
      }
    },
    // 打开算法属性编辑弹窗
    openAlgorithmAttr () {
      this.tempAlgorithmForm = {
        algorithmName: this.form.algorithmName || '',
        algorithmFileName: this.form.algorithmFileName || '',
        environmentCode: [...this.form.environmentCode] || [],
        introduction: this.form.introduction || ''
      }
      this.$nextTick(() => {
        this.$refs.algorithmForm && this.$refs.algorithmForm.clearValidate()
      })
      this.algorithmAttrVisible = true
    },

    // 关闭算法属性编辑弹窗
    handleCloseAlgorithmAttr () {
      this.algorithmAttrVisible = false
    },

    // 保存算法属性
    handleSaveAlgorithmAttr () {
      this.$refs.algorithmForm.validate(valid => {
        if (valid) {
          // 将临时数据复制到主表单数据中
          this.form.algorithmName = this.tempAlgorithmForm.algorithmName
          this.form.algorithmFileName = this.tempAlgorithmForm.algorithmFileName
          this.form.environmentCode = [
            ...this.tempAlgorithmForm.environmentCode
          ]
          this.form.introduction = this.tempAlgorithmForm.introduction
          this.algorithmAttrVisible = false
        } else {
          return false
        }
      })
    },
    // 新增参数
    addParam () {
      this.paramForm = {
        paramName: '',
        paramType: '',
        paramExample: '',
        paramIntroduction: ''
      }
      this.$nextTick(() => {
        this.$refs.paramForm && this.$refs.paramForm.clearValidate()
      })
      this.currentParamIndex = -1
      this.paramDialogVisible = true
    },
    // 删除参数
    deleteParam () {
      console.log(1111, this.currentParamIndex, this.form.algorithmParamList)
      if (
        this.currentParamIndex >= 0 &&
        this.currentParamIndex < this.form.algorithmParamList.length
      ) {
        this.form.algorithmParamList.splice(this.currentParamIndex, 1)
      }
      this.currentParamIndex = -1
      this.paramDialogVisible = false
    },
    openParamDialog (index) {
      this.currentParamIndex = index
      this.paramForm = { ...this.form.algorithmParamList[index] }
      this.paramDialogVisible = true
    },
    // 保存参数编辑
    saveParam () {
      this.$refs.paramForm.validate(valid => {
        if (valid) {
          if (this.currentParamIndex === -1) {
            // 新增参数
            this.form.algorithmParamList.push({
              ...this.paramForm
            })
          } else {
            // 编辑参数
            this.form.algorithmParamList[this.currentParamIndex] = {
              ...this.paramForm
            }
          }
          this.$nextTick(() => {
            this.$refs.paramForm && this.$refs.paramForm.clearValidate()
          })
          this.paramDialogVisible = false
        }
      })
    },

    // 确认发布
    async confirmPublish () {
      console.log(2334324, this.form.version, this.version)
      this.$refs.versionForm.validate(async valid => {
        if (valid) {
          this.mode === 'add' && (this.version = this.form.version)
          let publishParams = {
            ...this.form,
            mainMethodName: this.mainMethodName,
            environmentCode: this.form.environmentCode.join(',')
          }
          if (this.mode === 'edit') {
            delete publishParams.id
            publishParams.algorithmParamList.map(item => {
              delete item.id
            })
          }
          const res = await Request.algorithm.saveAlgorithm(publishParams)
          if (res.code === 200) {
            this.$message.success('发布成功')
            this.mode === 'edit' && (this.version = this.form.version)
            this.versionDialogVisible = false
            this.$router.push({
              name: 'AlgorithmLibrary'
            })
          } else {
            this.$message.error('发布失败')
          }
        }
      })
    },
    handleEnvironmentChange (val) {
      console.log(val, this.tableData)
      // 从之前保存的结果中找到对应环境的数据
      if (this.environmentResults[val]) {
        this.tableData = this.environmentResults[val].data || []
        this.tableColumns = this.environmentResults[val].columns || []
      } else {
        // 如果没有数据，清空表格
        this.tableData = []
        this.tableColumns = []
      }
    },
    handleDone () {
      this.dialogStep = 1
      this.dialogVisible = false
    },
    // 试运行
    async handleRun () {
      console.log(343434, this.ruleForm, this.form.algorithmParamList)
      if (this.form.pyScript.trim() === '') {
        this.$message.warning('请输入代码')
        return
      }

      if (this.form.environmentCode.length === 0) {
        this.$message.warning('请至少选择一个支持环境')
        return
      }

      // 构造包含参数值的表单数据
      const formWithParamValues = {
        ...this.form,
        mainMethodName: this.mainMethodName
      }

      // 将 ruleForm 中的参数值映射到 algorithmParamList 中
      formWithParamValues.algorithmParamList = this.form.algorithmParamList.map(
        param => {
          return {
            ...param,
            paramValue: this.ruleForm[param.paramName] || null
          }
        }
      )

      this.dialogStep = 2
      this.uploadPercentage = 0 // 初始化进度为 0

      // 记录请求开始时间
      this.requestStartTime = Date.now()

      // 开始模拟进度条增长
      let progress = 0
      const maxProgress = 90 // 最进度设为90%

      const interval = setInterval(() => {
        const increment = Math.floor(Math.random() * 3) + 1
        progress = Math.min(progress + increment, maxProgress)
        this.uploadPercentage = progress
      }, 100)

      try {
        // 并行发送所有请求，使用包含参数值的表单数据
        const requests = this.form.environmentCode.map(envCode => {
          return Request.algorithm.tryCalculate({
            ...formWithParamValues,
            environmentCode: envCode // 每个请求使用一个环境代码
          })
        })

        // 等待所有请求完成（无论成功或失败）
        const results = await Promise.allSettled(requests)
        console.log(111111, results)

        // 请求完成后，立即设置进度为100%
        clearInterval(interval)
        this.uploadPercentage = 100

        // 记录请求结束时间并计算耗时
        this.requestEndTime = Date.now()
        this.executionTime = (
          (this.requestEndTime - this.requestStartTime) /
          1000
        ).toFixed(2)
        // 处理所有结果，更新 tryCalculateResList
        this.tryCalculateResList = this.form.environmentCode.map(
          (envCode, index) => {
            const env = this.supportEnv.find(e => e.value === envCode)
            const result = results[index]

            // 检查请求是否成功
            if (result.status === 'fulfilled') {
              const response = result.value
              const isBusinessSuccess = response.code === 200
              return {
                label: env ? env.label : envCode,
                value: envCode,
                status: isBusinessSuccess ? 'success' : 'error',
                errMsg: isBusinessSuccess ? '' : response.message || '请求失败'
              }
            } else {
              // 请求失败
              return {
                label: env ? env.label : envCode,
                value: envCode,
                status: 'error',
                errMsg: result.reason || '请求失败'
              }
            }
          }
        )

        // 保存每个环境的结果数据（只保存成功的）
        this.form.environmentCode.forEach((envCode, index) => {
          const result = results[index]
          // 只有成功的请求才保存数据
          if (result.status === 'fulfilled' && result.value.code === 200) {
            if (result.value.data.result) {
              this.$set(this.environmentResults, envCode, {
                data: result.value.data.result || [],
                columns:
                  result.value.data.result &&
                  result.value.data.result.length > 0
                    ? Object.keys(result.value.data.result[0]).map(
                        (key, colIndex) => ({
                          id: colIndex,
                          label: key,
                          prop: key,
                          visible: true,
                          sortable: false
                        })
                      )
                    : []
              })
            }
          }
        })

        console.log(3432, this.environmentResults)

        // 默认选中第一个失败的环境，如果没有失败则选中第一个成功的环境
        const firstError = this.tryCalculateResList.find(
          item => item.status === 'error'
        )
        const firstSuccess = this.tryCalculateResList.find(
          item => item.status === 'success'
        )

        if (firstError) {
          this.tryCalculateResVal = firstError.value
          this.updateTableData(firstError.value)
        } else if (firstSuccess) {
          this.tryCalculateResVal = firstSuccess.value
          this.updateTableData(firstSuccess.value)
        }

        console.log('所有请求结果:', results)
        // 新增日志信息
        // 延迟一小段时间后进入结果页面
        setTimeout(() => {
          this.dialogStep = 3
        }, 300)
      } catch (error) {
        // 请求失败处理
        clearInterval(interval)
        this.uploadPercentage = 100
        // 延迟后进入结果页面
        setTimeout(() => {
          this.dialogStep = 3
        }, 300)
      }
    },
    updateTableData (environmentValue) {
      // 查找选中环境的结果信息
      const selectedEnv = this.tryCalculateResList.find(
        item => item.value === environmentValue
      )

      // 如果有错误信息，清空表格数据并显示错误信息
      if (selectedEnv && selectedEnv.status === 'error') {
        this.tableData = []
        this.tableColumns = []
        return
      }

      // 如果没有错误，显示表格数据
      if (this.environmentResults[environmentValue]) {
        this.tableData = this.environmentResults[environmentValue].data || []
        this.tableColumns =
          this.environmentResults[environmentValue].columns || []
      } else {
        // 如果没有数据，清空表格
        this.tableData = []
        this.tableColumns = []
      }
    },
    // 树节点加载
    async loadNode (node, resolve) {
      console.log('树节点加载', node)
      // 第一层库
      if (node.level === 0) {
        const { currentPage = 1, pageSize = 30 } = node.data.pagination || {}

        const { data } = await this.$httpBi.indicatorAnagement.getTableList({
          key: '',
          currentPage: currentPage,
          pageSize: pageSize
        })
        let list = data.list
        this.tables = this.tables.concat(data.list)

        const index = node.data.findIndex(d => d.id === 'load-more-id')
        console.log(index, 'index')
        if (index) {
          node.childNodes.splice(index, 1)
          node.data.splice(index, 1)
        }
        // 检查是否需要添加"加载更多"节点
        if (Math.ceil(data.totalCount / data.pageSize) > data.currentPage) {
          list.push({
            id: 'load-more-id', // 唯一标识
            label: '加载更多',
            leaf: true,

            isLoadingMoreNode: true
          })
        }
        // 点击"加载更多"合并新返回的数据
        node.data.push(...list)

        node.data.pagination = {
          currentPage: data.currentPage,
          pageSize: data.pageSize,
          total: data.totalCount
        }

        node.loaded = true // 标记为已加载，如果需要的话

        // 如果resolve有内容就是懒加载走查询 否则走的是修改
        console.log('否则走的是修改', node.data)
        if (resolve) {
          return resolve(node.data)
        }
      }
      // 第二层表
      if (node.level === 1) {
        const { data } = await this.$httpBi.indicatorAnagement.getTableFields({
          id: node.data.id,
          databaseName: 'ods'
        })
        let list = data[0].info.map(item => ({
          ...item,
          id: item.zddm,
          leaf: true
        }))
        this.columns = this.columns.concat(list)

        return resolve(list)
      }

      // 第三层字段
      if (node.level >= 2) return resolve([])
    },
    editHistory () {
      console.log('编辑历史', this.editId, this.editAlgorithmId)
      this.$router.push({
        name: 'AlgorithmHistory',
        query: {
          editAlgorithmId: this.editAlgorithmId,
          editId: this.editId
        }
      })
    },
    handleNodeClick (data, node) {
      if (node.level >= 2) return
      console.log(2222, data, node)
      // 根据分页和总条数，判断是否需要点击加载更多
      if (
        node.parent.data.pagination.currentPage <
        node.parent.data.pagination.total / node.parent.data.pagination.pageSize
      ) {
        console.log(3432545)
        node.parent.data.pagination.currentPage++
        this.loadNode(node.parent, () => {}) // 触发懒加载以获取更多数据，这里不需要 resolve
      }
    },
    handleTryCalc () {
      if (this.form.pyScript.trim() === '') {
        this.$message.warning('请输入代码')
        return
      }
      if (!this.mainMethodName) {
        this.$message.warning('请输入主函数')
        return
      }
      this.ruleForm = {}
      this.form.algorithmParamList.forEach(param => {
        this.$set(this.ruleForm, param.paramName, param.paramExample || '')
      })
      this.dialogVisible = true
    },
    // 打开版本号输入对话框
    handlePublish () {
      if (!this.mainMethodName) {
        this.$message.warning('请输入主函数')
        return
      }
      this.versionDialogVisible = true
      this.$nextTick(() => {
        this.$refs.versionForm && this.$refs.versionForm.clearValidate()
      })
      this.mode === 'add' && (this.form.version = '1.0') // 默认版本号
      this.mode === 'edit' && (this.form.version = this.version)
      let testStr1 = encryptSql.encrypt(this.form.pyScript)
      let testStr2 = encryptSql.decrypt(testStr1)
      console.log('加密', testStr1)
      console.log('解密', testStr2)
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-dialog__header {
  margin: 0 24px;
  padding: 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #edeff0;
}

::v-deep .el-dialog__body {
  padding: 20px 24px;
}

.inner-box {
  display: flex;
  align-items: center;
  padding: 24px;
  box-sizing: border-box;

  .icon {
    width: 56px;
    height: 56px;
    background: url('~@/assets/images/yjs.png') no-repeat;
    margin-right: 12px;
  }

  .text {
    text-align: left;
    height: 14px;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #323233;
    line-height: 14px;
    margin-bottom: 12px;
  }

  .right {
    width: 500px;
  }

  height: 104px;
  background: #ffffff;
  border-radius: 8px;
}

.step3-box {
  .label-text {
    margin: 20px 0;
  }
}

.create-head {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  border-bottom: 1px solid #e5e5e5;
  height: 80px;
  background: #fff;
  padding: 20px;
  box-sizing: border-box;

  .el-icon-back {
    font-size: 20px;
    padding-right: 16px;
    cursor: pointer;
  }

  .steps {
    width: 150px;
    height: 48px;
    background: #f5f7fa;
    border-radius: 6px;
    margin-left: 40px;
    display: flex;
    align-items: center;
    padding: 0 32px;

    .line {
      width: 168px;
      height: 1px;
      background: #cbced1;
      margin: 0 12px;

      &.active {
        background: #1563ff;
      }
    }

    .step-item {
      display: flex;
      align-items: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #5c646e;

      &.active {
        color: #1563ff;
      }

      i {
        font-size: 19px;
        margin-right: 6px;
      }
    }
  }
}

.step-two {
  height: calc(100% - 120px);
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.editor-content {
  width: 100%;
  display: flex;
  min-height: calc(100% - 120px);
  padding-top: 20px;

  background-color: #f0f2f5;
  box-sizing: border-box;

  .left {
    flex-shrink: 0;
    width: 320px;
    border-radius: 6px;
    margin-right: 16px;
    background: #fff;
    padding: 24px 20px 0 16px;

    ::v-deep .el-form {
      height: 100%;
      display: flex;
      flex-direction: column;

      .el-form-item:nth-child(2) {
        flex: 1 1 0;
        overflow: hidden;

        &::-webkit-scrollbar {
          /*滚动条整体样式*/
          width: 6px;
          /*高宽分别对应横竖滚动条的尺寸*/
          height: 6px;
        }

        &::-webkit-scrollbar-thumb {
          /*滚动条里面小方块*/
          border-radius: 6px;
          height: 2px;
          background-color: #cfd6e6;
        }

        &::-webkit-scrollbar-track {
          /*滚动条里面轨道*/
          // box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
          background: transparent;
          border-radius: 6px;
        }

        .el-form-item__content {
          height: calc(100% - 45px);
        }
      }
    }

    .custom-tree-node {
      width: 100%;
    }

    .tree-db {
      height: 100%;
      overflow-y: auto;

      &::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 6px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 6px;
      }

      &::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius: 6px;
        height: 2px;
        background-color: #cfd6e6;
      }

      &::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        // box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        background: transparent;
        border-radius: 6px;
      }
    }
  }

  .right {
    position: relative;
    width: calc(100% - 340px);
    padding: 20px;
    min-height: calc(100vh - 250px);

    background: #fff;
    padding-bottom: 52px;
    display: flex;

    .editor-pane {
      width: calc(100% - 340px);
      padding: 20px;
      background: #2d2d2d;
    }

    .config-pane {
      flex: 0 0 320px;
      overflow-y: auto;
      margin-left: 20px;
    }

    .code-editor {
      height: 100%;
      margin-right: 20px;
    }

    .form-section {
      margin-bottom: 16px;
      padding-bottom: 16px;

      h3 {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #222222;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        background: #f5f6fa;
        padding: 8px;
        box-sizing: border-box;
        border-radius: 4px 4px 0 0;
      }

      .attrs {
        padding: 16px;
        box-sizing: border-box;
        border-radius: 0 0 4px 4px;
        border: 1px solid #f5f6fa;
        border-top: none;

        .attr-item {
          display: flex;
          align-items: center;
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            height: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 14px;
            color: #222222;
            line-height: 14px;
            text-align: right;
            font-style: normal;
            margin-right: 8px;
          }

          .value {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #222222;
            line-height: 18px;
            text-align: left;
            font-style: normal;
          }
        }
      }

      .params {
        padding: 16px;
        box-sizing: border-box;
        border-radius: 0 0 4px 4px;
        border: 1px solid #f5f6fa;
        border-top: none;

        .params-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            height: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 14px;
            color: #222222;
            line-height: 14px;
            text-align: right;
            font-style: normal;
            margin-right: 8px;
          }

          .value {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #222222;
            line-height: 18px;
            text-align: left;
            font-style: normal;
          }
        }
      }

      .explain {
        padding: 16px;
        box-sizing: border-box;
        border-radius: 0 0 4px 4px;
        border: 1px solid #f5f6fa;
        border-top: none;
        height: 120px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    .footer-btn {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      position: absolute;
      bottom: 0;
      left: 0;
      height: 52px;
      padding-right: 24px;
      box-sizing: border-box;
      border-top: 1px solid #f0f0f0;
    }
  }
}

.step-btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 52px;
  padding-right: 24px;
  box-sizing: border-box;
  border-top: 1px solid #f0f0f0;
}
</style>
<style lang="scss">
.drag-element {
  /* 禁止文本选择 */
  user-select: none;
  /* 禁用默认拖拽效果 */
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

#project_frame
  .tree-db
  .el-tree-node.is-current
  > .el-tree-node__content
  .el-tree-node__expand-icon {
  background-color: transparent;
}

#project_frame
  .tree-db
  .el-tree-node.is-current
  > .el-tree-node__content:has(> span.item-style) {
  position: relative;
  background: #f4f7ff !important;
  box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
    0px 6px 6px -4px rgba(0, 42, 128, 0.12);
  border-radius: 4px;
  border: 1px solid #1563ff;
  cursor: move;

  .el-tree-node__label {
    background: transparent !important;
  }
}

#project_frame .tree-db .el-tree-node {
  border: 1px solid transparent !important;
}

#project_frame {
  .tree-db {
    .el-tree-node .el-tree-node__content {
      .is-leaf.el-tree-node__expand-icon.el-icon-caret-right {
        display: none;
      }
    }
    .el-tree-node > .el-tree-node__content {
      margin-bottom: 8px;
      &:last-child {
        margin-bottom: 0;
      }
    }
    .el-tree-node:not(.is-expanded)
      > .el-tree-node__content:has(> span.item-style) {
      &:hover {
        position: relative;
        background: #f4f7ff !important;
        box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
          0px 6px 6px -4px rgba(0, 42, 128, 0.12);
        border-radius: 4px;
        border: 1px solid #1563ff;
        cursor: move;

        .el-checkbox {
          background-color: transparent !important;
        }

        .el-tree-node__expand-icon {
          background-color: transparent !important;

          border-top-left-radius: 2px;
          border-bottom-left-radius: 2px;
          -webkit-transition: all 0.3s;
          transition: all 0.3s;
        }

        .custom-tree-node,
        .el-tree-node__label {
          background-color: transparent !important;
          border-top-right-radius: 2px;
          border-bottom-right-radius: 2px;
          -webkit-transition: all 0.3s;
          transition: all 0.3s;
        }
      }
    }

    .el-tree-node.dragging > .el-tree-node__content {
      opacity: 0.2;
    }

    .el-tree-node > .el-tree-node__content {
      height: 50px;
      display: block;

      .custom-tree-node-alg {
        position: relative;
        height: 100%;
        width: 100%;
        box-sizing: border-box;
        margin-bottom: 6px;

        .table-name {
          display: flex;
          height: 10px;
          position: absolute;
          font-size: 12px;
          top: 10px;
          left: 20px;

          &::after {
            position: absolute;
            top: 7px;
            left: -15px;
            content: '';
            display: block;
            width: 12px;
            height: 12px;
            border: 1px solid #1563ff;
            border-top: none;
            border-right: none;
          }
        }
      }

      &:hover {
        background: #f5f7fa !important;

        > .el-checkbox {
          background-color: transparent !important;
        }

        .el-tree-node__expand-icon {
          background-color: transparent !important;

          border-top-left-radius: 2px;
          border-bottom-left-radius: 2px;
          -webkit-transition: all 0.3s;
          transition: all 0.3s;
        }

        .custom-tree-node,
        .el-tree-node__label {
          background-color: transparent !important;

          border-top-right-radius: 2px;
          border-bottom-right-radius: 2px;
          -webkit-transition: all 0.3s;
          transition: all 0.3s;
        }
      }
    }

    .el-tree-node.is-current > .el-tree-node__content {
      background: #f5f7fa;

      > .el-checkbox {
        background-color: transparent !important;
      }

      .el-tree-node__expand-icon {
        background-color: transparent !important;

        border-top-left-radius: 2px;
        border-bottom-left-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }

      .custom-tree-node,
      .el-tree-node__label {
        background-color: transparent !important;

        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }
    }
  }
}

#project_frame .el-table.sqlIndicator-table td,
#project_frame .el-table.sqlIndicator-table th {
  padding: 0;
}
</style>
