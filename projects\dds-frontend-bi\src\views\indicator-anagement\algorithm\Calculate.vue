<template>
  <el-dialog
    title="试计算"
    :visible.sync="dialogVisible"
    width="950px"
    top="10vh"
    :before-close="handleClose"
  >
    <div class="sub-title" style="margin-top: 15px">
      <div class="sub-text">维度</div>
      <div class="sub-line"></div>
    </div>

    <div style="display: flex; align-items: center; margin-bottom: 8px">
      <div class="label" style="margin-right: 10px; flex: 0 0 60px">选择维度</div>
      <div class="right" style="flex: 1">
        <el-row :gutter="15">
          <el-col :span="10" v-for="(item, index) in dimensionList" :key="index">
            <div style="display: flex; align-items: center; margin: 8px 0">
              <span class="dimName" :title="item.dimAndLevelName">
                {{ item.dimAndLevelName }}
              </span>
              <LevelMultipleSelect
                v-model="item.wdzval"
                v-if="item.levelCode && !item.enableClustering"
                :is-selected-all="item.isSelectedAll"
                :is-selected-all-name="item.isSelectedAllName"
                style="width: 205px; margin-left: 6px"
                :level-code="item.levelCode"
                :props="{
                  label: 'value',
                  value: 'value'
                }"
              />
              <!-- 使用聚类 -->
              <ClusterMultipleSelect
                v-if="item.levelCode && item.enableClustering"
                :dim-values.sync="item.wdzval"
                v-model="item.clusterCodes"
                value-code="value"
                :is-selected-all="item.isSelectedAll"
                :is-selected-all-name="item.isSelectedAllName"
                :disabled="item.isDisabled"
                :level-code="item.levelCode"
                style="width: 205px; margin-left: 6px"
              />
            </div>
          </el-col>
          <el-col :span="2" style="margin: 8px 0">
            <el-button @click="openDimensionDialog"> 选择过滤维度 </el-button>
          </el-col>
        </el-row>
      </div>
    </div>
    <div style="display: flex; align-items: center" v-if="isHasDateDim">
      <div class="label" style="margin-right: 10px; flex: 0 0 60px">时间范围</div>
      <el-date-picker
        v-model="date"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd HH:mm:ss"
      ></el-date-picker>
      <el-button @click="calculation" style="margin-left: 70px"> 计算 </el-button>
    </div>

    <el-button v-else @click="calculation" style="margin-left: 70px; margin-top: 0px">
      计算
    </el-button>
    <div class="sub-title" style="margin-top: 24px">
      <div class="sub-text">试计算结果</div>
      <div class="sub-line"></div>
    </div>
    <el-table :data="tableData" v-loading="loading" :max-height="300">
      <el-table-column prop="indName" label="指标" show-overflow-tooltip></el-table-column>
      <el-table-column show-overflow-tooltip prop="valShow" label="值" width="220">
        <!-- <template #default="{ row }">
          {{ row.valShow | formatValue }}
        </template> -->
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="unit" label="单位" width="100">
        <template #default="{ row }">
          {{ row.unit || "-" }}
        </template>
      </el-table-column>
    </el-table>
    <FilterDimDialog
      :filter-dims.sync="dimensionList"
      ref="FilterDimDialog"
      :props="{
        label: 'dimAndLevelName',
        value: 'dimCol',
        valueList: 'wdzval'
      }"
      @getDimsValue="getDimsValue"
    />
  </el-dialog>
</template>

<script>
import options from "../mixins/options"
import Request from "@/service"
import LevelMultipleSelect from "../components/LevelMultipleSelect.vue"
import ClusterMultipleSelect from "../components/ClusterMultipleSelect.vue"

import FilterDimDialog from "../components/FilterDimDialog.vue"

export default {
  components: { ClusterMultipleSelect, FilterDimDialog, LevelMultipleSelect },
  mixins: [options],
  props: {},
  data() {
    return {
      isHasDateDim: false,
      isCollapsed: true,
      dialogVisible: false,
      tableData: [],
      loading: false,
      dimensionList: [],
      currentIndicatorInfo: {},
      pswdOptionsMap: {},
      date: [],
      allFilterDims: []
    }
  },
  computed: {},
  methods: {
    open() {
      this.dialogVisible = true
      this.tableData = []
      this.dimensionList = []
    },
    openDimensionDialog() {
      this.$refs.FilterDimDialog.open(this.allFilterDims)
    },
    async initData(row) {
      console.log(1212121, row)
      this.currentIndicatorInfo = row
      this.open()
      const { data } = await this.$httpBi.api.paramGet("DimManage/getDimLevelByIndCode", {
        indCode: row.indCode
      })
      this.allFilterDims = data
        .map((item) => ({
          ...item,
          col: item.dimCol,
          wdzval: []
        }))
        .filter((item) => item.fieldType !== "time")

      data.forEach((item) => {
        if (item.fieldType === "time") {
          this.isHasDateDim = true
        }
      })
      if (!data.length) {
        this.dimensionList = []
      }
      this.calculation()
    },
    async calculation() {
      this.loading = true

      // 优化数组为空的情况
      let dimensionFilters = {}
      if (this.dimensionList && this.dimensionList.length > 0) {
        dimensionFilters = this.dimensionList.reduce((acc, item) => {
          // 确保 item.dimCol 存在且 item.wdzval 是数组
          if (item && item.dimCol) {
            acc[item.dimCol] = Array.isArray(item.wdzval) ? item.wdzval : []
          }
          return acc
        }, {})
      }

      try {
        const { data } = await Request.api.paramPost("algorithmIndicator/tryCalculate", {
          indCode: this.currentIndicatorInfo.indCode,
          startTime: this.date && this.date.length > 0 ? this.date[0] : null,
          endTime: this.date && this.date.length > 1 ? this.date[1] : null,
          dimensionFilters: dimensionFilters
        })
        if (Array.isArray(data)) {
          this.tableData = data
        } else if (data && typeof data === "object") {
          this.tableData = [data]
        } else {
          this.tableData = []
        }
      } catch (error) {
        console.error("计算出错:", error)
        this.tableData = []
      } finally {
        this.loading = false
      }
    },
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed
    }
  }
}
</script>

<style scoped lang="scss">
.sub-title {
  display: flex;
  align-items: center;
  margin-bottom: 24px;

  .sub-text {
    font-size: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #1d2129;
    margin-right: 5px;
  }

  .sub-line {
    height: 1px;
    flex: 1;
    background: #e5e6eb;
  }
}
.dimName {
  flex: 0 0 50%;
  border: 1px solid #dcdfe6;
  padding-left: 15px;
  height: 32px;
  line-height: 32px;
  border-radius: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
::v-deep .el-row {
  margin-bottom: 0;
}
</style>
