<template>
  <div class="indicator-wrap">
    <!-- 指标 -->
    <template v-if="itemProp.ctype == 1">
      <div
        v-if="isDragging"
        class="indicator-item-before"
        @dragover.prevent
        @drop="handleDrop($event, index, 'left')"
      ></div>
      <el-popover
        placement="right"
        width="430"
        v-model="showPopover"
        trigger="manual"
        content="xxx"
      >
        <div>
          <div class="popover-title">选择维度</div>
          <div class="popover-content">
            <div class="indicators-name">
              <div class="label">指标名称：</div>
              <div class="value">
                {{ itemProp.indicatorName }}
              </div>
            </div>
            <div class="dimension">
              <div class="label">
                派生维度
                <div
                  class="plus"
                  @click="addDimension"
                  style="margin-left: 10px"
                  v-if="form.tempStepDimensionList.length === 0"
                ></div>
              </div>
              <div class="dimension-list">
                <el-form :model="form" ref="ruleForm" class="ruleForm">
                  <div
                    class="dimension-item"
                    v-for="(item, index) in form.tempStepDimensionList"
                    :key="index + item.id"
                  >
                    <el-form-item
                      :prop="'tempStepDimensionList.' + index + '.wdzd'"
                      :rules="{
                        required: true,
                        message: '维度字段不能为空',
                        trigger: 'change'
                      }"
                    >
                      <el-select
                        v-model="item.wdzd"
                        placeholder="请选择"
                        style="width: 144px; margin-right: 8px"
                        @change="handleChange"
                      >
                        <el-option
                          v-for="item in dimensionList"
                          :disabled="
                            form.tempStepDimensionList.some(
                              e => e.wdzd === item.dimCol
                            )
                          "
                          :key="item.dimCol"
                          :label="item.dimName"
                          :value="item.dimCol"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item
                      :prop="'tempStepDimensionList.' + index + '.dimValueList'"
                      :rules="{
                        required: true,
                        message: '维度值不能为空',
                        trigger: 'change'
                      }"
                      v-if="!item.enableClustering"
                    >
                      <LevelMultipleSelect
                        v-if="item.levelCode && !item.enableClustering"
                        :is-selected-all="item.isSelectedAll"
                        :is-selected-all-name="item.isSelectedAllName"
                        v-model="item.dimValueList"
                        :dim-id="item.dimId"
                        :ind-type="item.indType"
                        style="width: 144px; margin-left: 6px"
                        :level-code="item.levelCode"
                      />
                    </el-form-item>
                    <el-form-item
                      :prop="'tempStepDimensionList.' + index + '.clusterCodes'"
                      :rules="{
                        required: true,
                        message: '维度值不能为空',
                        trigger: 'change'
                      }"
                      v-else
                    >
                      <!-- 使用聚类 -->
                      <ClusterMultipleSelect
                        v-if="item.levelCode && item.enableClustering"
                        :dim-values.sync="item.dimValueList"
                        v-model="item.clusterCodes"
                        :is-selected-all="item.isSelectedAll"
                        :is-selected-all-name="item.isSelectedAllName"
                        :disabled="item.isDisabled"
                        :level-code="item.levelCode"
                        style="width: 205px; margin-left: 6px"
                      />
                    </el-form-item>

                    <div class="minus" @click="handleDelete(index)"></div>
                    <div class="plus" @click="addDimension" style="margin-right: 6px;"></div>
                  </div>
                </el-form>
              </div>
            </div>
          </div>
          <div class="popover-footer">
            <el-button @click="showPopover = false">取消</el-button>
            <el-button type="primary" @click="handleSave">保存</el-button>
          </div>
        </div>
        <div
          class="drag-indicator-item"
          slot="reference"
          :class="{ moving: isDragging, active: tempIndex === index }"
          :draggable="true"
          @dragstart="e => handleDragStart(e, itemProp)"
        >
          <p
            :title="`${itemProp.indicatorName}${itemProp.indicatorUnit?`(${itemProp.indicatorUnit})` : ''}`"
          >
            {{ itemProp.indicatorName }}
            <template v-if="itemProp.indicatorUnit">
({{ itemProp.indicatorUnit }})
            </template>
          </p>
          <p
            class="add"
            @click="getSelectDimension"
            v-if="!itemProp.stepDimensionList.length"
          >
            添加维度
          </p>
          <p class="numbers" @click="getSelectDimension" v-else>
            已选{{ itemProp.stepDimensionList.length }}个维度
          </p>
        </div>
      </el-popover>
      <div
        v-if="isDragging"
        class="indicator-item-after"
        @dragover.prevent
        @drop="handleDrop($event, index + 1, 'right')"
      ></div>
    </template>
    <!-- 括号 -->
    <template v-if="itemProp.ctype == 2">
      <div
        v-if="isDragging"
        class="indicator-item-before"
        @dragover.prevent
        @drop="handleDrop($event, index, 'left')"
      ></div>
      <BracketItem
        draggable="true"
        v-if="itemProp.jsf == '('"
        @handleDragStart="e => handleDragStart(e, itemProp)"
        @handleDrop="(...args) => $emit('handleDrop', args)"
        :item-prop="itemProp.jsf"
        :index="index"
        :is-dragging="isDragging"
      />

      <BracketItem
        draggable="true"
        v-if="itemProp.jsf == ')'"
        @handleDragStart="e => handleDragStart(e, itemProp)"
        @handleDrop="(...args) => $emit('handleDrop', args)"
        :type="itemProp.jsf"
        :index="index"
        :is-dragging="isDragging"
      />
      <div
        v-if="isDragging"
        class="indicator-item-after"
        @dragover.prevent
        @drop="handleDrop($event, index + 1, 'right')"
      ></div>
    </template>
    <!-- 计算符-->
    <template v-if="itemProp.ctype == 3">
      <div
        class="connector-line right"
        :class="[itemProp.jsf ? 'isArithmetic' : '']"
      ></div>

      <div class="connector" :class="[itemProp.jsf ? 'isArithmetic' : '']">
        <el-select
          :value="itemProp.jsf"
          placeholder="请选择"
          @change="handleChangejsf"
        >
          <el-option
            v-for="item in selectedOperator"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
      <div
        class="connector-line left"
        :class="[itemProp.jsf ? 'isArithmetic' : '']"
      ></div>
    </template>
    <!-- 自定义输入框 -->
    <template v-if="itemProp.ctype == 4">
      <div
        v-if="isDragging"
        class="indicator-item-before"
        @dragover.prevent
        @drop="handleDrop($event, index, 'left')"
      ></div>

      <div
        :class="{ moving: isDragging, active: tempIndex === index }"
        :draggable="true"
        @dragstart="e => handleDragStart(e, itemProp)"
      >
        <el-input
          placeholder="请输入数值"
          :value="itemProp.jsf"
          @input="handleChangejsf"
          style="width: 120px"
        ></el-input>
      </div>
      <div
        v-if="isDragging"
        class="indicator-item-after"
        @dragover.prevent
        @drop="handleDrop($event, index + 1, 'right')"
      ></div>
    </template>
  </div>
</template>

<script>
// import Request from "@/service"
import BracketItem from "./BracketItem"
import cloneDeep from "lodash/cloneDeep"
import LevelMultipleSelect from "../components/LevelMultipleSelect.vue"
import ClusterMultipleSelect from "../components/ClusterMultipleSelect.vue"

export default {
  components: { BracketItem, LevelMultipleSelect, ClusterMultipleSelect },
  props: {
    itemProp: {
      type: Object,
      default: () => {}
    },
    isLastNode: {
      type: Boolean,
      default: false
    },
    isDragging: {
      type: Boolean,
      default: false
    },
    index: {
      type: Number,
      default: 0
    },
    tempIndex: {
      type: Number,
      default: 0
    },
    parentIndex: {
      type: Number,
      default: 0
    },
    steps: {
      type: Array,
      default: () => []
    },
    mapDimensions: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      showPopover: false,
      // 相同单位运算符号
      sameSelectedOperator: [
        { label: "÷", value: "÷" },
        { label: "×", value: "×" },
        { label: "+", value: "+" },
        { label: "-", value: "-" }
      ],
      // 非相同单位
      differentSelectedOperator: [
        {
          label: "÷",
          value: "÷"
        },
        {
          label: "×",
          value: "×"
        }
      ],

      // 维度集合
      dimensionList: [],
      // 临时维度集合
      form: {
        tempStepDimensionList: []
      },
      // 维度值集合
      dimensionValues: {}
    }
  },
  computed: {
    selectedOperator() {
      if (this.itemProp.ctype === 4) {
        return this.sameSelectedOperator
      } else if (this.itemProp.ctype === 3) {
        return this.isSameUnit()
          ? this.sameSelectedOperator
          : this.differentSelectedOperator
      } else {
        return this.differentSelectedOperator
      }
    }
  },
  created() {},
  mounted() {},
  watch: {},
  methods: {
    handleChangejsf(val) {
      console.log(val, "val")
      this.itemProp.jsf = val
      this.$emit("update:itemProp", {
        ...this.itemProp,
        jsf: val
      })
      console.log(this.itemProp)
    },
    handleDragEnd() {
      this.$emit("handleDragEnd")
    },
    handleDrop(e, index, type) {
      console.log(this.parentIndex, "this.parentIndex")
      this.$emit("handleDrop", e, index, this.parentIndex, type)
    },
    handleDragStart(e, item) {
      console.log(e, item, ">>>>>>>>>>>>>>>>>>>>")
      if (this.itemProp.ctype === 1) {
        this.show = false
      }
      console.log("内部拖拽开始")
      this.$emit("handleDragStart", e, item, this.parentIndex, this.index)
    },
    async handleChange(value, wdz = "") {
      console.log(value, "value")
      console.log(
        this.form.tempStepDimensionList,
        "this.form.tempStepDimensionList"
      )
      const index = this.form.tempStepDimensionList.findIndex(
        e => e.wdzd === value
      )
      let dimension = this.dimensionList.find(e => e.dimCol === value)
      console.log(dimension, "dimension")

      this.$set(this.form.tempStepDimensionList, index, {
        ...this.form.tempStepDimensionList[index],
        ...dimension,
        lxbm: dimension.dimType,
        wdzd: dimension.dimCol,
        zdmc: dimension.dimName,
        wdz,
        dimValueList: []
      })
      console.log(
        this.form.tempStepDimensionList[index],
        ">>>>>>>>>>>>>>>>>>>>>>>>>>>"
      )
    },
    handleSave() {
      this.$refs.ruleForm.validate(async valid => {
        if (valid) {
          this.itemProp.stepDimensionList = cloneDeep(
            this.form.tempStepDimensionList
          )
          this.showPopover = false
          this.$emit("dimensionChanged")
        } else {
          return false
        }
      })
    },
    // 删除维度
    handleDelete(index) {
      this.form.tempStepDimensionList.splice(index, 1)
    },
    // 添加维度
    addDimension() {
      console.log(this.form.tempStepDimensionList.length)
      console.log(this.dimensionList.length)

      if (
        this.form.tempStepDimensionList.length === this.dimensionList.length
      ) {
        return this.$message({
          message: "暂无派生维度可选择",
          type: "warning"
        })
      }
      this.form.tempStepDimensionList.push({
        id: null,
        compositeid: null,
        stepid: null,
        lxbm: "",
        stepIndCode: this.itemProp.stepIndCode,
        wdid: null,
        wdzd: "",
        tabid: "",
        zdmc: "",
        wdz: "",
        dimValueList: [],
        cjr: "",
        createBy: null,
        createTime: "",
        updateBy: null,
        updateTime: ""
      })
    },
    // 获取维度
    async getSelectDimension() {
      const dimList = this.mapDimensions[this.itemProp.stepIndCode]
      if (!Array.isArray(dimList)) {
        this.$message.warning("维度数据加载中，请稍后重试")
        return
      }
      this.showPopover = true
      this.dimensionList = dimList
      this.form.tempStepDimensionList = cloneDeep(
        this.itemProp.stepDimensionList
      )
      if (this.form.tempStepDimensionList.length) {
        this.form.tempStepDimensionList.forEach(item => {
          if (item.wdid) {
            this.handleChange(item.wdid, item.wdz)
          }
        })
      }
      this.$emit("clickAdd", this.index)
    },
    // 是否单位相同
    isSameUnit() {
      let preIndicator = null
      let nextIndicator = null
      console.log(this.index)
      // 查找前一个 指标
      for (let i = this.index - 1; i >= 0; i--) {
        if (this.steps[i].ctype === 1 || this.steps[i].ctype === 4) {
          preIndicator = this.steps[i]
          break
        }
      }

      // 查找后一个 指标
      for (let i = this.index + 1; i < this.steps.length; i++) {
        if (this.steps[i].ctype === 1 || this.steps[i].ctype === 4) {
          nextIndicator = this.steps[i]
          break
        }
      }
      if (preIndicator?.ctype !== nextIndicator?.ctype) {
        return true
      }

      if (preIndicator?.indicatorUnit && nextIndicator?.indicatorUnit) {
        let isSome = preIndicator.indicatorUnit === nextIndicator.indicatorUnit
        if (
          (!isSome && this.itemProp.jsf === "+") ||
          (!isSome && this.itemProp.jsf === "-")
        ) {
          this.itemProp.jsf = ""
        }
        return isSome
      } else {
        return false
      }
    }
  }
}
</script>

<style scoped lang="scss">
.indicator-wrap {
  position: relative;
  display: flex;
  align-items: center;
}
.connector-line {
  position: relative;

  width: 23px;
  height: 20px;
  &.left {
    &::after {
      content: "";
      position: absolute;
      right: 0;
      top: 6px;
      width: 4px;
      height: 8px;
      background: #ff800e;
      border-radius: 4px 0 0 4px;
    }
    &::before {
      content: "";
      position: absolute;
      right: 3px;
      top: 9px;
      width: 20px;
      height: 1px;
      border: 1px solid #ff800e;
    }
    &.isArithmetic {
      &::after {
        background: #00cc88;
      }
      &::before {
        border: 1px solid #00cc88;
      }
    }
  }
  &.right {
    &::after {
      content: "";
      position: absolute;
      left: 0;
      top: 6px;
      width: 4px;
      height: 8px;
      background: #ff800e;
      border-radius: 0 4px 4px 0;
    }
    &::before {
      content: "";
      position: absolute;
      left: 3px;
      top: 9px;
      width: 20px;
      height: 1px;
      border: 1px solid #ff800e;
    }
    &.isArithmetic {
      &::after {
        background: #00cc88;
      }
      &::before {
        border: 1px solid #00cc88;
      }
    }
  }
}
.indicator-item-before {
  width: 40px;
  height: 56px;
  background: #e5f9f3;
  border: 1px dashed #00cc88;
  border-radius: 6px;
  margin: 0 4px;
  background: url("~@/assets/images/drag-enter.png") no-repeat center;
  background-size: 21px 20px;
}
.indicator-item-after {
  width: 40px;
  height: 56px;
  background: #e5f9f3;
  border-radius: 6px;
  border: 1px dashed #00cc88;
  background: url("~@/assets/images/drag-enter.png") no-repeat center;
  background-size: 21px 20px;

  margin: 0 4px;
}

.drag-indicator-item {
  position: relative;
  width: 160px;
  height: 56px;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #d7d9db;
  text-align: center;
  padding: 12px 16px;
  box-sizing: border-box;
  p {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #060607;
    line-height: 14px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  .add,
  .numbers {
    font-size: 11px;
    cursor: pointer;
    height: 11px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 11px;
    line-height: 11px;
    margin-top: 7px;
    color: #979da6;
  }
  .add {
    color: #1563ff;
  }
  &.moving {
    border: 1px dashed #cbced1;
  }
  &.active {
    border: 1px solid #1563ff;
  }
}
.connector {
  ::v-deep .el-input--small .el-input__inner {
    width: 50px;
    height: 20px;
    border: none;
    padding: 0;
    text-align: center;

    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #ff800e;

    border: 1px solid #ff800e;
    &::placeholder {
      position: relative;
      top: -2px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 11px;

      color: #ff800e;
    }
  }
  &.isArithmetic {
    ::v-deep .el-input--small .el-input__inner {
      color: #00cc88;
      border: 1px solid #00cc88;

      &::placeholder {
        color: #00cc88;
      }
    }
  }
  ::v-deep .el-select .el-input .el-select__caret {
    display: none;
  }
}
::v-deep .el-form-item--small.el-form-item {
  margin-bottom: 8px;
}
</style>

<style lang="scss">
.el-popover--plain {
  padding: 0;
  .popover-title {
    padding-left: 16px;
    height: 40px;
    line-height: 40px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.88);
    text-align: left;
    font-style: normal;
    box-sizing: border-box;
    border-bottom: 1px solid #ebedf0;
  }
  .popover-content {
    height: 200px;
    padding: 20px 16px;
    .indicators-name {
      display: flex;
      margin-bottom: 24px;
      .label {
        width: 70px;
        height: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.88);
        line-height: 14px;
        text-align: left;
        font-style: normal;
      }
      .value {
        height: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #000000;
        line-height: 14px;
        text-align: left;
        font-style: normal;
      }
    }
    .dimension {
      .dimension-list {
        height: 120px;
        overflow: auto;
        //滚动条样式
        &::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 6px;
        }
      }
      .label {
        height: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.88);
        line-height: 14px;
        text-align: left;
        font-style: normal;
        display: flex;
        align-items: center;
        .minus {
          width: 21px;
          height: 20px;
          background: url("~@/assets/images/minus.png") no-repeat center;
          background-size: cover;
          margin-left: 15px;
          margin-right: 7px;
          cursor: pointer;
        }
        .plus {
          width: 21px;
          height: 20px;
          background: url("~@/assets/images/add1.png") no-repeat center;
          background-size: cover;
          cursor: pointer;
        }
      }
      .dimension-item {
        display: flex;
        align-items: center;
        margin-top: 8px;
        .minus {
          width: 21px;
          height: 20px;
          background: url("~@/assets/images/minus.png") no-repeat center;
          background-size: cover;
          margin-left: 15px;
          margin-right: 7px;
          cursor: pointer;
          margin-top: -8px;
        }
        .plus {
          width: 21px;
          height: 20px;
          background: url("~@/assets/images/add1.png") no-repeat center;
          background-size: cover;
          cursor: pointer;
          margin-top: -8px;
        }
      }
    }
  }
  .popover-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 52px;
    padding: 0 16px;
    box-sizing: border-box;
    border-top: 1px solid #ebedf0;
    button {
      margin-left: 10px;
    }
  }
}
</style>
