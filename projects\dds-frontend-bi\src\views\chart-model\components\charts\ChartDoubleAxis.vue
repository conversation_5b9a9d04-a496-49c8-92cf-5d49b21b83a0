<template>
  <div class="chart-double-axis" :style="{ width, height }">
    <div ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'ChartDoubleAxis',
  props: {
    // 基础属性
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    // 图表数据
    chartData: {
      type: Array,
      default: () => []
    },
    // 新格式数据支持
    categories: {
      type: Array,
      default: () => []
    },
    series: {
      type: Array,
      default: () => []
    },
    // 字段配置
    xField: {
      type: String,
      default: 'name'
    },
    yField: {
      type: Array,
      default: () => ['value1', 'value2']
    },
    seriesName: {
      type: Array,
      default: () => ['系列1', '系列2']
    },
    // 样式配置
    colors: {
      type: Array,
      default: () => [
        '#2361DB',
        '#0EACCC',
        '#1DB35B',
        '#FFC508',
        '#FF742E',
        '#F5427E',
        '#AA51D6',
        '#77D2E5'
      ]
    },
    // 图表类型配置
    chartTypes: {
      type: Array,
      default: () => ['bar', 'line'] // 第一个系列为柱状图，第二个系列为折线图
    },
    // 显示配置
    showLabel: {
      type: Boolean,
      default: false
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    showGrid: {
      type: Boolean,
      default: true
    },
    showTooltip: {
      type: Boolean,
      default: true
    },
    // 动画配置
    animation: {
      type: Boolean,
      default: true
    },
    // 是否格式化X轴label
    isFormatterXAxis: {
      type: Boolean,
      default: true
    },
    animationDuration: {
      type: Number,
      default: 1000
    },
    // 自定义配置
    customOption: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      chart: null
    }
  },
  computed: {
    chartOption () {
      // 判断使用哪种数据格式
      const useNewFormat = this.categories.length > 0 && this.series.length > 0

      let xData, seriesData, legendData

      if (useNewFormat) {
        // 新格式：categories + series
        xData = this.categories
        seriesData = this.series
        legendData = this.series.map(item => item.name)
      } else {
        // 旧格式：chartData + xField + yField
        xData = this.chartData.map(item => item[this.xField])
        const series1Data = this.chartData.map(item => item[this.yField[0]])
        const series2Data = this.chartData.map(item => item[this.yField[1]])
        seriesData = [
          {
            name: this.seriesName[0],
            type: this.chartTypes[0] || 'bar',
            data: series1Data
          },
          {
            name: this.seriesName[1],
            type: this.chartTypes[1] || 'line',
            data: series2Data
          }
        ]
        legendData = this.seriesName
      }

      const option = {
        title: {
          show: false
        },
        tooltip: {
          trigger: 'axis',

          className: 'echarts-tooltip-diy'
        },

        legend: {
          show: true,
          left: 'center',
          top: 0,
          itemGap: 20,
          itemWidth: 12,
          itemHeight: 8,

          orient: 'horizontal',
          // color: this.color,
          textStyle: {
            color: '#646566',
            fontSize: 12
          },
          data: legendData
        },
        grid: {
          top: 40,
          // bottom: this.showDataZoom ? 12 : 0,
          bottom: 0,
          left: 0,
          right: 0,
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xData,
          axisPointer: {
            type: 'shadow'
          },
          axisLine: {
            lineStyle: {
              color: '#d9d9d9'
            }
          },
          axisLabel: {
            fontSize: 12,
            lineHeight: 16,
            color: '#323233',
            fontWeight: '400',
            interval: 0,
            rotate: 0, // 倾斜角度
            formatter: this.isFormatterXAxis
              ? value => {
                  let startName = value.substring(0, 5)
                  let endName = value.substring(5)
                  if (endName.length > 5) {
                    return `${startName}\n${value.substring(5, 9)}...`
                  }
                  return `${startName}\n${endName}`
                }
              : value => value
          },
          axisTick: {
            alignWithLabel: true
          }
        },
        yAxis: [
          {
            type: 'value',
            position: 'left',
            axisLine: {
              show: false,
              lineStyle: {
                color: this.colors[0]
              }
            },
            splitLine: {
              lineStyle: {
                color: '#EBEDF0',
                type: 'dashed'
              }
            },
            axisLabel: {
              fontSize: 12,
              color: '#646566',
              fontWeight: '400'
              // formatter: metricAxisLabelFormatter,
            }
          },
          {
            type: 'value',
            position: 'right',
            splitLine: {
              show: false,
              lineStyle: {
                color: '#EBEDF0',
                type: 'dashed'
              }
            },
            axisLabel: {
              fontSize: 12,
              color: '#646566',
              fontWeight: '400'
              // formatter: metricAxisLabelFormatter,
            }
          }
        ],
        series: seriesData.map((seriesItem, seriesIndex) => ({
          name: seriesItem.name,
          type: seriesItem.type || (seriesIndex === 0 ? 'bar' : 'line'),
          yAxisIndex: seriesIndex,
          data: seriesItem.data,
          barWidth: 16,
          lineStyle:
            seriesItem.type === 'line'
              ? {
                  color: this.colors[seriesIndex % this.colors.length],
                  width: 2
                }
              : undefined,
          itemStyle: {
            color: this.colors[seriesIndex % this.colors.length],
            borderRadius: seriesItem.type === 'bar' ? [4, 4, 0, 0] : 0
          },
          symbol: seriesItem.type === 'line' ? 'circle' : undefined,
          symbolSize: seriesItem.type === 'line' ? 4 : undefined,
          label: {
            show: this.showLabel,
            position: 'top',
            color: '#666666',
            fontSize: 12
          }
        })),
        animation: this.animation,
        animationDuration: this.animationDuration,
        animationEasing: 'cubicOut'
      }

      // 合并自定义配置
      return this.mergeOption(option, this.customOption)
    }
  },
  watch: {
    chartData: {
      handler () {
        this.updateChart()
      },
      deep: true
    },
    chartOption: {
      handler () {
        this.updateChart()
      },
      deep: true
    }
  },
  mounted () {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeUnmount () {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initChart () {
      if (!this.$refs.chartContainer) return

      this.chart = echarts.init(this.$refs.chartContainer)
      this.updateChart()

      // 绑定点击事件
      this.chart.on('click', params => {
        this.$emit('chart-click', params)
      })

      // 绑定双击事件
      this.chart.on('dblclick', params => {
        this.$emit('chart-dblclick', params)
      })

      // 绑定鼠标悬停事件
      this.chart.on('mouseover', params => {
        this.$emit('chart-mouseover', params)
      })

      // 绑定鼠标离开事件
      this.chart.on('mouseout', params => {
        this.$emit('chart-mouseout', params)
      })
    },
    updateChart () {
      if (!this.chart) return

      this.chart.setOption(this.chartOption, true)
    },
    handleResize () {
      if (this.chart) {
        this.chart.resize()
      }
    },
    mergeOption (target, source) {
      if (!source || typeof source !== 'object') return target

      const result = { ...target }

      Object.keys(source).forEach(key => {
        if (
          source[key] &&
          typeof source[key] === 'object' &&
          !Array.isArray(source[key])
        ) {
          result[key] = this.mergeOption(result[key] || {}, source[key])
        } else {
          result[key] = source[key]
        }
      })

      return result
    },
    // 公共方法
    getChart () {
      return this.chart
    },
    getOption () {
      return this.chart ? this.chart.getOption() : null
    },
    getDataURL (opts) {
      return this.chart ? this.chart.getDataURL(opts) : null
    },
    resize () {
      if (this.chart) {
        this.chart.resize()
      }
    },
    clear () {
      if (this.chart) {
        this.chart.clear()
      }
    },
    dispose () {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    }
  }
}
</script>

<style scoped>
.chart-double-axis {
  position: relative;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
