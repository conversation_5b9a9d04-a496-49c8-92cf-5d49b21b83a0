<template>
  <div class="tag-container">
    <!-- 标签容器 - 固定宽度200px -->
    <div class="tags-wrapper" ref="tagsWrapper">
      <!-- 显示的标签 -->
      <div v-for="(tag, index) in visibleTags" :key="index" class="tag">
        {{ tag }}
      </div>

      <!-- 溢出标签的汇总显示 -->

      <el-tooltip
        v-if="showMoreTag"
        class="item"
        effect="light"
        placement="top"
      >
        <div slot="content">
          <el-tag
            v-for="(tag, index) in hiddenTags"
            :key="index"
            type="primary"
            style="margin-left: 8px;"
          >
            {{ tag }}
          </el-tag>
        </div>
        <div
          class="tag more-tag"
          @mouseenter="showTooltip = true"
          @mouseleave="showTooltip = false"
        >
          +{{ hiddenTags.length }}
        </div>
      </el-tooltip>
    </div>
  </div>
</template>

<script>
import { getTextWidth } from '@/utils'
export default {
  name: 'DynamicTagList',
  props: {
    // 标签数据列表
    tags: {
      type: Array,
      required: true,
      default: () => []
    }
  },
  data () {
    return {
      visibleTags: [], // 可见的标签
      hiddenTags: [], // 隐藏的标签
      showMoreTag: false, // 是否显示汇总标签
      showTooltip: false // 是否显示提示框
    }
  },
  watch: {
    // 监听标签数据变化，重新计算可见标签
    tags: {
      handler (val) {
        console.log(val)
        this.$nextTick(() => {
          this.calculateVisibleTags()
        })
      },
      deep: true,
    }
  },
  mounted () {
          this.calculateVisibleTags()

  },
  beforeUnmount () {
    // 移除事件监听
  },
  methods: {
    // 计算可见标签和隐藏标签
    calculateVisibleTags () {
      if (!this.tags.length) {
        this.visibleTags = []
        this.hiddenTags = []
        this.showMoreTag = false
        return
      }

      // 获取容器和标签元素
      const container = this.$refs.tagsWrapper
      console.log(container,'container')
      if (!container) return

    

      let totalWidth = 0
      const visible = []
      const hidden = []
      const moreTagWidth = 40 // 汇总标签的预估宽度

      // 逐个添加标签并检查宽度
      for (const tag of this.tags) {
        // 22 是padding+border    8是gap间距  
        const tagWidth = getTextWidth(tag,12)+22+8
        // console.log(tempTag.width)
        console.log(tagWidth, totalWidth)

        // 检查是否超出容器宽度（预留汇总标签的空间）
        if (
          totalWidth + tagWidth + (visible.length < this.tags.length - 1 ? moreTagWidth : 0) <=
          200
        ) {
          visible.push(tag)
          totalWidth += tagWidth
        } else {
          hidden.push(tag)
        }
      }


      // 更新数据
      this.visibleTags = visible
      this.hiddenTags = hidden
      this.showMoreTag = hidden.length > 0
    }
  },

}
</script>

<style scoped>
.tag-container {
  position: relative;
  width: 200px;
}

.tags-wrapper {
  width: 200px;
  display: flex;
  gap: 8px;
  overflow: hidden;
  padding: 4px;
  border-radius: 4px;
}

.tag {
  padding: 0px 10px;
  background-color: #ebf1fe;
  border-radius: 4px;
  font-size: 12px;
  color: #3875f6;
  white-space: nowrap;
  border:1px solid #d7e3fd;
}

.more-tag {
  background-color: #e0e7ff;
  color: #4263eb;
  cursor: pointer;
}

.tooltip {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 8px;
  z-index: 100;
}

.tooltip-content {
  background-color: #333;
  color: white;
  padding: 8px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tooltip-tag {
  font-size: 12px;
  padding: 2px 4px;
}
</style>
