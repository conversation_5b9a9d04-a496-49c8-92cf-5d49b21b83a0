<template>
  <DT-View
    :inner-style="{
      padding: 0,
      position: 'relative',
      height: '100%'
    }"
    :outer-style="{
      padding: isFull ? 0 : '20px'
    }"
    class="container-wrapper indicator-algorithm-page"
  >
    <div class="create-head">
      <div class="create-text">创建指标算法</div>
      <div class="steps">
        <div class="step-item active">
          <i class="el-icon-edit"></i>
          Python
        </div>
        <div class="line" :class="{ active: activeId > 1 }"></div>
        <div class="step-item" :class="{ active: activeId > 1 }">
          <i class="el-icon-data-line"></i>
          指标与维度
        </div>
      </div>
      <div class="env-container" v-if="activeId === 1">
        <div class="env-item">
          <div class="env-label">依赖环境</div>
          <el-select
            v-model="form.environmentCode"
            placeholder="请选择"
            @change="handleEnvironment"
            clearable
          >
            <el-option
              v-for="item in environmentList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
        <div class="env-info" v-if="form.environmentCode">
          <div class="env-label">编译器:</div>
          <div class="env-value">{{ form.environmentCode }}</div>
          <el-button plain size="mini" type="primary">更多环境信息</el-button>
        </div>
      </div>
      <el-button
        v-if="activeId === 1 && editId"
        plain
        size="mini"
        type="primary"
        style="margin-left: auto"
        @click="editHistory"
      >
        编辑历史
      </el-button>
    </div>
    <div class="editor-content" v-if="activeId === 1">
      <div class="left">
        <el-form label-width="75px" label-position="top">
          <el-form-item prop="sourceId" label="数据源:">
            <el-select @change="sourceDb" v-model="sourceId" placeholder="请选择数据源" disabled>
              <el-option
                v-for="item in sourcesList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="数据库名称:">
            <el-tree
              :props="defaultProps"
              class="tree-db"
              :load="loadNode"
              :data="dbs"
              node-key="id"
              @node-click="handleNodeClick"
              lazy
            >
              <span class="custom-tree-node-alg" slot-scope="{ node }">
                <span v-show="node.data.id == 'load-more-id'">
                  <span class="tree-node-label">
                    <el-link>{{ node.data.label }}</el-link>
                  </span>
                </span>
                <template v-if="node.level == 1 && node.data.id != 'load-more-id'">
                  <span>
                    <i class="el-icon-s-grid" />
                    {{ node.data.id }}
                    <br /><span class="table-name">{{ node.data.c }}</span>
                  </span>
                </template>
                <template v-if="node.level == 2 && node.data.id != 'load-more-id'">
                  <div
                    style="
                      display: flex;
                      justify-content: space-between;
                      align-items: center;
                      width: 100%;
                    "
                  >
                    {{ node.data.id }}<span style="padding-right: 18px">{{ node.data.bt }}</span>
                  </div>
                </template>
              </span>
            </el-tree>
          </el-form-item>
        </el-form>
      </div>
      <div class="right">
        <div class="top-content">
          <div class="editor-pane">
            <MonacoEditor
              ref="MonacoEditor"
              :init-value.sync="form.pyScript"
              :hint-data="hintData"
              height="100%"
              language="python"
            />
          </div>
          <div class="config-pane">
            <div class="config-content">
              <div class="form-section">
                <h3>
                  {{ algorithmPackages.length ? "算法包" : "已调用算法包" }}
                  <el-button
                    plain
                    size="mini"
                    type="primary"
                    icon="el-icon-plus"
                    @click="openAlgorithmAttr"
                  ></el-button>
                </h3>
              </div>
              <div v-if="algorithmPackages.length" class="section-item">
                <div class="section-container" v-for="pkg in algorithmPackages" :key="pkg.id">
                  <h3 @click="toggleCollapse(pkg.id)" class="collapsible-header">
                    <div>
                      <span class="param-title">{{ pkg.algorithmName }}</span>
                      <span class="param-version">{{ pkg.version }}</span>
                    </div>
                    <div class="section-right-btns">
                      <i
                        :class="['el-icon-arrow-down', { 'is-rotate': collapsedStates[pkg.id] }]"
                      ></i>
                      <el-button
                        type="primary"
                        size="mini"
                        plain
                        icon="el-icon-minus"
                        @click.stop="handleMinusClick(pkg.id)"
                      >
                      </el-button>
                    </div>
                  </h3>
                  <div v-show="!collapsedStates[pkg.id]">
                    <div class="item">
                      <div class="item-label">入参</div>
                      <div
                        class="item-content-container"
                        v-for="(param, paramIndex) in pkg.algorithmParamList"
                        :key="paramIndex"
                      >
                        <div class="item-content-header">
                          <span>{{ param.paramName }}</span>
                          <i
                            class="el-icon-document-copy copy-icon"
                            @click="copyContent(param.paramName)"
                          ></i>
                        </div>
                        <div class="item-content">{{ param.paramIntroduction }}</div>
                      </div>
                    </div>
                    <div class="item">
                      <div class="item-label">输出</div>
                      <div class="item-content-container">
                        <div class="item-content">{{ pkg.outputDescription }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="section-item">
                <div class="no-item">
                  <el-button plain size="large" type="primary" @click="openAlgorithmAttr">
                    从算法库中添加
                  </el-button>
                  <div>左侧import的算法会直接添加到此处</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="middle-content">
          <div class="tip-container">
            <div class="tip-left" v-if="form.pyScript">
              <el-form :model="form" :rules="rules" ref="mainMethodForm">
                <el-form-item prop="mainMethodName" label="主函数名称：" label-width="110px">
                  <el-input
                    placeholder="请输入"
                    v-model="form.mainMethodName"
                    style="width: 200px"
                    clearable
                  ></el-input>
                </el-form-item>
              </el-form>
            </div>
            <div class="tip-right">
              <div class="tip-item">
                <i class="el-icon-warning-outline tip-icon"></i>
                <span class="tip-text">输出说明：</span>
              </div>
              <div class="tip-item">
                -为了让算法结果能够录入指标库，最终的输出结果需为一张表格，点击下方的"试计算"且本编译器能渲染后，可以进入下-下一步实现算法的指标信息配置
              </div>
              <div class="tip-item">-为了让生成的指标具有更多的扩展性，建议输出尽量宽的表格</div>
            </div>
          </div>
          <div v-if="tableData.length || tableColumns.length" class="result">
            <CommonTable
              :page.sync="page"
              id="xh"
              :table-data="tableData"
              :show-batch-tag="false"
              :table-columns.sync="tableColumns"
              @onload="handleTryCalc"
              @handleSortChange="sortChange"
              @handleExport="handleExportExcel"
              @handleAllExport="handleAllExport"
            ></CommonTable>
          </div>
        </div>

        <div class="footer-btn">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="success" :disabled="!form.pyScript" @click="handleTryCalc">
            试计算
          </el-button>
          <el-button type="primary" :disabled="!tableData.length" @click="handleNextStep1">
            下一步
          </el-button>
        </div>
      </div>
    </div>
    <!-- activeId === 2 -->
    <el-form
      status-icon
      :model="ruleForm"
      ref="middleForm"
      label-width="0"
      class="middleForm"
      v-if="activeId === 2"
    >
      <div class="step-two">
        <div class="algorithm-info-form">
          <el-form
            :model="algorithmInfo"
            ref="algorithmInfoForm"
            label-width="120px"
            class="algorithm-info-form-content"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="算法名称：" prop="indAlgorithmName">
                  <el-input
                    v-model="algorithmInfo.indAlgorithmName"
                    placeholder="请输入算法名称"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="运行周期：" prop="runCycle">
                  <el-select
                    v-model="algorithmInfo.runCycle"
                    placeholder="请选择运行周期"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in runCycleOption"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="算法介绍：" prop="indAlgorithmIntroduction">
                  <el-input
                    v-model="algorithmInfo.indAlgorithmIntroduction"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入算法介绍"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div class="result-text">输出结果标注：</div>
        </div>
        <el-table
          :data="ruleForm.tableData1"
          style="width: calc(100% - 48px); margin: 24px 24px 0"
          max-height="calc(100% - 300px)"
          class="sqlIndicator-table"
          border
        >
          <el-table-column prop="zd" label="字段">
            <template #default="{ row }">
              {{ row.field }}
            </template>
          </el-table-column>
          <el-table-column prop="tagType" label="标记类型">
            <template #default="{ row }">
              <el-select v-model="row.tagType" placeholder="请选择" :disabled="!row.isNewlyAdded">
                <el-option
                  v-for="item in tagOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  :disabled="item.value === 'time' && hasTimeDimension && row.tagType !== 'time'"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>

          <el-table-column prop="wb" label="对应维度" width="260">
            <template #default="{ row }">
              <el-popover
                placement="right"
                width="250"
                trigger="hover"
                @after-leave="resetNoCoverShowCount(row)"
                v-if="row.noCoverValues.length && ['ps', 'indOrDim', 'time'].includes(row.tagType)"
              >
                <div>
                  <div class="popover-title">无法覆盖以下字段：</div>
                  <div class="popover-content">
                    <div
                      v-for="field in row.noCoverValues.slice(0, row.noCoverShowCount || 5)"
                      :key="field"
                    >
                      {{ field }}
                    </div>
                    <el-button
                      v-if="row.noCoverValues.length > (row.noCoverShowCount || 5)"
                      type="text"
                      @click.stop="loadMoreNoCover(row)"
                    >
                      继续查看
                    </el-button>
                  </div>
                </div>
                <i
                  class="el-icon-warning"
                  style="margin-right: 5px; font-size: 20px; color: #e6a23c; cursor: pointer"
                  slot="reference"
                ></i>
              </el-popover>
              <el-select-v2
                v-if="['ps', 'indOrDim', 'time'].includes(row.tagType) && !row.newCreate"
                v-model="row.definitionCode"
                :options="allDimTreeList"
                :props="{
                  label: 'dimName',
                  value: 'definitionCode'
                }"
                filterable
                placeholder="请选择"
                :disabled="!row.isNewlyAdded"
                @change="getDimLevels($event, row)"
                clearable
              >
                <template #default="{ item }">
                  <p style="padding: 0 17px; display: flex; justify-content: space-between">
                    <span v-tooltip-content="180">{{ item.dimName }}</span>
                    <span>{{ item.version }}</span>
                  </p>
                </template>
              </el-select-v2>
              <span
                v-if="row.newCreate && ['ps', 'indOrDim', 'time'].includes(row.tagType)"
                style="display: flex; align-items: center"
              >
                <span v-tooltip-content="130"> {{ row.dimName }}({{ row.version }}) </span>
                <el-button type="text" @click="removeDim(row)">移除</el-button>
                <el-button type="text" @click="editDim(row)">编辑</el-button>
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="wb" label="维度层级" width="220">
            <template #default="{ row }">
              <el-select
                v-if="
                  ['ps', 'indOrDim', 'time'].includes(row.tagType) && row.definitionCode != 'self'
                "
                v-model="row.levelCode"
                placeholder="请选择"
                :disabled="!row.isNewlyAdded"
                @change="onLevelCodeChange(row)"
              >
                <el-option
                  v-for="item in dimLevelMap[row.definitionCode]"
                  :key="item.levelCode"
                  :label="item.levelName"
                  :value="item.levelCode"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>

        <div class="step-btn">
          <el-button @click="handleCancel"> 取消 </el-button>
          <el-button size="small" @click="activeId = activeId = 1"> 上一步 </el-button>
          <el-button size="small" type="primary" :disabled="false" @click="handleNextStep2">
            下一步
          </el-button>
        </div>
      </div>
    </el-form>
    <el-form
      status-icon
      :rules="rules"
      :model="ruleForm"
      ref="ruleForm"
      label-width="0"
      class="ruleForm"
      v-if="activeId === 3"
    >
      <div class="step-two">
        <el-table
          :data="ruleForm.tableData2"
          style="width: calc(100% - 48px); margin: 24px 24px 0; overflow-x: auto"
          max-height="calc(100% - 300px)"
          class="sqlIndicator-table"
        >
          <el-table-column prop="indName" label="指标名称" width="250">
            <template slot-scope="scope">
              <el-form-item
                :ref="'indName' + scope.row.index"
                class="is-required"
                :prop="'tableData2.' + scope.$index + '.indName'"
                :rules="rules.indName"
                style="margin-bottom: 0"
              >
                <el-input placeholder="" v-model="scope.row.indName"></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="val1" label="计算方式" width="260">
            <template #default="{ row }">
              <div style="display: flex">
                <el-select
                  v-model="row.execType"
                  placeholder="请选择"
                  :style="{
                    width: row.execType === 'sort' ? '76px' : '240px'
                  }"
                >
                  <el-option
                    v-for="(item, index) in jsfsList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <template v-if="row.execType === 'sort'">
                  <el-select
                    style="margin-left: 10px"
                    v-model="row.sortType"
                    placeholder="排序方式"
                    :style="{ width: '76px' }"
                    class="myselect"
                  >
                    <el-option
                      v-for="(item, index) in sortTypeList"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                  <el-select
                    style="margin-left: 10px"
                    v-model="row.sortRange"
                    v-if="row.execType === 'sort'"
                    :style="{ width: '76px' }"
                    class="myselect"
                  >
                    <el-option
                      v-for="(item, index) in sortDefineList"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                  <el-input
                    :style="{ width: '76px' }"
                    style="margin-left: 10px"
                    placeholder="数值"
                    v-if="row.sortRange === 'top'"
                    v-model.number="row.sortLimit"
                    clearable
                  ></el-input>
                </template>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="period" label="计算周期" width="100">
            <template #default="{ row }">
              <el-select
                v-model="row.period"
                placeholder="请选择"
                v-if="row.tagType == 'INT' || row.tagType == 'indOrDim'"
                :key="isFull"
                :popper-append-to-body="!isFull"
              >
                <el-option
                  v-for="item in jszqList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="scopeId" label="所属指标域" width="150">
            <template #default="{ row }">
              <avue-input-tree
                v-if="row.tagType == 'INT' || row.tagType == 'indOrDim'"
                default-expand-all
                v-model="row.scopeId"
                :key="isFull"
                :popper-append-to-body="!isFull"
                :props="{
                  label: 'name',
                  value: 'id'
                }"
                placeholder="请选择归属域"
                :dic="viewGroup"
              ></avue-input-tree>
            </template>
          </el-table-column>
          <el-table-column label="归属部门" width="150">
            <template #default="{ row }">
              <el-cascader clearable v-model="row.deptAllCode" :props="cascaderProps"></el-cascader>
            </template>
          </el-table-column>
          <el-table-column prop="dataFormat" label="数据类型" width="150">
            <template #default="{ row }">
              <el-select v-model="row.dataFormat" placeholder="请选择数据格式">
                <el-option
                  v-for="item in sjgs"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="unitName" label="单位" width="200">
            <template #header>
              单位
              <span style="color: red">*</span>
            </template>
            <template #default="{ row }">
              <div style="display: flex">
                <el-select
                  v-model="row.unitName"
                  placeholder="请选择单位"
                  :key="isFull"
                  :popper-append-to-body="!isFull"
                  :style="{
                    width: row.unitName === '其他' ? '122px' : '250px'
                  }"
                  class="myselect"
                  v-if="row.tagType == 'INT' || row.tagType == 'indOrDim'"
                >
                  <el-option
                    v-for="(item, index) in dwList"
                    :key="index"
                    :label="item.name"
                    :value="item.bm"
                  ></el-option>
                  <el-option label="无单位" :value="null"></el-option>
                </el-select>
                <el-input
                  v-if="row.unitName === '其他'"
                  v-model="row.diydw"
                  style="width: 122px; margin-left: 6px"
                  placeholder="请输入单位"
                ></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column width="150">
            <template #header>
              精度
              <el-tooltip
                class="item"
                effect="dark"
                content="精度的数值代表小数点的位数，此处仅支持输入整数"
                placement="top"
              >
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template #default="{ row }">
              <div
                style="display: flex; align-items: center; width: 150px"
                v-if="row.tagType == 'INT' || row.tagType == 'indOrDim'"
              >
                <el-input
                  style="width: 45px"
                  placeholder=""
                  v-model.number="row.precision"
                ></el-input>
                <el-checkbox
                  :true-label="1"
                  :false-label="0"
                  style="margin-left: 8px"
                  v-model="row.rounding"
                >
                  四舍五入
                </el-checkbox>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="阈值" width="150">
            <template #default="{ row }">
              <div
                style="display: flex; align-items: center"
                v-if="row.tagType == 'INT' || row.tagType == 'indOrDim'"
              >
                <el-input placeholder="" v-model.number="row.thresholdMin"></el-input>
                -
                <el-input placeholder="" v-model.number="row.thresholdMax"></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" width="150">
            <template #default="{ row }">
              <el-input
                v-if="row.tagType == 'INT' || row.tagType == 'indOrDim'"
                placeholder="请输入描述"
                v-model="row.description"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="tag" label="标签" width="150">
            <template #default="{ row }">
              <el-select
                v-if="row.tagType == 'INT' || row.tagType == 'indOrDim'"
                v-model="row.tag"
                filterable
                multiple
                remote
                allow-create
                default-first-option
                @change="(val) => {
                  row.tag = val.filter(item => item.trim() !== '')
                  changeTag(val)

                }"
                @visible-change="handleSelectVisibleChange"
                @remove-tag="removeTag"
                :remote-method="remoteMethod"
                placeholder="请创建或者选择标签"
                :key="isFull"
                :popper-append-to-body="!isFull"
              >
                <el-option
                  v-for="item in formatLabels"
                  :key="item.bqmc"
                  :label="item.bqmc"
                  :value="item.bqmc"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>

        <div class="step-btn">
          <el-button @click="handleCancel"> 取消 </el-button>
          <el-button size="small" @click="activeId = 2">上一步</el-button>
          <el-button
            size="small"
            type="primary"
            :disabled="false"
            :loading="loading"
            @click="handleSave"
          >
            保存
          </el-button>
        </div>
      </div>
    </el-form>
    <el-dialog
      title="添加调用算法"
      :visible.sync="visibleAlgorithm"
      width="60%"
      :before-close="handleAlgorithmDialogClose"
    >
      <div class="algorithm-dialog-content">
        <div class="algorithm-search">
          <el-form :inline="true" :model="algorithmSearchForm" class="algorithm-search-form">
            <el-form-item label="算法名称">
              <el-input
                v-model="algorithmSearchForm.name"
                placeholder="请输入算法名称"
                clearable
                @keyup.enter.native="searchAlgorithms"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="searchAlgorithms">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <el-table
          :data="algorithmList"
          border
          ref="algorithmTable"
          row-key="id"
          @selection-change="handleAlgorithmSelectionChange"
        >
          <el-table-column
            type="selection"
            :selectable="isSelected"
            :reserve-selection="true"
            width="55"
          ></el-table-column>
          <el-table-column prop="algorithmName" label="算法名称" min-width="150"></el-table-column>
          <el-table-column prop="version" label="当前版本" min-width="100"></el-table-column>
          <el-table-column prop="environmentCode" label="支持环境" min-width="150">
            <template slot-scope="scope">
              <el-tag
                v-for="env in processEnvironmentCodes(scope.row)"
                :key="env"
                size="mini"
                style="margin-right: 5px"
              >
                {{ getEnvironmentLabel(env) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <div class="algorithm-pagination">
          <el-pagination
            @size-change="handleAlgorithmSizeChange"
            @current-change="handleAlgorithmCurrentChange"
            :current-page="algorithmPagination.currentPage"
            :page-sizes="[10, 20, 50]"
            :page-size="algorithmPagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="algorithmPagination.total"
          >
          </el-pagination>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="visibleAlgorithm = false">取 消</el-button>
        <el-button type="primary" @click="confirmAlgorithmSelection">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="编辑维度"
      :visible.sync="editDialogVisible"
      width="600px"
      label-width="100px"
      label-position="top"
    >
      <el-form :model="editDimForm" :rules="editDimRules" ref="editDimForm">
        <el-form-item label="维度名称" prop="dimName">
          <el-input v-model="editDimForm.dimName" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="维度类型">
          <el-select
            v-model="editDimForm.categoryCode"
            placeholder="请选择类型"
            style="width: 100%"
          >
            <el-option
              :label="item.label"
              :value="item.value"
              v-for="item in dimensionTypeList"
              :key="item.categoryCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="版本" prop="version">
          <el-input v-model="editDimForm.version" placeholder="请输入版本" />
        </el-form-item>
        <el-form-item label="层级名称" prop="levelName">
          <el-input v-model="editDimForm.levelName" placeholder="请输入层级名称" />
        </el-form-item>
        <el-form-item label="标签">
          <el-input v-model="editDimForm.tags" placeholder="请输入标签" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            type="textarea"
            :rows="4"
            placeholder="请输入描述"
            v-model="editDimForm.description"
          ></el-input>
        </el-form-item>
        <el-form-item label="更新频率">
          <el-select v-model="editDimForm.updateFrequency" placeholder="请选择更新频率">
            <el-option
              v-for="item in updateFrequencys"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="更新方式">
          <el-radio-group v-model="editDimForm.updateType">
            <el-radio :label="0">增量同步</el-radio>
            <el-radio :label="1">全量同步</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveEditDim">确 定</el-button>
      </span>
    </el-dialog>
  </DT-View>
</template>

<script>
import Request from "@/service"
import encryptInd from "@/utils/encryptInd.js"
import { jsonToSheetXlsx } from "@/utils/Export2Excel"
import MonacoEditor from "@/components/MonacoEditor"
import options from "../mixins/options"
import screenfull from "screenfull"
import CommonTable from "@/components/CommonTable.vue"

export default {
  components: { MonacoEditor, CommonTable },
  mixins: [options],

  props: {},
  data() {
    let messageInstance = null
    const resetMessage = (options) => {
      if (messageInstance) messageInstance.close()
      messageInstance = this.$message(options)
    }
    var changeFieldNameModified = async (rule, value, callback) => {
      const arr = this.ruleForm.tableData2.filter((item) => item.indName === value)
      let errorMsg = ""
      if (arr.length > 1) {
        errorMsg = "指标或维度名称已存在,请重新输入"
      }
      if (!value) {
        errorMsg = "请输入指标或维度名称"
      }

      if (this.mode === "edit") {
        return
      }
      const fieldNameParams = {
        indName: value
      }
      const { data } = await Request.algorithm.checkIndNameExistAlgorithmIndicator(fieldNameParams)
      if (data) errorMsg = "指标或维度名称已存在,请重新输入"
      if (errorMsg) {
        resetMessage({ message: errorMsg, type: "warning" })
        callback(new Error(errorMsg))
      } else {
        callback()
      }
    }
    return {
      visibleAlgorithm: false,
      algorithmInfo: {
        indAlgorithmName: "",
        runCycle: "",
        indAlgorithmIntroduction: ""
      },
      algorithmSearchForm: {
        name: ""
      },
      mainMethodNameError: "",
      rules: {
        mainMethodName: [
          { required: true, message: "请输入主函数名称", trigger: "change" },
          {
            validator: this.validateMainMethodName,
            trigger: "change"
          }
        ],
        indName: [
          {
            validator: changeFieldNameModified,
            trigger: "blur",
            required: true
          }
        ],
        unitName: [
          {
            required: true,
            message: "请选择或输入单位",
            trigger: "blur",
            validator: (rule, value, callback) => {
              // 只对度量和度量和维度类型的字段进行单位验证
              const row = this.ruleForm.tableData2.find((item) => item.unitName === value)
              if (row && (row.tagType === "INT" || row.tagType === "indOrDim")) {
                if (!value && value !== null) {
                  callback(new Error("请选择或输入单位"))
                } else {
                  callback()
                }
              } else {
                callback()
              }
            }
          }
        ]
      },
      algorithmList: [],
      selectedAlgorithms: [],
      activeId: 1,
      collapsedStates: {},
      algorithmPackages: [],
      dimOptions: {}, // 每个字段的可选维度
      dataDomainList: [], // 数据列表
      tableInfoList: [],
      calcResults: [], // 尝试计算结果
      initialTableData: [], // 用于存储编辑模式下的初始 tableData
      newlyAddedData: [], // 存储新增的数据
      viewGroup: [{ id: "0", name: "根目录", children: [] }], // 数据域分组
      environmentList: [
        { label: "基础环境", value: "Base" },
        { label: "数据分析环境", value: "DataScience" },
        { label: "AI环境", value: "AIEnv" }
      ],
      tagOptions: [
        {
          value: "ps",
          label: "维度"
        },
        {
          value: "indOrDim",
          label: "度量和维度"
        },
        {
          value: "INT",
          label: "度量"
        },
        {
          value: "time",
          label: "时间维度"
        },
        {
          value: "NULL",
          label: "无"
        }
      ],
      dimLevelMap: {}, // 维度层级
      allDimTreeList: [], // 所有维度树
      editDialogVisible: false,
      editDimForm: {
        dimName: "",
        version: ""
      },
      editDimRules: {
        dimName: [{ required: true, message: "请输入维度名称", trigger: "blur" }],
        version: [{ required: true, message: "请输入版本", trigger: "blur" }],
        levelName: [{ required: true, message: "请输入层级名称", trigger: "blur" }]
      },
      sourceId: null,
      editRow: null,
      updateFrequencys: [
        { label: "按日", value: 1 },
        { label: "按周", value: 2 },
        { label: "按月", value: 3 },
        { label: "按学期", value: 4 },
        { label: "按学年", value: 5 },
        { label: "按年", value: 6 }
      ],
      runCycleOption: [
        { label: "按日", value: "1" },
        { label: "按周", value: "2" },
        { label: "按月", value: "3" },
        { label: "按学期", value: "4" },
        { label: "按学年", value: "5" },
        { label: "按年", value: "6" }
      ],
      dimensionTypeList: [],
      form: {
        environmentCode: "",
        mainMethodName: "", // 主方法名
        pyScript: "" // python
      },
      tables: [],
      columns: [],
      tableColumns: [],
      tableData: [],
      environmentResults: {},
      executionTime: "",
      uploadPercentage: 0,
      dialogStep: 1,
      ruleForm: {
        tableData1: [],
        tableData2: []
      },
      dialogVisible: false,
      textarea: "",
      sourcesList: [], // 数据源列表

      dbs: [],
      page: {
        total: 0,
        pageSize: 10,
        currentPage: 1
      },
      algorithmPagination: {
        total: 0,
        pageSize: 10,
        currentPage: 1
      },
      defaultProps: {
        label: "name",
        children: "zones",
        isLeaf: "leaf"
      },

      isFull: false, // 是否全屏
      indCode: null,
      editId: null,
      mode: "add",
      editDetailObj: {},
      numericFields: [], // 数值字段
      tryCalculateResVal: "",
      tryCalculateResList: [],
      parameters: [],
      historyVersion: []
    }
  },
  computed: {
    hintData() {
      return [...this.tables.map((item) => item.id), ...this.columns.map((item) => item.id)]
    },
    hasTimeDimension() {
      // 检查tableData1和tableData2是否有“时间维度”
      return (
        (this.ruleForm.tableData2 &&
          this.ruleForm.tableData2.some((item) => item.tagType === "time")) ||
        (this.ruleForm.tableData1 &&
          this.ruleForm.tableData1.some((item) => item.tagType === "time"))
      )
    }
  },
  mounted() {
    this.indCode = this.$route.query.indCode || null
    this.mode = this.$route.query.mode || "add"
    this.editId = this.$route.query.id || null
    console.log(1121212121, this.indCode, this.editId, this.mode)
    if (this.editId) {
      this.getIndDetail()
    }
    screenfull.on("change", () => {
      this.isFull = screenfull.isFullscreen
    })
    this.rules.mainMethodName.push({
      validator: this.validateMainMethodName,
      trigger: "change"
    })
    this.getLabelSelectList()
    this.getBaseUnit()
    this.getAllViewGroup()
    this.getDimTree()
  },
  watch: {},
  methods: {
    // 获取指标详情
    async getIndDetail() {
      const res = await Request.api.paramGet(`algorithmIndicator/${this.editId}`)
      if (res.code === 200) {
        const pyScript = res.data?.indDetail?.pyScript
          ? encryptInd.decrypt(res.data?.indDetail?.pyScript)
          : ""
        this.editDetailObj = res.data
        this.form.pyScript = pyScript
        this.form.mainMethodName = res.data?.indDetail?.mainMethodName
        this.form.environmentCode = res.data?.indDetail?.environmentCode
        this.algorithmInfo.indAlgorithmName = res.data?.indDetail?.indAlgorithmName
        this.algorithmInfo.indAlgorithmIntroduction = res.data?.indDetail?.indAlgorithmIntroduction
        this.algorithmInfo.runCycle = res.data?.runCycle
        if (this.mode === "edit" && pyScript) {
          this.executeTryCalc().then(() => {
            // 保存初始的 tableData
            this.initialTableData = [...this.tableData]
          })
          this.$nextTick(() => {
            if (this.$refs.MonacoEditor) {
              this.$refs.MonacoEditor.setInitValue(pyScript)
            }
          })
        }
      }
    },
    // 新增方法：填充表格数据
    populateTableData(data) {
      // 执行试计算以获取表格结构
      this.$nextTick(() => {
        // 处理维度数据（tableData1）
        if (this.tableColumns && this.tableColumns.length > 0) {
          // 先找出新增字段
          const newFields = this.findNewData()
          const newFieldsSet = new Set(newFields)

          this.ruleForm.tableData1 = this.tableColumns.map((column) => {
            // 默认值
            const rowData = {
              ...column,
              field: column.prop,
              indName: column.prop,
              tagType: "NULL", // 默认为无
              definitionCode: "",
              levelCode: "",
              noCoverValues: [],
              noCoverShowCount: 5,
              newCreate: false,
              isNewlyAdded: newFieldsSet.has(column.prop) // 根据是否为新增字段设置标志
            }

            // 设置为度量
            if (column.prop === data.field) {
              rowData.tagType = "INT"
            } else {
              // 对于其他字段，从dimensions中查找对应的维度信息
              if (data.dimensions && data.dimensions.length > 0) {
                const dimension = data.dimensions.find((dim) => dim.field === column.prop)

                if (dimension) {
                  // 设置维度类型
                  rowData.tagType = dimension.dimensionType || "NULL"
                  // 设置维度定义编码
                  rowData.definitionCode = dimension.definitionCode || ""
                  // 设置维度层级编码
                  rowData.levelCode = dimension.levelCode || ""

                  // 如果有definitionCode，自动获取维度层级信息
                  if (dimension.definitionCode) {
                    // 使用nextTick确保DOM更新后再获取层级信息
                    this.$nextTick(() => {
                      this.getDimLevels(dimension.definitionCode, rowData)
                    })
                  }
                }
              }
            }

            return rowData
          })
        }

        // 处理度量数据（tableData2）
        // 从tableData1中找出标记为INT的行
        this.ruleForm.tableData2 = this.ruleForm.tableData1
          .filter((item) => item.tagType === "INT")
          .map((item) => {
            return {
              ...item,
              indName: data.indName,
              period: data.period || "1",
              scopeId: data.scopeId || "",
              deptAllCode: data.deptId ? data.deptId.split(",").map(Number) : [],
              execType: data.execType || "sum",
              dataFormat: Number(data.dataFormat) || 0,
              unitName: data.unitName || "",
              precision: data.precision || null,
              rounding: data.rounding || 0,
              thresholdMin: data.thresholdMin || null,
              thresholdMax: data.thresholdMax || null,
              description: data.description || "",
              tag: data.tag ? data.tag.split(",") : [],
              runCycle: data.runCycle || "",
              sortType: data.sortType || null,
              sortRange: data.sortRange || null,
              sortLimit: data.sortLimit || null,
              isZeroWarn: data.isZeroWarn || 0,
              zeroWarnTime: data.zeroWarnTime || ""
            }
          })
      })
    },
    // 获取所有数据域分组
    async getAllViewGroup() {
      const { data } = await this.$httpBi.indicatorAnagement.getAllViewGroup()
      this.viewGroup[0].children = data
    },
    // 获取所有维度树
    async getDimTree() {
      const { data } = await Request.api.paramPost("/DimManage/getDimList", {
        pageSize: -1,
        dimName: "",
        pageNum: 1
      })
      this.allDimTreeList =
        [
          {
            dimName: "自身维度创建",
            definitionCode: "self",
            value: ""
          },
          ...(data.list || [])
        ] || []
    },
    // 获取无覆盖维度
    async getNoCoverDimValues(row) {
      const { data } = await Request.api.paramPost("/DimManage/getNoCoverDimValues", {
        levelCode: row.levelCode,
        fieldCode: row.zddm,
        algorithmCalcResult: this.tableData,
        lxbm: "sf"
      })
      row.noCoverValues = data
    },
    // 匹配相似维度和层级
    matchDim(params) {
      return Request.api
        .paramPost("/AtomIndicator/checkDimMatch", params)
        .then((response) => {
          return response.data || []
        })
        .catch((error) => {
          console.error("匹配维度失败:", error)
          return []
        })
    },
    // 获取维度版本
    async getDimLevels(definitionCode, row) {
      console.log(definitionCode, row, "definitionCode>>>>>>>>")
      if (!definitionCode) {
        this.$set(row, "definitionCode", "")
        this.$set(row, "noCoverValues", [])
        this.$set(row, "levelCode", "")
        return
      }
      if (definitionCode === "self") {
        console.log("获取维度版本-------", row)
        await this.createSelfDim(row)
      } else {
        const { data } = await Request.api.paramPostQuery(
          "/DimManage/getDimLevelByDefinitionCode",
          { definitionCode }
        )
        if (data.length) {
          row.levelCode = data[0].levelCode
          row.levelName = data[0].levelName
          this.onLevelCodeChange(row)
        } else {
          row.levelCode = ""
          row.levelName = ""
          this.$message.warning("该维度没有层级")
        }
        this.$set(this.dimLevelMap, definitionCode, data)
      }
    },
    loadMoreNoCover(row) {
      if (!row.noCoverShowCount) {
        this.$set(row, "noCoverShowCount", 10)
      } else {
        this.$set(row, "noCoverShowCount", row.noCoverShowCount + 5)
      }
    },
    resetNoCoverShowCount(row) {
      this.$set(row, "noCoverShowCount", 5)
    },
    async onLevelCodeChange(row) {
      await this.getNoCoverDimValues(row)
    },
    validateMainMethodName(rule, value, callback) {
      if (!value) {
        callback(new Error("请输入主函数名称"))
        return
      }

      // 检查pyScript中是否存在对应的函数定义
      if (this.form.pyScript) {
        const functionRegex = new RegExp(`^\\s*def\\s+${value}\\s*\\(`, "m")
        if (!functionRegex.test(this.form.pyScript)) {
          callback(new Error("主函数名有误"))
          return
        }
      }

      callback()
    },
    // 判断算法是否已经添加，如果已添加则不可选
    isSelected(row) {
      return !this.algorithmPackages.some((pkg) => pkg.id === row.id)
    },
    clickFullscreen() {
      if (!screenfull.enabled) {
        this.$message({
          message: "浏览器不支持全屏功能",
          type: "warning"
        })
        return false
      }
      console.log(screenfull, "screenfull")
      const elment = document.querySelector(".container-wrapper")
      screenfull.toggle(elment)
    },

    openAlgorithmAttr() {
      this.visibleAlgorithm = true
      // 重置分页和搜索条件
      this.algorithmPagination.currentPage = 1
      this.algorithmSearchForm.name = ""
      // 清空当前选中状态
      if (this.$refs.algorithmTable) {
        this.$refs.algorithmTable.clearSelection()
      }
      this.getAlgorithmPackages()
    },
    toggleCollapse(id) {
      this.$set(this.collapsedStates, id, !this.collapsedStates[id])
    },
    // 处理环境代码显示
    processEnvironmentCodes(row) {
      // 只处理字符串和空值情况
      if (typeof row.environmentCode === "string" && row.environmentCode) {
        // 如果是逗号分隔的字符串
        return row.environmentCode
          .split(",")
          .map((code) => code.trim())
          .filter((code) => code)
      } else if (row.algorithmDetail?.environmentCode) {
        // 从 algorithmDetail 中获取，同样只处理字符串情况
        if (typeof row.algorithmDetail.environmentCode === "string") {
          return row.algorithmDetail.environmentCode
            .split(",")
            .map((code) => code.trim())
            .filter((code) => code)
        }
      }
      return []
    },

    handleMinusClick(id) {
      console.log("减号按钮被点击，ID:", id)
      const index = this.algorithmPackages.findIndex((pkg) => pkg.id === id)
      if (index !== -1) {
        this.algorithmPackages.splice(index, 1)
      }
    },
    handleSave() {
      this.loading = true
      // 前置验证单位字段
      const hasEmptyUnit = this.ruleForm.tableData2.some((item) => {
        return (
          (item.tagType === "INT" || item.tagType === "indOrDim") &&
          !item.unitName &&
          item.unitName !== null
        )
      })
      if (hasEmptyUnit) {
        this.$message.error("请为所有度量类型字段选择或输入单位")
        this.loading = false
        return
      }
      const dimensions = this.ruleForm.tableData1
        .filter((item) => item.tagType !== "INT")
        .map((item) => {
          return {
            ...item,
            dimensionCode: item.definitionCode, // 维度编码
            fieldName: item.field, // 字段名称
            dimensionType: item.tagType === "indOrDim" ? "ps" : item.tagType, // 维度类型
            id: this.mode === "edit" ? this.editId : null // 新建的时候id设置为null
          }
        })
      console.log(44445555, this.ruleForm.tableData1, dimensions)
      // 加密 pyScript
      const encryptedPyScript = this.form.pyScript ? encryptInd.encrypt(this.form.pyScript) : ""
      let indDetail = {
        ...this.form,
        indAlgorithmName: this.algorithmInfo.indAlgorithmName,
        indAlgorithmIntroduction: this.algorithmInfo.indAlgorithmIntroduction,
        pyScript: encryptedPyScript,
        toolDetailList: this.algorithmPackages || []
      }
      let paramsFilter = this.ruleForm.tableData2.map((item) => {
        const scopeName = this.getScopeNameById(item.scopeId)
        return {
          ...item,
          thresholdMax: item?.thresholdMax ? Number(item.thresholdMax) : null,
          thresholdMin: item?.thresholdMin ? Number(item.thresholdMin) : null,
          period: item.period ? Number(item.period) : null,
          deptId: item?.deptAllCode?.length ? item.deptAllCode.join() : null,
          indDetail: indDetail, // 指标详情
          dimensions: dimensions, // 维度
          runCycle: this.algorithmInfo.runCycle, // 运行周期
          tag: item?.tag?.length ? item.tag.join() : null,
          scopeName,
          tagType: item?.tagType === "indOrDim" ? "INT" : item.tagType,
          unitName: item?.unitName === "其他" ? item.diydw : item.unitName,
          id: this.mode === "edit" ? this.editId : null
        }
      })
      console.log(paramsFilter, "paramsFilter")
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          const res = await Request.algorithm.saveOrUpdateAlgorithmIndicator(paramsFilter)
          if (res.code === 200) {
            this.$message.success("保存成功")
          }
          this.$router.push({
            path: "/ddsBi/indicatorAnagement",
            query: {}
          })
          this.loading = false
        } else {
          this.loading = false
        }
      })
    },
    copyContent(content) {
      const textarea = document.createElement("textarea")
      textarea.value = content
      document.body.appendChild(textarea)
      textarea.select()
      document.execCommand("copy")
      document.body.removeChild(textarea)
      this.$message.success("复制成功")
    },
    async getAlgorithmPackages() {
      let param = {
        pageSize: this.algorithmPagination.pageSize,
        pageNum: this.algorithmPagination.currentPage,
        query: this.algorithmSearchForm.name
      }

      this.$dt_loading.show()
      try {
        const res = await Request.algorithm.getAlgorithmList(param)
        if (res.code === 200) {
          this.algorithmList = res.data.list || []
          // this.algorithmList = (res.data.list || []).map((item) => {
          //   if (item.algorithmDetail && typeof item.algorithmDetail === "object") {
          //     return {
          //       ...item,
          //       ...item.algorithmDetail, // 展开 algorithmDetail 的所有字段
          //       environmentCode: item.algorithmDetail.environmentCode
          //         ? item.algorithmDetail.environmentCode.split(",")
          //         : []
          //     }
          //   }
          //   return item
          // })
          this.algorithmPagination.total = res.data.totalCount || 0
        }
      } catch (error) {
        console.error("获取算法列表失败:", error)
        this.algorithmList = []
        this.algorithmPagination.total = 0
      } finally {
        this.$dt_loading.hide()
      }
    },

    searchAlgorithms() {
      this.algorithmPagination.currentPage = 1
      this.getAlgorithmPackages()
    },
    resetSearch() {
      this.algorithmSearchForm.name = ""
      this.algorithmPagination.currentPage = 1
      this.getAlgorithmPackages()
    },
    handleAlgorithmSizeChange(val) {
      this.algorithmPagination.pageSize = val
      this.algorithmPagination.currentPage = 1
      this.getAlgorithmPackages()
    },

    handleAlgorithmCurrentChange(val) {
      this.algorithmPagination.currentPage = val
      this.getAlgorithmPackages()
    },

    handleAlgorithmSelectionChange(selection) {
      console.log(232332, selection)
      this.selectedAlgorithms = selection
    },

    confirmAlgorithmSelection() {
      // 将选择的算法添加到 algorithmPackages 中
      this.selectedAlgorithms.forEach((algorithm) => {
        const exists = this.algorithmPackages.some((pkg) => pkg.id === algorithm.id)
        if (!exists) {
          this.algorithmPackages.push({
            ...algorithm,
            originId: algorithm.id // 保存原始ID用于去重判断
          })
        }
      })
      this.visibleAlgorithm = false
      console.log(3233333, this.algorithmPackages)
    },

    handleAlgorithmDialogClose() {
      this.visibleAlgorithm = false
      // 清空当前页选中状态但保留selectedAlgorithms
      if (this.$refs.algorithmTable) {
        this.$refs.algorithmTable.clearSelection()
      }
    },

    getEnvironmentLabel(value) {
      const env = this.environmentList.find((item) => item.value === value)
      return env ? env.label : value
    },
    handleEnvironment(val) {
      console.log(val)
    },
    // 创建自身维度
    async createSelfDim(row) {
      const { data } = await Request.api.paramPost("/DimManage/addDimBySelf", {
        dimDefinition: {
          categoryCode: "wdlx_jcwd_1930198052817580032",
          dimName: row.field || row.indName,
          description: "",
          version: "v1.0",
          tags: "基于自身创建维度",
          updateFrequency: 1,
          updateType: 0,
          categoryName: "基础维度"
        },
        createType: 1, // 算法指标
        dimLevels: [
          {
            level: 1,
            levelName: row.field || row.indName
          }
        ],
        configs: [],
        calcResults: this.calcResults
      })
      await this.getDimTree()
      // 补充所有维度相关字段，便于后续编辑和回显
      this.$set(row, "definitionCode", data.definitionCode)
      this.$set(row, "dimName", data.dimName)
      this.$set(row, "newCreate", true)
      this.$set(row, "version", data.version)
      this.$set(row, "categoryCode", "wdlx_jcwd_1930198052817580032")
      this.$set(row, "tags", "基于自身创建维度")
      this.$set(row, "description", "")
      this.$set(row, "updateFrequency", 1)
      this.$set(row, "updateType", 0)
      this.getDimLevels(data.definitionCode, row)
    },
    // 删除
    removeDim(row) {
      this.$confirm("是否删除当前维度树数据", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          Request.api
            .paramDel("/DimManage/deleteDimByCode", {
              code: row.definitionCode
            })
            .then(() => {
              this.$message.success("删除成功")
              this.$set(row, "definitionCode", "")
              this.$set(row, "dimName", "")
              this.$set(row, "version", "")
              this.$set(row, "newCreate", false)
              this.$set(row, "levelCode", "")
              this.getDimTree()
            })
        })
        .catch((error) => {
          console.error(error)
        })
    },
    editDim(row) {
      this.editRow = row
      // 针对自身创建维度，回显所有相关字段
      this.editDimForm = {
        definitionCode: row.definitionCode || "",
        dimName: row.dimName || "",
        version: row.version || "",
        categoryCode: row.categoryCode || "",
        levelName: row.levelName || "",
        tags: row.tags || "",
        description: row.description || "",
        updateFrequency: typeof row.updateFrequency === "undefined" ? 1 : row.updateFrequency,
        updateType: typeof row.updateType === "undefined" ? 0 : row.updateType
      }
      this.editDialogVisible = true
    },
    saveEditDim() {
      this.$refs.editDimForm.validate(async (valid) => {
        if (valid) {
          await Request.api.paramPost("/DimManage/editVersion", {
            ...this.editDimForm
          })
          this.$message.success("编辑成功")
          this.editDialogVisible = false
          // 同步表单数据到row
          if (this.editRow) {
            Object.assign(this.editRow, this.editDimForm)
          }
        } else {
          return false
        }
      })
    },
    // 原子指标详情
    getTableInfo() {
      // 组装参数
      const params = this.tableColumns.map((column) => {
        return {
          fieldName: column.prop || column.label,
          algorithmCalcResult: this.tableData,
          lxbm: "sf"
        }
      })

      // 获取匹配结果
      this.matchDim(params).then((matchResult) => {
        // 融合数据
        this.ruleForm.tableData1 = this.tableColumns.map((item) => {
          const match = matchResult.find((m) => m.fieldName === item.prop)
          if (match.definitionCode) {
            this.$set(this.dimLevelMap, match.definitionCode, match.dimLevels)
          }
          return {
            ...item,
            definitionCode: match?.definitionCode || "",
            levelCode: match?.levelCode || "",
            noCoverValues: match?.noCoverValues || [],
            dimName: match?.fieldName || "",
            version: match?.version || "",
            noCoverShowCount: 5,
            newCreate: false,
            field: item.prop, // 字段
            indName: item.prop, // 指标名称
            // tagType: "指标",
            period: "1",
            scopeId: Number(this.$route.query.sysjy) || 999, // 指标域id
            precision: null, // 精度
            rounding: 0, // 1：是，0：否
            thresholdMin: null, // 最小阈值
            thresholdMax: null, // 最大阈值
            runCycle: "", // 运行周期
            unitName: "", // 单位
            description: "", // 描述
            tag: [],
            createdById: null,
            indCode: null,
            indDetailCode: null,
            isNewlyAdded: true,
            // sqlStatementOrigin: this.form.sql,
            tagType: item.prop.toUpperCase() === "ID" ? "NULL" : "ps",
            deptName: null, // 部门名称
            sortType: null, // 排序类型
            sortRange: null, // 排序范围
            sortLimit: null, // 排序限制
            dataFormat: 0, // 数据格式
            execType: "sum", // 计算方式
            isZeroWarn: 0, // 是否启用零值预警
            zeroWarnTime: "" // 预警检测时间
          }
        })
        console.log(66666, this.ruleForm.tableData1)
      })
    },
    // 下一步
    handleNextStep1() {
      if (this.$refs.mainMethodForm) {
        this.$refs.mainMethodForm.validate((valid) => {
          if (valid) {
            this.executeTryCalc("next").then(() => {
              this.loading = false
              console.log(55555, this.mode)
              if (this.mode === "edit") {
                this.populateTableData(this.editDetailObj)
              } else if (this.mode === "add") {
                this.getTableInfo()
              }
              // 确保数据更新后再切换到第二步
              this.$nextTick(() => {
                this.activeId = 2
              })
            })
          }
        })
      }
    },
    handleNextStep2() {
      if (this.mode === "add") {
        // 只有在ruleForm.tableData2为空时才执行过滤操作
        if (this.ruleForm.tableData2.length === 0) {
          let arr = []
          let invalidRow = null
          this.ruleForm.tableData1.forEach((item) => {
            if (item.tagType === "INT" || item.tagType === "indOrDim") {
              arr.push(item)
            }
            // 新增校验
            if (["ps", "indOrDim", "time"].includes(item.tagType)) {
              if (!item.definitionCode || !item.levelCode) {
                invalidRow = item
              }
            }
          })
          if (invalidRow) {
            this.$message.warning("维度、度量和维度、时间维度类型必须选择对应维度和维度层级")
            return
          }
          this.ruleForm.tableData2 = arr
          if (this.ruleForm.tableData2.length === 0) {
            this.$message.warning("请选择度量")
            return
          }
        }
      } else if (this.mode === "edit") {
        // 在编辑模式下，基于现有的tableData2和tableData1更新数据
        let arr = []
        let invalidRow = null

        // 先处理现有的tableData2中的数据，保留原有的配置
        const existingTableData2Map = {}
        this.ruleForm.tableData2.forEach((item) => {
          existingTableData2Map[item.field] = item
        })

        // 遍历tableData1，处理所有标记为INT或indOrDim的字段
        this.ruleForm.tableData1.forEach((item) => {
          if (item.tagType === "INT" || item.tagType === "indOrDim") {
            // 如果这个字段已经在tableData2中存在，保留原有配置
            if (existingTableData2Map[item.field]) {
              arr.push(existingTableData2Map[item.field])
            } else {
              // 如果是新增的度量字段，创建新的条目
              arr.push({
                ...item,
                indName: item.indName || item.field,
                period: "1",
                scopeId: "",
                deptAllCode: [],
                execType: "sum",
                dataFormat: 0,
                unitName: "",
                precision: null,
                rounding: 0,
                thresholdMin: null,
                thresholdMax: null,
                description: "",
                tag: [],
                runCycle: "",
                sortType: null,
                sortRange: null,
                sortLimit: null,
                isZeroWarn: 0,
                zeroWarnTime: ""
              })
            }
          }
          // 校验维度类型字段是否选择了对应维度和层级
          if (["ps", "indOrDim", "time"].includes(item.tagType)) {
            if (!item.definitionCode || !item.levelCode) {
              invalidRow = item
            }
          }
        })

        if (invalidRow) {
          this.$message.warning("维度、度量和维度、时间维度类型必须选择对应维度和维度层级")
          return
        }

        this.ruleForm.tableData2 = arr
        if (this.ruleForm.tableData2.length === 0) {
          this.$message.warning("请选择度量")
          return
        }
        console.log(23324324, this.ruleForm.tableData1, this.ruleForm.tableData2)
      }
      this.loading = false
      this.activeId = 3
    },
    // 树节点加载
    async loadNode(node, resolve) {
      console.log("树节点加载", node)
      // 第一层库
      if (node.level === 0) {
        const { currentPage = 1, pageSize = 30 } = node.data.pagination || {}

        const { data } = await this.$httpBi.indicatorAnagement.getTableList({
          key: "",
          currentPage: currentPage,
          pageSize: pageSize
        })
        let list = data.list
        this.tables = this.tables.concat(data.list)

        const index = node.data.findIndex((d) => d.id === "load-more-id")
        console.log(index, "index")
        if (index) {
          node.childNodes.splice(index, 1)
          node.data.splice(index, 1)
        }
        // 检查是否需要添加"加载更多"节点
        if (Math.ceil(data.totalCount / data.pageSize) > data.currentPage) {
          list.push({
            id: "load-more-id", // 唯一标识
            label: "加载更多",
            leaf: true,

            isLoadingMoreNode: true
          })
        }
        // 点击"加载更多"合并新返回的数据
        node.data.push(...list)

        node.data.pagination = {
          currentPage: data.currentPage,
          pageSize: data.pageSize,
          total: data.totalCount
        }

        node.loaded = true // 标记为已加载，如果需要的话

        // 如果resolve有内容就是懒加载走查询 否则走的是修改
        console.log("否则走的是修改", node.data)
        if (resolve) {
          return resolve(node.data)
        }
      }
      // 第二层表
      if (node.level === 1) {
        const { data } = await this.$httpBi.indicatorAnagement.getTableFields({
          id: node.data.id,
          databaseName: "ods"
        })
        let list = data[0].info.map((item) => ({
          ...item,
          id: item.zddm,
          leaf: true
        }))
        this.columns = this.columns.concat(list)

        return resolve(list)
      }

      // 第三层字段
      if (node.level >= 2) return resolve([])
    },
    editHistory() {
      console.log("编辑历史")
      this.$router.push({
        name: "AlgorithmIndicatorHistory",
        query: {
          indCode: this.indCode,
          editAlgorithmId: this.editId
        }
      })
    },
    handleNodeClick(data, node) {
      if (node.level >= 2) return
      // 根据分页和总条数，判断是否需要点击加载更多
      if (
        node.parent.data.pagination.currentPage <
        node.parent.data.pagination.total / node.parent.data.pagination.pageSize
      ) {
        node.parent.data.pagination.currentPage++
        this.loadNode(node.parent, () => {}) // 触发懒加载以获取更多数据，这里不需要 resolve
      }
    },
    handleCancel() {
      this.$router.push({
        name: "indicatorAnagement"
      })
    },
    async handleTryCalc() {
      console.log(2222, this.form, this.$refs.mainMethodForm)
      if (!this.form.environmentCode) {
        this.$message.warning("请选择依赖环境")
        return
      }

      if (this.$refs.mainMethodForm) {
        this.$refs.mainMethodForm.validate((valid) => {
          if (valid) {
            this.executeTryCalc()
          }
        })
      }
    },

    // 将原来的试计算逻辑提取到单独的方法中
    async executeTryCalc(flag) {
      const encryptedPyScript = this.form.pyScript ? encryptInd.encrypt(this.form.pyScript) : ""
      const params = {
        ...this.form,
        pyScript: encryptedPyScript,
        toolDetailList: this.algorithmPackages || []
      }

      try {
        const res = await Request.algorithm.tryCalculateIndicator(params)
        console.log(res)
        if (res.code === 200 && res.data.result && res.data.result.length) {
          this.calcResults = res.data.result || []
          this.tableColumns = Object.keys(res.data.result[0]).map((key, index) => ({
            id: index,
            label: key,
            prop: key,
            visible: true,
            sortable: false
          }))
          this.tableData = res.data.result || []
          this.page.total = this.tableData.length
          console.log(1234, this.tableData, this.tableColumns)

          // 如果是编辑模式且是第一次试计算，保存初始数据
          if (this.mode === "edit" && flag !== "next" && this.initialTableData.length === 0) {
            this.initialTableData = [...this.tableData]
          }

          // 如果是点击下一步，对比数据并找出新增项
          if (flag === "next") {
            this.findNewData()
            this.activeId = 2
          }
        } else {
          this.page.total = 0
          this.tableColumns = []
          this.tableData = []
          this.calcResults = []
        }
      } catch (error) {
        console.error("试计算失败:", error)
        this.$message.error("试计算失败，请重试")
      }
    },
    // 对比当前tableData与初始数据，找出新增的字段
    findNewData() {
      console.log(2222222, this.tableData, this.initialTableData)

      if (this.mode !== "edit") return []
      // 获取初始数据的字段集合
      const initialFields = new Set()
      if (this.initialTableData && this.initialTableData.length > 0) {
        Object.keys(this.initialTableData[0]).forEach((key) => {
          initialFields.add(key)
        })
      }

      // 获取当前数据的字段集合
      const currentFields = new Set()
      if (this.tableData && this.tableData.length > 0) {
        Object.keys(this.tableData[0]).forEach((key) => {
          currentFields.add(key)
        })
      }

      // 找出新增的字段（在当前数据中有但在初始数据中没有的字段）
      const newFields = []
      currentFields.forEach((field) => {
        if (!initialFields.has(field)) {
          newFields.push(field)
        }
      })

      console.log("新增的字段:", newFields)
      return newFields
    },
    // 全部导出
    handleAllExport() {},
    handleExportExcel(selection) {
      console.log(selection, "selection")

      jsonToSheetXlsx({
        data: selection,
        filename: "表数据" + new Date().getTime()
      })
    }
  }
}
</script>

<style scoped lang="scss">
.create-head {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  border-bottom: 1px solid #e5e5e5;
  height: 80px;
  background: #fff;
  padding: 20px;
  box-sizing: border-box;

  .el-icon-back {
    font-size: 20px;
    padding-right: 16px;
    cursor: pointer;
  }

  .steps {
    width: auto;
    height: 48px;
    background: #f5f7fa;
    border-radius: 6px;
    margin-left: 40px;
    display: flex;
    align-items: center;
    padding: 0 32px;

    .line {
      flex: 0 0 auto;
      width: 168px;
      height: 1px;
      background: #cbced1;
      margin: 0 12px;

      &.active {
        background: #1563ff;
      }
    }

    .step-item {
      display: flex;
      align-items: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #5c646e;

      &.active {
        color: #1563ff;
      }

      i {
        font-size: 19px;
        margin-right: 6px;
      }
    }
  }
  .env-container {
    margin-left: 40px;
    display: flex;
    align-items: center;
    gap: 20px;

    .env-item {
      display: flex;
      align-items: center;
      gap: 10px;

      .env-label {
        font-size: 14px;
        font-weight: 400;
        white-space: nowrap;
      }

      .el-select {
        width: 180px;
      }
    }

    .env-info {
      display: flex;
      align-items: center;
      gap: 10px;

      .env-label {
        font-size: 14px;
        font-weight: 400;
      }

      .env-value {
        font-size: 14px;
        color: #333;
        font-weight: 500;
      }
    }
  }
}
.step-two {
  height: calc(100% - 100px);
  display: flex;
  flex-direction: column;
  background-color: #fff;
  .algorithm-info-form {
    padding: 20px 20px 0 0;
    width: 100%;
    .result-text {
      font-size: 17px;
      font-weight: 700;
      padding-left: 24px;
    }
  }
  .step-btn {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 52px;
    padding-right: 24px;
    box-sizing: border-box;
    border-top: 1px solid #f0f0f0;
  }
}

.editor-content {
  width: 100%;
  display: flex;
  min-height: calc(100% - 120px);
  padding-top: 20px;

  background-color: #f0f2f5;
  box-sizing: border-box;

  .left {
    flex-shrink: 0;
    width: 320px;
    border-radius: 6px;
    margin-right: 16px;
    background: #fff;
    padding: 24px 20px 0 16px;

    ::v-deep .el-form {
      height: 100%;
      display: flex;
      flex-direction: column;

      .el-form-item:nth-child(2) {
        flex: 1 1 0;
        overflow: hidden;

        &::-webkit-scrollbar {
          /*滚动条整体样式*/
          width: 6px;
          /*高宽分别对应横竖滚动条的尺寸*/
          height: 6px;
        }

        &::-webkit-scrollbar-thumb {
          /*滚动条里面小方块*/
          border-radius: 6px;
          height: 2px;
          background-color: #cfd6e6;
        }

        &::-webkit-scrollbar-track {
          /*滚动条里面轨道*/
          // box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
          background: transparent;
          border-radius: 6px;
        }

        .el-form-item__content {
          height: calc(100% - 45px);
        }
      }
    }

    .custom-tree-node {
      width: 100%;
    }

    .tree-db {
      height: 100%;
      overflow-y: auto;

      &::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 6px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 6px;
      }

      &::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius: 6px;
        height: 2px;
        background-color: #cfd6e6;
      }

      &::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        // box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        background: transparent;
        border-radius: 6px;
      }
    }
  }

  .right {
    position: relative;
    width: calc(100% - 340px);
    padding: 20px;
    min-height: calc(100vh - 250px);
    background: #fff;
    display: flex;
    flex-direction: column;

    .top-content {
      display: flex;
      flex: 1;
      min-height: 0;

      .editor-pane {
        flex: 1;
        height: 100%;
        padding: 20px;
        background: #2d2d2d;
        width: calc(100% - 340px);
        min-height: 300px;
      }

      .config-pane {
        flex: 0 0 320px;
        margin-left: 20px;
        box-sizing: border-box;
        border-radius: 0 0 4px 4px;
        border: 1px solid #f5f6fa;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }
    }

    .config-content {
      display: flex;
      flex-direction: column;
      height: 100%;
      overflow-y: auto;
      max-height: 950px;

      .form-section {
        padding-bottom: 16px;

        h3 {
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 16px;
          color: #222222;
          line-height: 16px;
          text-align: left;
          font-style: normal;
          background: #f5f6fa;
          padding: 8px;
          box-sizing: border-box;
          border-radius: 4px 4px 0 0;
        }
      }

      .section-item {
        flex: 1;
        overflow-y: auto;
        display: flex;
        flex-direction: column;

        .no-item {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          flex: 1;
          text-align: center;
          padding: 20px;

          .el-button {
            margin-bottom: 16px;
          }

          div {
            color: #909399;
            font-size: 14px;
          }
        }

        .section-container {
          border-radius: 0 0 4px 4px;
          border: 1px solid #f5f6fa;
          margin: 8px;

          .collapsible-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #222222;
            line-height: 16px;
            text-align: left;
            font-style: normal;
            padding: 8px;
            box-sizing: border-box;
            border-radius: 4px 4px 0 0;
            cursor: pointer;
            .param-title {
              width: 180px;
              line-height: 1.2;
            }
            .param-version {
              margin-left: 12px;
              font-size: 14px;
              font-weight: 400;
              background-color: rgb(221, 221, 221);
            }
            .section-right-btns {
              .el-icon-arrow-down {
                margin-right: 10px;
                color: rgb(16, 142, 233);
                transition: transform 0.3s;

                &.is-rotate {
                  transform: rotate(180deg);
                }
              }
            }
          }

          .item {
            background-color: #f5f6fa;
            padding: 10px 5px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

            .item-label {
              padding: 8px 0 0 5px;
            }

            .item-content-container {
              border-radius: 4px;
              background-color: #ffffff;
              padding: 5px 8px;
              margin-bottom: 10px;

              &:last-child {
                margin-bottom: 0;
              }

              .item-content-header {
                font-family: "Arial Negreta", "Arial Normal", "Arial", sans-serif;
                font-weight: 700;
                color: rgb(16, 142, 233);
                display: flex;
                justify-content: space-between;
                align-items: center;

                .copy-icon {
                  cursor: pointer;
                  color: rgb(16, 142, 233);
                  font-size: 14px;

                  &:hover {
                    opacity: 0.8;
                  }
                }
              }

              .item-content {
                margin: 10px 0;
                line-height: 1.5;
                word-wrap: break-word;
                color: #6b6b6b;
              }
            }
          }
        }
      }
    }

    .middle-content {
      flex: 0 0 auto;
      margin: 20px 0;

      .tip-container {
        border: 1px solid rgba(255, 249, 212, 1);
        border-radius: 4px;
        padding: 12px 16px;
        box-sizing: border-box;
        background: rgba(255, 249, 212, 1);
        display: flex;
        justify-content: space-between;

        .tip-left {
          flex: 0 0 auto;
          display: flex;
          align-items: center;
          gap: 10px;
          .main-method-container {
            display: flex;
            flex-direction: column;
            gap: 5px;

            .tip-label {
              white-space: nowrap;
              font-size: 14px;
              color: #606266;
            }

            .el-input {
              width: 200px;
            }
          }
        }

        .tip-right {
          flex: 1 1 auto;
          padding-left: 20px;

          .tip-item {
            display: flex;
            align-items: flex-start;
            font-size: 12px;
            line-height: 1.5;
            margin-bottom: 4px;
            color: #ff7709;
            font-family: PingFangSC, PingFang SC;

            &:last-child {
              margin-bottom: 0;
            }

            .tip-icon {
              color: #ff7709;
              font-size: 14px;
              margin-right: 8px;
              flex-shrink: 0;
              margin-top: 3px;
            }

            .tip-text {
              font-weight: 500;
              font-family: PingFangSC, PingFang SC;
              color: #ff7709;
            }
          }
        }
      }

      .result {
        margin-top: 0;
        padding: 16px;
      }
    }

    .footer-btn {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 52px;
      padding-right: 24px;
      box-sizing: border-box;
      border-top: 1px solid #f0f0f0;
      flex-shrink: 0;
      margin-top: auto;
    }
  }
}

.algorithm-form-container {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fff;
  min-height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;

  .algorithm-form {
    margin-bottom: 20px;

    ::v-deep .el-form-item__label {
      font-weight: 500;
    }
  }

  .output-description {
    p {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      margin: 0;
      padding-bottom: 10px;
    }
  }

  .output-table {
    flex: 1;
    margin-bottom: 20px;

    ::v-deep .el-table {
      .el-table__header th {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 500;
      }
    }
  }

  .footer-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 52px;
    padding-right: 24px;
    box-sizing: border-box;
    border-top: 1px solid #f0f0f0;
    flex-shrink: 0;
    margin-top: auto;
    margin-bottom: 0;
  }
}
.indicator-table-container {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fff;
  min-height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;

  .table-wrapper {
    flex: 1;
    overflow-x: auto;
    margin-bottom: 20px;

    .indicator-table {
      width: 100%;
      min-width: 1200px;

      ::v-deep .el-table__header th {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 500;

        .cell {
          display: flex;
          align-items: center;
        }

        .precision-header {
          display: flex;
          align-items: center;

          .el-tooltip {
            margin-left: 5px;
            color: #909399;
            cursor: pointer;
          }
        }
      }

      ::v-deep .el-table__row {
        .precision-cell {
          display: flex;
          align-items: center;

          .el-input {
            margin-right: 10px;
          }
        }
      }
    }
  }

  .footer-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 52px;
    padding-right: 24px;
    box-sizing: border-box;
    border-top: 1px solid #f0f0f0;
    flex-shrink: 0;
    margin-top: auto;
    margin-bottom: 0;
  }
}
</style>
<style lang="scss">
.drag-element {
  /* 禁止文本选择 */
  user-select: none;
  /* 禁用默认拖拽效果 */
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

#project_frame
  .indicator-algorithm-page
  .model-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .el-tree-node__expand-icon {
  background-color: transparent;
}

#project_frame
  .indicator-algorithm-page
  .model-tree
  .el-tree-node.is-current
  > .el-tree-node__content:has(> span.item-style) {
  position: relative;
  background: #f4f7ff !important;
  box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1), 0px 6px 6px -4px rgba(0, 42, 128, 0.12);
  border-radius: 4px;
  border: 1px solid #1563ff;
  cursor: move;

  .el-tree-node__label {
    background: transparent !important;
  }
}

#project_frame .indicator-algorithm-page .model-tree .el-tree-node {
  border: 1px solid transparent !important;
}

#project_frame .indicator-algorithm-page .el-tree-node .el-tree-node__content {
  .is-leaf.el-tree-node__expand-icon.el-icon-caret-right {
    display: none;
  }
}

#project_frame .indicator-algorithm-page {
  .el-tree-node > .el-tree-node__content {
    margin-bottom: 8px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .el-tree-node:not(.is-expanded) > .el-tree-node__content:has(> span.item-style) {
    &:hover {
      position: relative;
      background: #f4f7ff !important;
      box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1), 0px 6px 6px -4px rgba(0, 42, 128, 0.12);
      border-radius: 4px;
      border: 1px solid #1563ff;
      cursor: move;

      .el-checkbox {
        background-color: transparent !important;
      }

      .el-tree-node__expand-icon {
        background-color: transparent !important;

        border-top-left-radius: 2px;
        border-bottom-left-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }

      .custom-tree-node,
      .el-tree-node__label {
        background-color: transparent !important;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }
    }
  }

  .el-tree-node.dragging > .el-tree-node__content {
    opacity: 0.2;
  }

  .el-tree-node > .el-tree-node__content {
    height: 42px;
    line-height: 42px;
    display: block;

    .custom-tree-node-alg {
      position: relative;
      height: 100%;
      width: 100%;
      box-sizing: border-box;
      margin-bottom: 12px;

      .table-name {
        display: flex;
        height: 10px;
        position: absolute;
        font-size: 12px;
        top: 10px;
        left: 20px;

        &::after {
          position: absolute;
          top: 7px;
          left: -15px;
          content: "";
          display: block;
          width: 12px;
          height: 12px;
          border: 1px solid #1563ff;
          border-top: none;
          border-right: none;
        }
      }
    }

    &:hover {
      background: #f5f7fa !important;

      > .el-checkbox {
        background-color: transparent !important;
      }

      .el-tree-node__expand-icon {
        background-color: transparent !important;

        border-top-left-radius: 2px;
        border-bottom-left-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }

      .custom-tree-node,
      .el-tree-node__label {
        background-color: transparent !important;

        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }
    }
  }

  .el-tree-node.is-current > .el-tree-node__content {
    background: #f5f7fa;

    > .el-checkbox {
      background-color: transparent !important;
    }

    .el-tree-node__expand-icon {
      background-color: transparent !important;

      border-top-left-radius: 2px;
      border-bottom-left-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }

    .custom-tree-node,
    .el-tree-node__label {
      background-color: transparent !important;

      border-top-right-radius: 2px;
      border-bottom-right-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }
  }
}

#project_frame .indicator-algorithm-page .el-table.sqlIndicator-table td,
#project_frame .indicator-algorithm-page .el-table.sqlIndicator-table th {
  padding: 0;
}
</style>
