<template>
  <div style="position: relative">
    <div
      class="indicator-item"
      id="draggable"
      :draggable="!selectIndicators.length || isDrag(item)"
      :style="{
        cursor:
          !selectIndicators.length || isDrag(item) ? 'move' : 'not-allowed'
      }"
      @dragstart="onDragStart(item, $event)"
      @dragend="onDragEnd"
      @mouseenter="showCustomTooltip"
      @mousemove="moveCustomTooltip"
      @mouseleave="hideCustomTooltip"
    >
      <div class="target-type">
        <div class="zbmc" :title="item.indName">{{ item.indName }}</div>
        <div class="zblx">
          {{ getIndicatorType(item.indType) }}
          <div class="tags">
            <el-tooltip effect="light" placement="bottom-start">
              <div slot="content" v-html="getDimensionTooltipContent()"></div>
              <div
                class="tag-item"
                :class="[
                  sameDimensionsCount > 0 && isDrag(item) ? 'deep' : 'gray'
                ]"
              >
                {{ sameDimensionsCount }}项共有维度
              </div>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="tooltip.show"
      :style="{
        position: 'fixed',
        left: tooltip.x + 'px',
        top: tooltip.y + 'px',
        background: 'rgba(0,0,0,0.75)',
        color: '#fff',
        padding: '6px 12px',
        borderRadius: '4px',
        fontSize: '12px',
        pointerEvents: 'none',
        zIndex: 9999
      }"
    >
      {{ tooltip.text }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'IndicatorItem',
  components: {},
  props: {
    item: {
      type: Object,
      required: true
    },
    selectIndicators: {
      type: Array,
      required: true
    },
    sameDimension: {
      type: Array,
      required: true
    },
    useDims: {
      type: Array,
      required: true
    },
    indicatorTypeList: {
      type: Array
    }
  },
  data () {
    return {
      tooltip: {
        show: false,
        text: '',
        x: 0,
        y: 0
      }
    }
  },
  computed: {
    sameDimensionsCount () {
      if (!this.item.dimList) return 0
      return this.item.dimList.filter(wd => this.isSameDimensions(wd)).length
    }
  },
  created () {},
  mounted () {},
  watch: {},
  methods: {
    // 是否选中有相同维度
    isSameDimensions (wd) {
      return this.sameDimension.some(e => e.levelCode === wd.levelCode)
    },
    // 是否可以拖拽
    isDrag (item) {
      if (this.useDims.length) {
        console.log(this.useDims, 'this.useDims')
        console.log(this.dimList, 'this.dimList')

        // 每个dimCol都要在item.dimList中找到
        return this.useDims.every(
          ele =>
            item.dimList &&
            item.dimList.some(e => e.levelCode === ele.levelCode)
        )
      }
      // 其它逻辑不变
      return (
        item.dimList &&
        item.dimList.some(e => {
          return this.sameDimension.some(ele => ele.levelCode === e.levelCode)
        }) &&
        !this.selectIndicators.some(i => i.indCode === item.indCode)
      )
    },
    // 拖拽开始
    onDragStart (data) {
      this.$emit('indicatorsdragStart', data)
    },
    // 拖拽结束
    onDragEnd () {
      this.$emit('indicatorsdragEnd')
    },
    getIndicatorType (indType) {
      return this.indicatorTypeList.find(e => e.value === indType).label
    },
    getDimensionTooltipContent () {
      if (!this.item.dimList) return '无维度信息'

      const dimensions = this.item.dimList.map(dim => {
        const isShared = this.isSameDimensions(dim)
        return `<div class="dimension-item ${isShared ? 'deep' : 'gray'}">${
          dim.dimName
        }</div>`
      })

      return `<div class="dimension-tooltip">${dimensions.join('')}</div>`
    },
    getMissingDims () {
      if (!this.useDims.length || !this.item.dimList) return []
      return this.useDims.filter(
        useDim => !this.item.dimList.some(dim => dim.dimCol === useDim.dimCol)
      )
    },
    showCustomTooltip (e) {
      if (!this.selectIndicators.length) {
        return
      }
      let text = ''
      if (this.sameDimensionsCount === 0) {
        text = '没有共有维度'
      } else if (!this.isDrag(this.item)) {
        const missing = this.getMissingDims()
        if (missing.length) {
          text = '缺少维度：' + missing.map(dim => dim.dimName).join('，')
        } else {
          text = '不能拖拽'
        }
      }
      if (text) {
        this.tooltip = {
          show: true,
          text,
          x: e.clientX + 10,
          y: e.clientY - 10
        }
      }
    },
    moveCustomTooltip (e) {
      if (this.tooltip.show) {
        this.tooltip.x = e.clientX + 10
        this.tooltip.y = e.clientY - 10
      }
    },
    hideCustomTooltip () {
      this.tooltip.show = false
    }
  }
}
</script>

<style scoped lang="scss">
.indicator-item {
  width: calc(100% - 10px);
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
  padding: 8px;
  border-radius: 8px;
}

::v-deep .dimension-tooltip {
  max-width: 500px;
  display: flex;
  flex-wrap: wrap;

  .dimension-item {
    padding: 4px 8px;
    margin-bottom: 4px;
    border-radius: 2px;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    line-height: 20px;
    white-space: nowrap;
    margin: 4px;

    &.gray {
      background: #f1f1f1;
      color: #666666;
      border: 1px solid #e0e0e0;
    }

    &.deep {
      background: #3875f6;
      color: #fff;
      border: 1px solid #3875f6;
    }
  }
}

.target-type {
  display: flex;
  flex-direction: column;
  min-width: 108px;

  .zbmc {
    height: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #060607;
    text-align: left;
    font-style: normal;
    text-transform: none;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .zblx {
    margin-top: 8px;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #999999;
    display: flex;
    align-items: center;

    .tags {
      flex: 1;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      margin-left: 16px;
      .tag-item {
        padding: 0 8px;
        margin-right: 4px;
        margin-bottom: 4px;
        height: 20px;
        border-radius: 2px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        line-height: 20px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;

        &.gray {
          background: #f1f1f1;
          color: #666666;
          border: 1px solid #e0e0e0;
        }

        &.deep {
          background: #3875f6;
          color: #fff;
          border: 1px solid #3875f6;
        }
      }
    }
  }
}
.tag-item {
  padding: 0 8px;
  margin-right: 4px;
  margin-bottom: 4px;
  height: 20px;
  border-radius: 2px;
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 20px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;

  &.gray {
    background: #f1f1f1;
    color: #666666;
    border: 1px solid #e0e0e0;
  }

  &.deep {
    background: #3875f6;
    color: #fff;
    border: 1px solid #3875f6;
  }
}
</style>
