<template>
  <DT-View class="preview-excel">
    <div class="preview--header">
      <div class="title">
        <span>AI报表预览</span>
      </div>
      <el-button type="primary" @click="downloadExcel">导出</el-button>
    </div>
    <vue-office-excel
        :src="file"
        style="height:calc(100vh - 240px);width: 100%;"
      />
  </DT-View>
</template>

<script>
import VueOfficeExcel from "@vue-office/excel"
import "@vue-office/excel/lib/index.css"
export default {
  components: {
    VueOfficeExcel
  },
  props: {},
  data () {
    return {
      file:"file",
    }
  },
  computed: {},
  created () {
    const emptyCell = JSON.parse(this.$route.query.emptyCell)

    const shapeError = JSON.parse(this.$route.query.shapeError)

    if (emptyCell||shapeError) {

      this.$message.warning("报表存在数据为空的情况，建议增加跟模板有相关性的指标")

    }
    this.file = this.$route.query.file
  },
  methods: {
    downloadExcel () {
      if (this.file) {
        window.open(this.file)
      } else {
        this.$message.warning('暂无可下载的Excel文件')
      }
    }
  }

}
</script>

<style lang="scss">
.preview-excel {
  box-sizing: border-box;
  background: #f5f7fa;
  .preview--header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid #e4e7ed;
    padding-bottom: 10px;

    .title {
      font-size: 18px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #060607;
    }

  }

}

#excel-preview {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  background: #fff;
  table {
    width: 100%;
    border: 1px solid #e4e7ed;
  }
  th,
  td {
    border: 1px solid #e4e7ed;
    padding: 8px;
    text-align: center;
    font-size: 14px;
    color: #303133;
    min-width: 80px;
    max-width: 300px;
    word-break: break-all;
  }
  th {
    background: #f5f7fa;
    font-weight: bold;
  }
  tr:hover {
    background: #f0f9ff;
  }
}
</style>
