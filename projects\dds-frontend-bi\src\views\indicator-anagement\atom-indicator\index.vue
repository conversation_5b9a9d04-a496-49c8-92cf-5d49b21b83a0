<template>
  <DT-View
    :inner-style="{
      position: 'relative'
    }"
  >
    <!-- <el-alert title="XXXX" type="warning" show-icon></el-alert> -->
    <div class="warm-reminder">
      <svg-icon icon-class="warning" style="margin-top: 3px" />
      <div class="warm-reminder-content">
        <p>
          支持批量创建原子指标，数据源可多选，预览数据列时请手动下拉进度条，确认每个数据源中的字段情况。
        </p>
      </div>
    </div>
    <!-- <el-steps :active="active" finish-status="success">
        <el-step title="选择数据源"></el-step>
        <el-step title="预览数据列"></el-step>
        <el-step title="预览原子指标"></el-step>
      </el-steps> -->
    <div class="steps">
      <div
        class="step-item step1"
        :class="{
          active: active == 1,
          success: active > 1
        }"
      >
        <svg-icon
          v-if="active > 1"
          icon-class="success"
          class="step-icon-success"
        />
        <span v-else class="step-icon">1</span>

        <span class="step-text">选择数据源</span>
        <span class="step-line"></span>
      </div>
      <div
        class="step-item"
        :class="{
          active: active == 2,
          success: active > 2
        }"
      >
        <svg-icon
          v-if="active > 2"
          icon-class="success"
          class="step-icon-success"
        />
        <span v-else class="step-icon">2</span>
        <span class="step-text">预览数据列</span>
        <span class="step-line"></span>
      </div>
      <div
        class="step-item"
        :class="{
          active: active == 3,
          success: active > 3
        }"
      >
        <svg-icon
          v-if="active > 3"
          icon-class="success"
          class="step-icon-success"
        />
        <span v-else class="step-icon">3</span>
        <span class="step-text">预览原子指标</span>
      </div>
    </div>
    <div class="one" v-if="active == 1">
      <el-input
        placeholder="输入关键字搜索"
        prefix-icon="el-icon-search"
        v-model="key"
        class="search-input"
        @input="getSearchTable"
      ></el-input>
      <div
        class="checkbox-warp"
        v-loading="loading1"
        v-load-more="handleScroll"
      >
        <el-checkbox-group v-model="ids">
          <el-checkbox
            v-for="item in dataDomainList"
            :label="item.id"
            :key="item.id"
          >
            <svg-icon icon-class="sjy" />
            {{ item.a }}/{{ item.c }}/{{ item.id }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
    </div>
    <div class="two" v-show="active == 2" v-loading="loading">
      <div
        class="data-column-wrapper"
        v-for="item in tableInfoList"
        :key="item.id"
      >
        <div class="data-column-title">
          {{ item.dataSourceName }}/{{ item.bm }}/{{ item.id }}
        </div>
        <el-table
          :data="item.info"
          style="width: 100%"
          border
          class="atomIndicator"
        >
          <!-- <el-table-column type="index" label="序号"></el-table-column> -->
          <el-table-column prop="zddm" label="字段代码"></el-table-column>
          <el-table-column prop="bt" label="标题"></el-table-column>
          <!-- <el-table-column prop="sm" label="说明"></el-table-column> -->
          <el-table-column prop="sjlx" label="数据类型"></el-table-column>
          <el-table-column prop="cd" label="长度"></el-table-column>
          <!-- 
            <el-table-column prop="xsws" label="小数位数"></el-table-column> -->
          <!-- <el-table-column prop="kwk" label="可为空"></el-table-column> -->
          <!-- <el-table-column prop="sfwy" label="是否唯一"></el-table-column> -->
          <el-table-column prop="zdlx" label="标记类型" width="220">
            <template #default="{ row }">
              <el-select
                v-model="row.zdlx"
                placeholder="请选择"
                @change="onZdlxChange(row)"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  :disabled="
                    item.value === 'time' &&
                    hasTimeDimension &&
                    row.zdlx !== 'time'
                  "
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="wb" label="对应维度" width="260">
            <template #default="{ row }">
              <div style="display: flex; align-items: center">
                <el-popover
                  placement="right"
                  width="250"
                  trigger="hover"
                  @after-leave="resetNoCoverShowCount(row)"
                  v-if="
                    row.noCoverValues.length &&
                    ['ps', 'indOrDim', 'time'].includes(row.zdlx)
                  "
                >
                  <div>
                    <div class="popover-title">无法覆盖以下字段：</div>
                    <div class="popover-content">
                      <div
                        v-for="field in row.noCoverValues.slice(
                          0,
                          row.noCoverShowCount || 5
                        )"
                        :key="field"
                      >
                        {{ field }}
                      </div>
                      <el-button
                        v-if="
                          row.noCoverValues.length > (row.noCoverShowCount || 5)
                        "
                        type="text"
                        @click.stop="loadMoreNoCover(row)"
                      >
                        继续查看
                      </el-button>
                    </div>
                  </div>
                  <i
                    class="el-icon-warning"
                    style="
                      margin-right: 5px;
                      font-size: 20px;
                      color: #e6a23c;
                      cursor: pointer;
                    "
                    slot="reference"
                  ></i>
                </el-popover>
                <el-select-v2
                  v-if="
                    ['ps', 'indOrDim', 'time'].includes(row.zdlx) &&
                    !row.newCreate
                  "
                  v-model="row.definitionCode"
                  :options="allDimTreeList"
                  :props="{
                    label: 'dimName',
                    value: 'definitionCode'
                  }"
                  filterable
                  placeholder="请选择"
                  @change="getDimLevels($event, row)"
                  clearable
                >
                  <template #default="{ item }">
                    <p
                      style="
                        padding: 0 17px;
                        display: flex;
                        justify-content: space-between;
                      "
                    >
                      <span v-tooltip-content="180">{{ item.dimName }}</span>
                      <span>{{ item.version }}</span>
                    </p>
                  </template>
                </el-select-v2>
                <span
                  v-if="
                    row.newCreate &&
                    ['ps', 'indOrDim', 'time'].includes(row.zdlx)
                  "
                  style="display: flex; align-items: center"
                >
                  <span v-tooltip-content="130">
                    {{ row.dimName }}({{ row.version }})
                  </span>
                  <el-button type="text" @click="removeDim(row)"
                    >移除</el-button
                  >
                  <el-button type="text" @click="editDim(row)">编辑</el-button>
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="wb" label="维度层级" width="220">
            <template #default="{ row }">
              <el-select
                v-if="
                  ['ps', 'indOrDim', 'time'].includes(row.zdlx) &&
                  row.definitionCode != 'self'
                "
                v-model="row.levelCode"
                placeholder="请选择"
                @change="onLevelCodeChange(row)"
              >
                <el-option
                  v-for="item in dimLevelMap[row.definitionCode]"
                  :key="item.levelCode"
                  :label="item.levelName"
                  :value="item.levelCode"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="three" v-show="active == 3">
      <!--  -->
      <div class="allselect">
        <el-button @click="toggleSelection">全选</el-button>
        <span style="margin-left: 12px">
          已选中{{ multipleSelection.length }}条
        </span>
      </div>
      <el-form
        status-icon
        :rules="rules"
        :model="form"
        ref="ruleForm"
        label-width="0"
        class="ruleForm"
      >
        <el-table
          ref="multipleTable"
          :data="form.tableData"
          tooltip-effect="dark"
          style="width: 100%"
          :max-height="320"
          :row-class-name="tableRowClassName"
          @cell-click="tabClick"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column
            type="index"
            label="序号"
            width="70"
          ></el-table-column>
          <el-table-column prop="zbmc" label="指标名称">
            <template slot-scope="scope">
              <el-form-item
                :ref="'zdmc' + scope.row.index"
                class="is-required"
                :prop="'tableData.' + scope.$index + '.zbmc'"
                :rules="scope.row.openRule ? rules.zbmc : {}"
                style="
                  height: 100%;
                  display: flex;
                  align-items: center;
                  margin-bottom: 0;
                "
              >
                <span
                  v-if="
                    scope.row.index === tabClickIndex &&
                    tabClickLabel === '指标名称'
                  "
                >
                  <el-input
                    v-focus
                    v-model.trim="scope.row.zbmc"
                    maxlength="300"
                    placeholder="请输入标签"
                    size="mini"
                  />
                </span>
                <span v-else>
                  {{ scope.row.zbmc }}
                  <svg-icon style="color: #5b8ff9" icon-class="edit" />
                </span>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="zbymc" label="原数据列标题"></el-table-column>
          <el-table-column
            prop="dataSourceName"
            label="对应数据源"
          ></el-table-column>

          <el-table-column label="所属数据域" width="150">
            <template #default="{ row }">
              <avue-input-tree
                v-model="row.sysjy"
                placeholder="请选择数据域"
                :dic="viewGroup"
                :props="{
                  label: 'name',
                  value: 'id'
                }"
              ></avue-input-tree>
            </template>
          </el-table-column>
          <el-table-column label="归属部门" width="150">
            <template #default="{ row }">
              <el-cascader
                v-model="row.deptAllCode"
                clearable
                :props="cascaderProps"
              ></el-cascader>
            </template>
          </el-table-column>

          <el-table-column label="描述">
            <template slot-scope="scope">
              <span
                v-if="
                  scope.row.index === tabClickIndex && tabClickLabel === '描述'
                "
              >
                <el-input
                  v-focus
                  v-model="scope.row.ms"
                  maxlength="300"
                  placeholder="请输入描述"
                  size="mini"
                  @blur="inputBlur"
                />
              </span>
              <span v-else>
                {{ scope.row.ms }}
                <el-button type="text">添加</el-button>
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="bq" label="标签">
            <template slot-scope="scope">
              <!-- <span
                  v-if="
                    scope.row.index === tabClickIndex && tabClickLabel === '标签'
                  "
                >
                  <el-input
                    v-focus
                    v-model="scope.row.bq"
                    maxlength="300"
                    placeholder="请输入标签"
                    size="mini"
                    @blur="inputBlur"
                  />
                </span>
                <span v-else>
                </span> -->
              {{ scope.row.bq && scope.row.bq.join(',') }}
              <el-button type="text">添加</el-button>
            </template>
          </el-table-column>
          <el-table-column
            prop="address"
            label="派生维度"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <!-- <span v-for="(item, index) in row.pswd" :key="index">
                {{ item.wdbm?item.wdbm:item.zdmc }}{{ index ? "," : "" }}
              </span> -->
              {{ foramtPswd(row.pswd) }}
            </template>
          </el-table-column>
          <el-table-column prop="address" label="创建人">
            <template>
              {{ $store.state.user.username }}
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </div>
    <div class="fotter-btn">
      <el-button v-if="active == 1" @click="$router.go(-1)">取消</el-button>
      <el-button v-if="active > 1" @click="active--">上一步</el-button>
      <el-button
        v-if="active < 3"
        :disabled="!ids.length && active == 1"
        @click="handleAddStep"
      >
        下一步
      </el-button>
      <el-button v-if="active > 2" type="primary" @click="handleSave">
        保存({{ multipleSelection.length }})
      </el-button>
    </div>
    <MakeTag
      v-model="tabs"
      :dialog-visible.sync="dialogVisible"
      @handleSaveTags="handleSaveTags"
    />

    <el-dialog
      title="编辑维度"
      :visible.sync="editDialogVisible"
      width="600px"
      label-width="100px"
      label-position="top"
    >
      <el-form :model="editDimForm" :rules="editDimRules" ref="editDimForm">
        <el-form-item label="维度名称" prop="dimName">
          <el-input v-model="editDimForm.dimName" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="维度类型">
          <el-select
            v-model="editDimForm.categoryCode"
            placeholder="请选择类型"
            style="width: 100%"
          >
            <el-option
              :label="item.label"
              :value="item.value"
              v-for="item in dimensionTypeList"
              :key="item.categoryCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="版本" prop="version">
          <el-input v-model="editDimForm.version"></el-input>
        </el-form-item>
        <el-form-item label="层级名称" prop="levelName">
          <el-input
            v-model="editDimForm.levelName"
            placeholder="请输入层级名称"
          />
        </el-form-item>
        <el-form-item label="标签">
          <el-input v-model="editDimForm.tags"></el-input>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            type="textarea"
            :rows="4"
            placeholder="请输入描述"
            v-model="editDimForm.description"
          ></el-input>
        </el-form-item>
        <el-form-item label="更新频率">
          <el-select
            v-model="editDimForm.updateFrequency"
            placeholder="请选择维度类型"
          >
            <el-option
              v-for="item in updateFrequencys"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="更新方式">
          <el-radio-group v-model="editDimForm.updateType">
            <el-radio :label="0">增量同步</el-radio>
            <el-radio :label="1">全量同步</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveEditDim">确 定</el-button>
      </span>
    </el-dialog>
  </DT-View>
</template>
<script>
import _ from 'lodash'
import Request from '@/service'
import options from '../mixins/options'
import MakeTag from '../components/MakeTag.vue'

export default {
  components: { MakeTag },
  // 自定义指定
  directives: {
    focus: {
      // 指令的定义
      inserted (el) {
        el.querySelector('.el-input__inner').focus()
      }
    }
  },
  mixins: [options],
  props: {},
  data () {
    var changeZdmc = async (rule, value, callback) => {
      const { data } =
        await this.$httpBi.indicatorAnagement.checkIndicatorRepeat({
          zbmc: value,
          indCode: ''
        })
      const arr = this.form.tableData.filter(item => item.zbmc === value)
      if (data) {
        callback(new Error('字段名称已存在,请重新输入'))
      } else if (arr && arr.length > 1) {
        callback(new Error('字段名称已存在,请重新输入'))
      } else {
        this.tabClickIndex = null
        this.tabClickLabel = ''
      }
    }
    return {
      loading: false,
      sysjy: '0',
      rules: {
        zbmc: [{ validator: changeZdmc, trigger: 'blur', required: true }]
      },
      dialogVisible: false, // 打标签弹窗
      tabs: [],
      loading1: false,
      value: [],
      key: '',
      ids: [], // 选中的id
      dataDomainList: [], // 数据列表
      tableInfoList: [],
      multipleSelection: [],
      active: 1,
      isSuccess: false,
      tabClickIndex: '',
      tabClickLabel: '',
      checkList: [],
      form: {
        tableData: []
      },
      options: [
        {
          value: 'ps',
          label: '维度'
        },
        {
          value: 'indOrDim',
          label: '度量和维度'
        },
        {
          value: 'INT',
          label: '度量'
        },
        {
          value: 'time',
          label: '时间维度'
        },
        {
          value: 'NULL',
          label: '无'
        }
      ],
      viewGroup: [{ id: '0', name: '根目录', children: [] }], // 数据域分组

      dimLevelMap: {},
      dimOptions: {}, // 每个字段的可选维度
      allDimTreeList: [], // 所有维度树
      editDialogVisible: false,
      editDimForm: {
        dimName: '',
        version: ''
      },
      editDimRules: {
        dimName: [
          { required: true, message: '请输入维度名称', trigger: 'blur' }
        ],
        version: [{ required: true, message: '请输入版本', trigger: 'blur' }],
        levelName: [
          { required: true, message: '请输入层级名称', trigger: 'blur' }
        ]
      },
      editRow: null,
      updateFrequencys: [
        {
          label: '按日',
          value: 1
        },
        {
          label: '按周',
          value: 2
        },
        {
          label: '按月',
          value: 3
        },
        {
          label: '按学期',
          value: 4
        },
        {
          label: '按学年',
          value: 5
        },
        {
          label: '按年',
          value: 6
        }
      ],
      dimensionTypeList: [],
      page: {
        currentPage: 1,
        pageSize: 30,
        total: 0
      }
    }
  },
  computed: {
    hasTimeDimension () {
      // 检查所有数据列是否已存在 zdlx === 'time'
      return this.tableInfoList.some(item =>
        item.info.some(row => row.zdlx === 'time')
      )
    }
  },
  created () {
    this.sysjy = Number(this.$route.query.sysjy) || 999
    this.getTableList()
    this.getAllViewGroup()
    this.getDimTree()
    this.getDimensionList()
  },
  mounted () {},
  watch: {
    active (val) {
      if (val === 3) {
        this.$nextTick(() => {
          this.toggleSelection()
        })
      }
    }
  },
  methods: {
    // 获取维度列表
    async getDimensionList () {
      const { data } = await Request.api.paramPost(
        '/DimManage/getDimCategoryList',
        {}
      )
      this.dimensionTypeList = data.map(item => ({
        label: item.categoryName,
        value: item.categoryCode
      }))
    },
    // 获取所有维度树
    async getDimTree () {
      const { data } = await Request.api.paramPost('/DimManage/getDimList', {
        pageSize: -1,
        dimName: '',
        pageNum: 1
      })
      this.allDimTreeList =
        [
          {
            dimName: '自身维度创建',
            definitionCode: 'self',
            value: ''
          },
          ...(data.list || [])
        ] || []
    },

    // 获取维度版本
    async getDimLevels (definitionCode, row) {
      console.log(definitionCode, 'definitionCode>>>>>>>>')
      if (!definitionCode) {
        this.$set(row, 'definitionCode', '')
        this.$set(row, 'noCoverValues', [])
        this.$set(row, 'levelCode', '')

        return
      }
      if (definitionCode === 'self') {
        console.log(row)
        this.createSelfDim(row)
      } else {
        const { data } = await Request.api.paramPostQuery(
          '/DimManage/getDimLevelByDefinitionCode',
          {
            definitionCode
          }
        )
        if (data.length) {
          row.levelCode = data[0].levelCode
          row.levelName = data[0].levelName
          this.onLevelCodeChange(row)
        } else {
          row.levelCode = ''
          row.levelName = ''
          this.$message.warning('该维度没有层级')
        }
        this.$set(this.dimLevelMap, definitionCode, data)
      }
    },
    // 获取所有数据域分组
    async getAllViewGroup () {
      const { data } = await this.$httpBi.indicatorAnagement.getAllViewGroup()
      this.viewGroup[0].children = data
    },
    // 获取无覆盖维度
    async getNoCoverDimValues (row) {
      const { data } = await Request.api.paramPost(
        '/DimManage/getNoCoverDimValues',
        {
          tableSource: row.id,
          levelCode: row.levelCode,
          fieldCode: row.zddm,
          lxbm: 'yz'
        }
      )
      row.noCoverValues = data
    },
    tableRowClassName ({ row, rowIndex }) {
      // 把每一行的索引放进row
      row.index = rowIndex
    },
    // 添加明细原因 row 当前行 column 当前列
    tabClick (row, column) {
      switch (column.label) {
        case '描述':
          this.tabClickIndex = row.index
          this.tabClickLabel = column.label
          break
        case '标签':
          this.tabClickIndex = row.index
          this.tabClickLabel = column.label
          this.dialogVisible = true
          this.tabs = row.bq || []
          break
        case '指标名称':
          this.tabClickIndex = row.index
          this.tabClickLabel = column.label
          break

        default:
          return
      }
    },
    // 失去焦点初始化
    inputBlur () {
      this.tabClickIndex = null
      this.tabClickLabel = ''
    },
    getSearchTable () {
      this.page.currentPage = 1
      this.dataDomainList = []
      console.log(this.page.currentPage, '1////////////')

      this.getTableList()
    },
    handleScroll () {
      if (!this.loading1 && this.dataDomainList.length < this.page.total) {
        this.page.currentPage++
        this.getTableList()
      }
    },
    // 获取列表
    getTableList: _.debounce(
      function () {
        this.loading1 = true
        console.log(this.page.currentPage, '2////////////')
        this.$httpBi.indicatorAnagement
          .getTableList({
            key: this.key,
            currentPage: this.page.currentPage,
            pageSize: this.page.pageSize
          })
          .then(res => {
            this.dataDomainList.push(...res.data.list)
            this.page.total = res.data.totalCount
            this.loading1 = false
          })
      },
      300,
      {
        leading: true
      }
    ),
    // 下一步
    handleAddStep () {
      if (this.active === 1) {
        this.getTableInfo()
      }
      if (this.active === 2) {
        // 检查维度选择是否完整
        console.log(this.tableInfoList, 'this.tableInfoList')
        for (const item of this.tableInfoList) {
          for (const row of item.info) {
            if (
              (row.zdlx === 'ps' || row.zdlx === 'indOrDim') &&
              !row.levelCode
            ) {
              this.$message.warning('请选择维度层级')
              return
            }
          }
        }

        const arr = []
        const dywdMap = {} // 选对应维度的派生维度

        // 过滤为派生维度且选择对应维度的数据
        this.tableInfoList.forEach(item => {
          dywdMap[item.id] = []
          item.info.forEach(i => {
            if (i.zdlx === 'ps' || i.zdlx === 'time' || i.zdlx === 'indOrDim') {
              dywdMap[item.id].push({
                wdbm:
                  i.definitionCode === 'self' ? i.bt || i.zddm : i.levelName,
                wdid: i.wb || '0',
                wdzd: i.zddm,
                zdmc: i.bt,
                wdlx: i.zdlx === 'indOrDim' ? 'ps' : i.zdlx,
                levelCode: i.levelCode,
                definitionCode: i.definitionCode,
                sjlx: i.sjlx,
                zdlx: i.zdlx,
                sjgs: i.sjgs, // 时间维度格式
                gldm: '' // 关联代码,
              })
            }
          })
          // 去重
          dywdMap[item.id] = Array.from(new Set(dywdMap[item.id]))
        })
        console.log(dywdMap, 'dywdMap')
        this.tableInfoList.forEach(item => {
          item.info.forEach(i => {
            if (i.zdlx === 'INT' || i.zdlx === 'indOrDim') {
              const object = {
                zddm: i.zddm,
                dataSourceName: item.bm,
                cd: i.cd,
                kwk: i.kwk,
                sfwy: i.sfwy,
                sjlx: i.sjlx,
                ms: i.sm,
                bq: i.bq,
                tabmc: item.bm,
                pswd:
                  i.zdlx === 'indOrDim'
                    ? dywdMap[item.id].filter(e => e.zdlx !== 'indOrDim')
                    : dywdMap[item.id],
                tabid: item.id,
                zbmc: i.bt ? i.bm + '_' + i.bt : i.bm + '_' + i.zddm,
                zbymc: i.bt || i.zddm,
                sysjy: this.sysjy, // 所选指标域id
                zblx: '原子指标',
                lxbm: 'yz',
                lxbms: ['yz'],
                zdlx: i.sjlx.toUpperCase(),
                cjr: this.$store.state.user.username,
                deptIds: [],
                openRule: true
              }

              arr.push(object)
            }
          })
        })
        this.form.tableData = arr
        if (this.form.tableData.length === 0) {
          this.$message.warning('请选择度量')
          return
        }
      }
      this.active++
    },
    // 原子指标详情
    async getTableInfo () {
      this.loading = true
      this.tableInfoList = []
      const { data } = await this.$httpBi.indicatorAnagement.getTableInfo({
        id: this.ids.join()
      })
      const params = []
      console.log(11111, data)
      // 组装参数
      for (const item of data) {
        for (const i of item.info) {
          params.push({
            tableSource: i.id,
            fieldCode: i.zddm,
            fieldName: i.bt,
            lxbm: 'yz'
          })
          i.zdlx = i.zddm.toUpperCase() === 'ID' ? 'NULL' : 'ps'
          i.levelName = ''
          i.dimName = ''
          i.newCreate = false
          i.showPopover = false
        }
      }

      // 1. 获取匹配结果
      const matchResult = await this.matchDim(params)

      // 2. 融合数据
      for (const item of data) {
        for (const i of item.info) {
          // 找到匹配项
          const match = matchResult.find(
            m => m.fieldCode === i.zddm && m.tableSource === i.id
          )
          if (match) {
            this.$set(i, 'definitionCode', match.definitionCode || '')
            this.$set(i, 'levelCode', match.levelCode || '')
            this.$set(i, 'noCoverValues', match.noCoverValues || [])
            this.$set(i, 'dimName', match.dimName || '')
            this.$set(i, 'version', match.version || '')
            this.$set(i, 'noCoverShowCount', 5)
            if (match.definitionCode) {
              this.$set(this.dimLevelMap, match.definitionCode, match.dimLevels)
            }
          }
        }
      }

      this.tableInfoList = data
      this.loading = false

      console.log(this.tableInfoList, 'this.tableInfoList')
    },

    handleSelectionChange (val) {
      console.log(val, 'val')
      this.multipleSelection = val
      let newArr = [...val]
      console.log(newArr)
      this.form.tableData.forEach((item, index) => {
        const has = this.multipleSelection.find(i => i.zbymc === item.zbymc)
        if (has) {
          this.$set(this.form.tableData[index], 'openRule', true)
        } else {
          this.$set(this.form.tableData[index], 'openRule', false)
        }
      })
      console.log(this.form.tableData, 'this.form.tableData')
    },
    // 全选
    toggleSelection () {
      this.$refs.multipleTable.toggleAllSelection()
    },
    async handleSave () {
      console.log(this.multipleSelection)
      this.$refs.ruleForm.validate(async valid => {
        if (valid) {
          if (!this.multipleSelection.length) {
            this.$message.error('请选择指标')
            return
          }
          const { code } = await this.$httpBi.indicatorAnagement.saveYzzb(
            this.multipleSelection
          )
          if (code === 200) {
            this.$router.push('/ddsBi/indicatorAnagement')
            this.$message.success('保存成功')
          }
        }
      })
    },
    handleSaveTags (tags) {
      this.form.tableData[this.tabClickIndex].bq = tags
      this.dialogVisible = false
      this.inputBlur()
    },
    // 过滤维度名称
    filterWdName (id) {
      return this.wdList.filter(item => item.id === id)[0]?.wdmc || ''
    },
    foramtPswd (arr) {
      return arr.map(item => item.levelName || item.zdmc || item.wdzd).join(',')
    },

    formatZdlx (sjlx) {
      switch (sjlx) {
        case 'varchar':
          return 'ps'
        case 'int':
          return 'INT'
        case 'decimal':
          return 'DECIMAL'
        case 'date':
          return 'time'
        default:
          return 'NULL'
      }
    },
    // 匹配相似维度和层级
    async matchDim (params) {
      const { data } = await Request.api.paramPost(
        '/AtomIndicator/checkDimMatch',
        params
      )
      return data
    },
    // 创建自身维度
    async createSelfDim (row) {
      console.log(row)
      const { data } = await Request.api.paramPost('/DimManage/addDim', {
        dimDefinition: {
          categoryCode: 'wdlx_jcwd_1930198052817580032',
          dimName: row.bt || row.zddm,
          description: '',
          version: 'v1.0',
          tags: '基于自身创建维度',
          updateFrequency: 1,
          updateType: 0,
          categoryName: '基础维度'
        },
        createType: 0,
        dimLevels: [
          {
            level: 1,
            levelName: row.bt || row.zddm,
            sourceTable: row.id,
            sourceField: row.zddm,
            fieldValueType: row.sjlx
          }
        ],
        configs: []
      })
      await this.getDimTree()
      // 补充所有维度相关字段，便于后续编辑和回显
      this.$set(row, 'definitionCode', data.definitionCode)
      this.$set(row, 'dimName', data.dimName)
      this.$set(row, 'newCreate', true)
      this.$set(row, 'version', data.version)
      this.$set(row, 'categoryCode', 'wdlx_jcwd_1930198052817580032')
      this.$set(row, 'tags', '基于自身创建维度')
      this.$set(row, 'description', '')
      this.$set(row, 'updateFrequency', 1)
      this.$set(row, 'updateType', 0)
      this.getDimLevels(data.definitionCode, row)
    },
    // 删除维度
    async deleteDim (definitionCode) {
      console.log('删除维度')
      this.$message.success('已删除维度：' + definitionCode)
      return true
    },

    // 删除
    removeDim (row) {
      this.$confirm('是否删除当前维度树数据', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          Request.api
            .paramDel('/DimManage/deleteDimByCode', {
              code: row.definitionCode
            })
            .then(() => {
              this.$message.success('删除成功')
              this.$set(row, 'definitionCode', '')
              this.$set(row, 'dimName', '')
              this.$set(row, 'version', '')
              this.$set(row, 'newCreate', false)
              this.$set(row, 'levelCode', '')
              this.getDimTree()
            })
        })
        .catch(error => {
          console.error(error)
        })
    },
    editDim (row) {
      this.editRow = row
      // 针对自身创建维度，回显所有相关字段
      this.editDimForm = {
        definitionCode: row.definitionCode || '',
        dimName: row.dimName || '',
        version: row.version || '',
        categoryCode: row.categoryCode || '',
        levelName: row.levelName || '',
        tags: row.tags || '',
        description: row.description || '',
        updateFrequency:
          typeof row.updateFrequency === 'undefined' ? 1 : row.updateFrequency,
        updateType: typeof row.updateType === 'undefined' ? 0 : row.updateType
      }
      this.editDialogVisible = true
    },

    saveEditDim () {
      this.$refs.editDimForm.validate(async valid => {
        if (valid) {
          console.log(this.form, 'this.form')
          await Request.api.paramPost('/DimManage/editVersion', {
            ...this.editDimForm
          })
          this.$message.success('编辑成功')
          this.editDialogVisible = false
          // 同步表单数据到row
          if (this.editRow) {
            Object.assign(this.editRow, this.editDimForm)
            this.getDimLevels(this.editRow.definitionCode, this.editRow)
          }
          // this.$emit("refresh")
        } else {
          return false
        }
      })
    },

    onZdlxChange (row) {
      if (row.zdlx === 'time') {
        // 取消其他行的时间维度
        this.tableInfoList.forEach(item => {
          item.info.forEach(r => {
            if (r !== row && r.zdlx === 'time') {
              r.zdlx = '' // 或者重置为默认值
            }
          })
        })
      }
    },
    loadMoreNoCover (row) {
      if (!row.noCoverShowCount) {
        this.$set(row, 'noCoverShowCount', 10)
      } else {
        this.$set(row, 'noCoverShowCount', row.noCoverShowCount + 5)
      }
    },
    resetNoCoverShowCount (row) {
      this.$set(row, 'noCoverShowCount', 5)
    },
    async onLevelCodeChange (row) {
      await this.getNoCoverDimValues(row)
    }
  }
}
</script>

<style scoped lang="scss">
#project_frame .el-alert {
  margin-top: 0;
  margin-bottom: 24px;
}
.warm-reminder {
  position: relative;
  width: 100%;
  height: 48px;
  background: #fffbe6;
  border-radius: 2px;
  border: 1px solid #fff1b8;
  margin: 0 auto 24px;
  display: flex;
  padding: 12px 17px 0;
  box-sizing: border-box;
  .warm-reminder-content {
    margin-left: 8px;
    p {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #323233;
      line-height: 22px;
      height: 22px;
    }
  }
  .el-icon-close {
    position: absolute;
    right: 12px;
    top: 12px;
  }
}
.steps {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  .step-item {
    display: flex;
    align-items: center;
    &.active {
      .step-icon {
        background: #5b8ff9;
        border: 1px solid #5b8ff9;
        color: #fff;
      }
      .step-text {
        font-weight: 500;
        color: #222222;
      }
    }
    &.success {
      .step-text {
        height: 16px;
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
        line-height: 16px;
        margin-left: 8px;
        white-space: nowrap;
      }
      .step-line {
        background: #5b8ff9;
      }
    }
    .step-icon {
      width: 32px;
      height: 32px;
      background: #fff;
      border: 1px solid #dddddd;
      border-radius: 50%;
      line-height: 32px;
      text-align: center;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
    }
    .step-icon-success {
      font-size: 32px;
    }
    .step-text {
      flex: 1;
      height: 16px;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
      line-height: 16px;
      margin-left: 8px;
    }
    .step-line {
      margin: 0 16px;
      width: 192px;
      height: 1px;
      background: #dddddd;
    }
  }
}
::v-deep .search-input.el-input--small .el-input__inner {
  border: none !important;
  border-bottom: 1px solid #ccc !important;
  border-radius: 0 !important;
}
.one {
  width: 640px;
  margin: 20px auto;
  .checkbox-warp {
    height: calc(100vh - 420px);
    overflow: auto;
    margin-top: 24px;
  }
  ::v-deep .el-checkbox {
    margin-bottom: 24px;
  }
  //滚动条样式
  ::-webkit-scrollbar {
    width: 2px;
    height: 2px;
  }
  ::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #cbced1;
  }
  ::-webkit-scrollbar-track {
    border-radius: 3px;
    background-color: transparent;
  }
}

.two {
  height: calc(100vh - 350px);

  overflow: auto;
  //滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: #cbced1;
  }
  &::-webkit-scrollbar-track {
    border-radius: 2px;
    background-color: #ffffff;
  }

  .data-column-wrapper {
    margin: 20px auto;

    .data-column-title {
      font-size: 18px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #222222;
      margin-bottom: 16px;
    }
  }
}
.three {
  margin-top: 32px;
  padding-bottom: 40px;
  .allselect {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #222222;
  }
}
::v-deep .el-dialog__header {
  margin: 0 24px;
  padding: 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #edeff0;
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #222222;
}

::v-deep .el-dialog__body {
  padding: 20px 24px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}

::v-deep .el-row {
  margin-bottom: 0;
}

::v-deep .el-checkbox-group {
  display: flex;
  flex-direction: column;
}

.fotter-btn {
  position: absolute;
  bottom: 0px;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 64px;
  background: #ffffff;
  box-sizing: border-box;
  box-shadow: 0px -4px 12px 0px rgba(0, 0, 0, 0.06);
  z-index: 55;
}
.el-table {
  ::v-deep .el-table__body-wrapper::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  /*定义滚动条轨道 内阴影+圆角*/
  ::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
    border-radius: 2px;
    background-color: #ffffff;
  }
  /*定义滑块 内阴影+圆角*/
  ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: #cbced1;
  }
}
.popover-content {
  max-height: 150px;
  overflow-y: auto;
  // 可选：自定义滚动条样式
  &::-webkit-scrollbar {
    width: 4px;
    background: #f5f5f5;
  }
  &::-webkit-scrollbar-thumb {
    background: #cbced1;
    border-radius: 2px;
  }
}
</style>
<style>
#project_frame .el-table.atomIndicator td .cell,
#project_frame .el-table th .cell {
  padding-right: 16px;
}
</style>
