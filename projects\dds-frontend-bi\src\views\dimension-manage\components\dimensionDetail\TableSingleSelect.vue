<template>
  <el-select
    v-model="selectedValue"
    filterable
    remote
    :remote-method="handleRemote"
    :loading="loading"
    :loading-text="'加载中...'"
    :no-match-text="'无匹配数据'"
    :value-key="valueKey"
    collapse-tags
    @change="handleChange"
    ref="my-select"
    @scroll="handleScroll"
    style="width: 100%"
    v-bind="$attrs"
    :placeholder="placeholder"
    v-load-more="handleScroll"
    @remove-tag="removeTag"
    @visible-change="reverseArrow"
    v-suffix-click="handleSuffixClick"
  >
    <el-option
      :label="item.c"
      :value="item.id"
      v-for="item in options"
      :key="item.id"
    >
      <span style="float: left">{{ item ? item.c : '' }}</span>
      <span style="float: right; color: #8492a6; font-size: 13px">
        {{ item ? item.id : '' }}
      </span>
    </el-option>
    <template #suffix>123123</template>
  </el-select>
</template>

<script>
export default {
  name: 'ElSelectWithPagination',
  directives: {
    loadMore: {
      bind: (el, binding) => {
        const SELECTWRAP_DOM = el.querySelector(
          '.el-select-dropdown .el-select-dropdown__wrap'
        )
        SELECTWRAP_DOM.addEventListener('scroll', function () {
          const CONDITION =
            this.scrollHeight - this.scrollTop <= this.clientHeight
          if (CONDITION) {
            binding.value()
          }
        })
      }
    },
    // 自定义指令
    suffixClick: {
      bind: (el, binding) => {
        const suffixElement = el.querySelector('.el-input__suffix')
        if (suffixElement) {
          suffixElement.addEventListener('click', binding.value)
        }
      },
      unbind: (el, binding) => {
        const suffixElement = el.querySelector('.el-input__suffix')
        if (suffixElement) {
          suffixElement.removeEventListener('click', binding.value)
        }
      }
    }
  },
  props: {
    value: {
      type: [String, Number, Object],
      default: null
    },
    size: {
      type: Number,
      default: 20
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    fetchDataMethod: {
      type: Function,
      required: true
    },
    lxbm: {
      type: String,
      required: true
    },
    dimCol: {
      type: String,
      required: true
    },
    indCode: {
      type: String,
      required: true
    },
    valueKey: {
      type: String,
      default: ''
    },
    props: {
      type: Object,
      default () {
        return {
          label: 'label',
          value: 'value'
        }
      }
    }
  },
  data () {
    return {
      selectedValue: this.value, // 双向绑定的值
      options: [], // 存储下拉框选项
      loading: false, // 是否正在加载
      current: 1, // 当前分页页码,
      query: '',
      total: 0,
      isDropdownOpen: false // 下拉框是否打开
    }
  },
  watch: {
    wdzd: {
      handler () {
        this.options = [] // 清空列表
        this.current = 1 // 重置分页页码
        this.loading = true
        this.fetchData() // 调用父组件传入的fetchDataMethod方法
      }
    },
    // 监听父组件传入的value，保持双向绑定
    value: {
      handler (newVal) {
        this.selectedValue = newVal
      },
      immediate: true
    }
  },
  created () {
    console.log('????????')
    this.fetchData()
  },
  mounted () {
    let rulesDom = this.$refs['my-select'].$el.querySelector(
      '.el-input .el-input__suffix .el-input__suffix-inner .el-input__icon'
    ) // 找到dom
    rulesDom.classList.add('el-icon-arrow-up') // 对d
  },
  methods: {
    // 处理远程搜索的方法
    handleRemote (query) {
      this.options = [] // 清空列表
      this.current = 1 // 重置分页页码
      this.loading = true
      this.query = query
      this.fetchData() // 调用父组件传入的fetchDataMethod方法
    },

    // 加载分页数据
    async fetchData () {
      const { data } = await this.$httpBi.indicatorAnagement.getTableList({
        key: this.query,
        currentPage: this.current,
        pageSize: this.size
      })

      if (data.list) {
        this.options.push(...data.list)
        this.total = data.totalCount
        console.log(this.options, 'this.options')
      }
      this.loading = false
    },

    // 处理滚动事件
    handleScroll () {
      if (!this.loading && this.options.length < this.total) {
        this.current++
        this.fetchData()
      }
    },

    handleChange (val) {
      this.selectedValue = val
      this.$emit('input', this.selectedValue)
      this.$emit('change', this.selectedValue)
    },

    reverseArrow (flag) {
      this.isDropdownOpen = flag
      let rulesDom = this.$refs['my-select'].$el.querySelector(
        '.el-input .el-input__suffix .el-input__suffix-inner .el-input__icon'
      ) // 找到dom
      if (flag) {
        rulesDom.classList.add('is-reverse') // 对dom新增class
      } else {
        rulesDom.classList.remove('is-reverse') // 对dom新增class
      }
    },
    handleSuffixClick () {
      if (this.isDropdownOpen) {
        // this.$refs["my-select"].close() // 关闭下拉框
        console.log('//')
      } else {
        this.$refs['my-select'].toggleMenu() // 打开下拉框
      }
    }
  }
}
</script>

<style scoped lang="scss">
/* 可根据需求自定义样式 */

::v-deep .el-select__tags {
  display: flex;
  flex-wrap: nowrap;
}
</style>
